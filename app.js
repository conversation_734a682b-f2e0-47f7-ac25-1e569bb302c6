/* eslint-disable @typescript-eslint/no-var-requires */
const express = require("express");
const next = require("next");
const fs = require("fs");
const path = require("path");

// All Environments
const dev = process.env.APP_ENV.trim() === "dev";

// Initialize the Next.js app
const app = next({ dev });
const handle = app.getRequestHandler(); // Get Next.js request handler

// Function to check if a file exists
function fileExists(filePath) {
  try {
    if (fs.existsSync(filePath + ".js")) {
      return true; // This one is for pages root folder .js files.
    } else if (fs.existsSync(filePath + ".html")) {
      return true; // This one is for .next/server/pages standalone production .html code.
    }
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
}

// Array to store URL prefixes and their corresponding directories
const routes = [
  { prefix: "/libs/", directory: "public/conf/libs" },
  { prefix: "/scripts/", directory: "public/conf/scripts" },
  { prefix: "/explore/", directory: "/public/conf/explore/" },
  { prefix: "/sounds/", directory: "public/conf/sounds" },
  {
    prefix: "/connection_optimization/",
    directory: "public/conf/connection_optimization",
  },
  { prefix: "/fonts/", directory: "public/conf/fonts" },
  { prefix: "/lang/", directory: "public/conf/lang" },
  { prefix: "/languages/", directory: "public/languages" },
  { prefix: "/images/", directory: "public/conf/images" },
  { prefix: "/static/", directory: "public/conf/static" },
  { prefix: "/manifest.json", directory: "public/conf/manifest.json" },
  { prefix: "/pwa-worker.js", directory: "public/conf/pwa-worker.js" },
  { prefix: "/ads.txt", directory: "public/conf/ads.txt" },
  { prefix: "/favicon.ico", directory: "public/img/favicon.ico" },
  { prefix: "/logging_config.js", directory: "public/conf/logging_config.js" },
  { prefix: "/external_api.js", directory: "public/conf/external_api.js" },
  { prefix: "/title.html", directory: "public/conf/title.html" },
  { prefix: "/base.html", directory: "public/conf/base.html" },
  { prefix: "/head.html", directory: "public/conf/head.html" },
  { prefix: "/plugin.head.html", directory: "public/conf/plugin.head.html" },
  // Add more routes as needed
];

app.prepare().then(() => {
  const server = express();

  // Serve static files from the 'public' folder
  server.use(express.static(path.join(__dirname, "public")));

  // Custom routes or proxying logic before passing to Next.js
  if (!dev) {
    server.get("/config.js", (req, res) => {
      res.sendFile("/config/config.js");
    });

    server.get("/interface_config.js", (req, res) => {
      res.sendFile("/config/interface_config.js");
    });
  }

  // Serve additional static or public files
  else {
    server.get("/config.js", (req, res) => {
      res.sendFile(path.join(__dirname, "public", "conf", "config.js"));
    });

    server.get("/interface_config.js", (req, res) => {
      res.sendFile(
        path.join(__dirname, "public", "conf", "interface_config.js")
      );
    });
  }

  // Handle routes that need to redirect to external URLs or Next.js routes
  // server.get('/redirect-to-external-url', (req, res) => {
  //   res.redirect('https://example.com');
  // });

  server.get("/explore/hospitals.html", (req, res) => {
    res.writeHead(301, { Location: "/solutions/industries/healthcare" });
    res.end();
  });

  server.get("/who-we-are.html", (req, res) => {
    res.writeHead(301, { Location: "/whoweare" });
    res.end();
  });

  server.get("/privacy-policy.html", (req, res) => {
    res.writeHead(301, { Location: "/privacypolicy" });
    res.end();
  });

  server.get("/terms-conditions.html", (req, res) => {
    res.writeHead(301, { Location: "/termsandconditions" });
    res.end();
  });

  server.get("/disclaimer.html", (req, res) => {
    res.writeHead(301, { Location: "/disclaimer" });
    res.end();
  });

  server.get("/refund-and-cancellation.html", (req, res) => {
    res.writeHead(301, { Location: "/refundandcancellation" });
    res.end();
  });

  server.get("/contact.html", (req, res) => {
    res.writeHead(301, { Location: "/contact" });
    res.end();
  });

  server.get("/news.html", (req, res) => {
    res.writeHead(301, { Location: "/inthenews" });
    res.end();
  });

  server.get("/FAQ.html ", (req, res) => {
    res.writeHead(301, { Location: "/faqs" });
    res.end();
  });

  server.get("/sdks ", (req, res) => {
    res.writeHead(301, { Location: "/developers/sdks" });
    res.end();
  });

  server.get("/login", (req, res) => {
    res.redirect("https://portal.meethour.io");
  });

  // Serve files for each route in the routes array
  routes.forEach(({ prefix, directory }) => {
    server.get(`${prefix}*`, (req, res) => {
      // Extract the requested file path from the URL
      let requestedFilePath = req.url.substring(prefix.length);
      if (req.url.includes("?")) {
        // Remove query parameters if present
        requestedFilePath = requestedFilePath.split("?")[0];
      }

      // Construct the full file path including URL parameters
      const filePath = path.join(__dirname, directory, requestedFilePath);

      // Check if the requested file exists
      if (fileExists(filePath)) {
        // Serve the file
        res.sendFile(filePath);
      } else {
        // File not found, return 404
        return handle(req, res);
      }
    });
  });

  // Handle Next.js routes
  server.get("/_next/*", (req, res) => {
    handle(req, res);
  });

  server.get("*", (req, res) => {
    // Get the requested URL
    let requestedUrl = req.url;

    if (req.url.includes("?")) {
      // Remove query parameters if present
      requestedUrl = requestedUrl.split("?")[0];
    }

    // Check if the requested URL exists as a file
    const filePath = path.join(
      __dirname,
      dev === false ? ".next/server/pages" : "pages",
      requestedUrl
    );

    // Check if the requested URL is the home page with URL parameters access_token
    if (
      requestedUrl === "/" ||
      fileExists(filePath) ||
      (req.query &&
        Object.keys(req.query).length > 0 &&
        req.query.access_token &&
        req.query.access_token !== "" &&
        requestedUrl !== "/joinmeeting")
    ) {
      // Serve the requested URL using Next.js
      return handle(req, res);
    }

    let slashCount = 0;

    if (req.url.includes("/")) {
      // counting the slashes
      slashCount = req.url.split("/").length;
    }

    // Serve the index.html from public/conf folder
    const indexPath = path.join(__dirname, "public", "conf", "index.html");
    if (
      (fileExists(indexPath) && slashCount === 2) ||
      (fileExists(indexPath) && slashCount === 3 && req.url.endsWith("/"))
    ) {
      const indexHtml = fs.readFileSync(indexPath, "utf-8");
      res.setHeader("Content-Type", "text/html");
      res.send(indexHtml);
    } else {
      // If index.html doesn't exist, return a 404
      handle(req, res);
    }
  });

  const currentPort = process.env.PORT
    ? process.env.PORT
    : dev === false
    ? 3000
    : 3000;

  server.listen(currentPort, (err) => {
    if (err) {
      throw err;
    }
    console.log(`==> Ready on http://localhost:${currentPort}`);
  });
});
