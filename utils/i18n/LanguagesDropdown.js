/* @flow */
import React, { useEffect, useState } from "react";
import { DEFAULT_LANGUAGE, i18next, LANGUAGES } from ".";
import { useTranslation } from "react-i18next";
import Image from "next/image";
import Select from "react-select";
import { CustomerLanguagePrefernce } from "../apiCalls";
import { getAccessToken } from "../api";
import { useSelector } from "react-redux";

/**
 * LanguageSelector component for choosing the app's language.
 *
 * @param {Object} props
 * @param {boolean} props.isMobileView - Determines if the view is in mobile mode.
 * @returns {ReactElement}
 */
const LanguageSelector = ({ isMobileView = false, config }) => {
  const { t } = useTranslation();
  const userDetailsRedux = useSelector((state) => state.user.userDetails);
  const [selectedLanguage, setSelectedLanguage] = useState(
    i18next.language || DEFAULT_LANGUAGE
  );

  // Auto-sync language from userDetails
  useEffect(() => {
    const preferredLang = userDetailsRedux?.strip_details?.language_preference;

    if (!preferredLang) {
      return;
    }

    // Convert API language format to internal format
    let formattedLang = preferredLang;
    if (preferredLang === "zh-TW") {
      formattedLang = "zhTW";
    } else if (preferredLang === "zh") {
      formattedLang = "zhCN";
    }

    if (
      formattedLang &&
      formattedLang !== selectedLanguage &&
      i18next.language !== formattedLang
    ) {
      i18next
        .changeLanguage(formattedLang)
        .then(() => {
          setSelectedLanguage(formattedLang);
        })
        .catch((err) => {
          console.error("Error syncing language from userDetailsRedux:", err);
        });
    }
  }, [userDetailsRedux]);

  /**
   * Handles language selection.
   *
   * @param {string} newLanguage - The selected language code.
   */
  const onSelect = async (selectedOption) => {
    const newLanguage = selectedOption.value;

    if (selectedLanguage !== newLanguage) {
      try {
        await i18next.changeLanguage(newLanguage);
        setSelectedLanguage(newLanguage);

        let apiLanguage = newLanguage;
        if (newLanguage === "zhTW") {
          apiLanguage = "zh-TW";
        } else if (newLanguage === "zhCN") {
          apiLanguage = "zh-CN";
        }

        // Only call the API if user has access token
        const accessToken = getAccessToken();
        if (accessToken) {
          await CustomerLanguagePrefernce(config, apiLanguage);
        }
      } catch (error) {
        console.error("Error changing language or calling API:", error);
      }
    }
  };

  /**
   * Creates the dropdown menu.
   *
   * @param {Object} options - Options for the dropdown menu.
   * @returns {ReactElement}
   */
  const createDropdown = ({ items }) => {
    return (
      <div
        className={`language-dropdown-menu d-flex align-items-center ${
          isMobileView
            ? "d-flex justify-content-between align-items-center mw-100 pr-1"
            : ""
        }`}
        style={{ minWidth: "150px" }}
      >
        {!isMobileView && (
          <Image
            alt="globe"
            height={24}
            src="/images/globe_white.svg"
            width={24}
          />
        )}
        <Select
          components={{
            DropdownIndicator: () => null,
            IndicatorSeparator: () => null,
          }}
          noOptionsMessage={({ inputValue }) =>
            !inputValue
              ? t("layout.header.noLanguages")
              : t("layout.header.noLanguagesFound")
          }
          onChange={onSelect}
          options={items}
          styles={{
            container: (prvStyles) => ({
              ...prvStyles,
              width: "100%",
            }),
            control: (prvStyles) => ({
              ...prvStyles,
              backgroundColor: "transparent",
              border: "none",
              boxShadow: "none",
              color: "white",
            }),
            input: (prvStyles) => ({
              ...prvStyles,
              color: "white",
            }),
            singleValue: (prvStyles) => ({
              ...prvStyles,
              color: "white",
              whiteSpace: "unset",
            }),
            menu: (prvStyles) => ({
              ...prvStyles,
              zIndex: 9999,
              minWidth: "160px",
            }),
          }}
          value={{
            label: t(`languages:${selectedLanguage}`),
            value: selectedLanguage,
          }}
        />
        {isMobileView && (
          <Image
            alt="globe"
            height={24}
            src="/images/globe_black.svg"
            width={24}
          />
        )}
      </div>
    );
  };

  const items = LANGUAGES.map((l) => ({
    label: t(`languages:${l}`),
    value: l,
  }));

  return createDropdown({ items });
};

export default LanguageSelector;
