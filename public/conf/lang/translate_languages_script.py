# some language translations will not happen . i mentioned that languages below
# {
#     "enGB": "English (UK)",
#     "esUS": "Spanish (Latin America)",
#     "frCA": "French (Canadian)",
#     "ptBR": "Portuguese (Brazil)",
#     "zhCN": "Chinese (China)",
# 	"zhTW": "Chinese (Taiwan)",
# }

# install deep_translator with python3.12

import json
import os
from deep_translator import GoogleTranslator # type: ignore
import copy  # Import copy module for creating deep copies

# Load the JSON data containing the nested words to be translated
with open('languages.json', 'r', encoding='utf-8') as file:
    original_words_data = json.load(file)

# Load language codes from codes.json
with open('codes_all_1.json', 'r', encoding='utf-8') as file:
    language_codes = json.load(file)

# Directory to store translated JSON files
os.makedirs("translations", exist_ok=True)

# Recursive function to count total words in nested JSON
def count_words(data):
    if isinstance(data, dict):
        return sum(count_words(value) for value in data.values())
    elif isinstance(data, list):
        return sum(count_words(item) for item in data)
    elif isinstance(data, str):
        return 1  # Count each string as one word
    else:
        return 0

# Recursive function to translate each text value in the nested JSON
def translate_nested(data, translator, word_counter, total_words, lang_code):
    if isinstance(data, dict):
        for key, value in data.items():
            translated_value = translate_nested(value, translator, word_counter, total_words, lang_code)
            data[key] = translated_value
    elif isinstance(data, list):
        for index in range(len(data)):
            translated_value = translate_nested(data[index], translator, word_counter, total_words, lang_code)
            data[index] = translated_value
    elif isinstance(data, str):
        translated_value = translator.translate(data)
        word_counter[0] += 1  # Increment the word counter
        # Print progress after each word is translated
        print_progress(word_counter[0], total_words, lang_code)
        return translated_value
    return data

def print_progress(current_count, total, lang_code):
    # Calculate the percentage completion
    percentage_complete = (current_count / total) * 100
    percentage_complete = int(percentage_complete)  # Convert to integer for comparison

    # Track previous percentage in a mutable way
    if lang_code not in prev_percentages:
        prev_percentages[lang_code] = 0

    # Ensure to print only if there's a significant change in percentage (minimum 5% change)
    if percentage_complete >= 100:
        print(f"100% is done for '{lang_code}' language.")
        prev_percentages[lang_code] = 100  # Update previous percentage to 100
    elif percentage_complete > prev_percentages[lang_code] and (percentage_complete - prev_percentages[lang_code]) >= 5:
        print(f"{percentage_complete}% is done for '{lang_code}' language.")
        prev_percentages[lang_code] = percentage_complete  # Update previous percentage

# Initialize a dictionary to keep track of previous percentages for each language
prev_percentages = {}

# Count total words to translate
total_words = count_words(original_words_data)

# Translate each word in the JSON data and save to separate files
completed_codes = 0  # Counter for completed language codes

for lang_code, lang in language_codes.items():
    # Create a deep copy of the original data to ensure each language starts fresh
    words_data = copy.deepcopy(original_words_data)

    try:
        translator = GoogleTranslator(source='en', target=lang_code)
        word_counter = [0]  # Mutable counter to keep track of translated words
        
        translate_nested(words_data, translator, word_counter, total_words, lang_code)
        filename = f"languages-{lang_code}.json"

        # Save the translated data to a file
        with open(filename, "w", encoding="utf-8") as file:
            json.dump(words_data, file, ensure_ascii=False, indent=4)

        completed_codes += 1  # Increment the count of completed language codes
        overall_percentage = (completed_codes / len(language_codes)) * 100
        print(f"Translation for '{lang_code}' saved to {filename}. Overall completion: {int(overall_percentage)}%")

    except Exception as e:
        print(f"Skipping '{lang_code}': {e}")
        continue  # Skip to the next language code if there's an error

print("Translation completed for all valid language codes.")
