# some language translations will not happen . i mentioned that languages below
# {
#     "enGB": "English (UK)",
#     "esUS": "Spanish (Latin America)",
#     "frCA": "French (Canadian)",
#     "ptBR": "Portuguese (Brazil)",
#     "zhCN": "Chinese (China)",
# 	"zhTW": "Chinese (Taiwan)",
# }

# install deep_translator with python3.12

import json
import os
from deep_translator import GoogleTranslator # type: ignore
import copy

# Load the JSON data containing the nested words to be translated
with open('main.json', 'r', encoding='utf-8') as file:
    original_words_data = json.load(file)

# Load language codes from codes.json
with open('codes_all.json', 'r', encoding='utf-8') as file:
    language_codes = json.load(file)

# Directory to store translated JSON files
os.makedirs("translations", exist_ok=True)

# Recursive function to count total words in nested JSON
def count_words(data):
    if isinstance(data, dict):
        return sum(count_words(value) for value in data.values())
    elif isinstance(data, list):
        return sum(count_words(item) for item in data)
    elif isinstance(data, str):
        return 1  # Count each string as one word
    else:
        return 0

# Recursive function to translate only missing text values in the nested JSON
def translate_missing(data, translated_data, translator, word_counter, total_words, lang_code):
    if isinstance(data, dict):
        for key, value in data.items():
            if key not in translated_data:
                translated_data[key] = {} if isinstance(value, dict) else [] if isinstance(value, list) else None
            translated_value = translate_missing(value, translated_data[key], translator, word_counter, total_words, lang_code)
            translated_data[key] = translated_value
    elif isinstance(data, list):
        for index in range(len(data)):
            if index >= len(translated_data):
                translated_data.append(None)
            translated_value = translate_missing(data[index], translated_data[index], translator, word_counter, total_words, lang_code)
            translated_data[index] = translated_value
    elif isinstance(data, str):
        if not translated_data:  # Only translate if the target data is missing
            translated_value = translator.translate(data)
            word_counter[0] += 1  # Increment the word counter
            print_progress(word_counter[0], total_words, lang_code)
            return translated_value
        return translated_data  # If already translated, return the existing translation
    return translated_data

def print_progress(current_count, total, lang_code):
    # Calculate the percentage completion
    percentage_complete = (current_count / total) * 100
    percentage_complete = int(percentage_complete)

    # Track previous percentage for each language
    if lang_code not in prev_percentages:
        prev_percentages[lang_code] = 0

    # Print progress only if there's an increment of at least 5% or if it's 100%
    if percentage_complete >= 100:
        print(f"100% is done for '{lang_code}' language.")
        prev_percentages[lang_code] = 100
    elif percentage_complete > prev_percentages[lang_code] and (percentage_complete - prev_percentages[lang_code]) >= 5:
        print(f"{percentage_complete}% is done for '{lang_code}' language.")
        prev_percentages[lang_code] = percentage_complete  # Update previous percentage

# Initialize a dictionary to keep track of previous percentages for each language
prev_percentages = {}

# Count and print the total words in the original file
total_words_original = count_words(original_words_data)
print(f"Total words in the original file: {total_words_original}")

# Translate only missing words for each language and save to files
completed_codes = 0

for lang_code, lang in language_codes.items():
    words_data = copy.deepcopy(original_words_data)
    translated_data = {}

    # Load existing translation if available
    filename = f"main-{lang_code}.json"
    if os.path.exists(filename):
        with open(filename, "r", encoding="utf-8") as file:
            translated_data = json.load(file)
        total_words_translated = count_words(translated_data)
        print(f"Total words in the existing '{lang_code}' translated file: {total_words_translated}")
    else:
        print(f"No existing translation found for '{lang_code}'. Starting from scratch.")

    try:
        translator = GoogleTranslator(source='en', target=lang_code)
        word_counter = [0]  # Mutable counter to keep track of translated words
        
        translate_missing(words_data, translated_data, translator, word_counter, total_words_original, lang_code)

        # Save the updated translated data to a file
        with open(filename, "w", encoding="utf-8") as file:
            json.dump(translated_data, file, ensure_ascii=False, indent=4, sort_keys=True)

        completed_codes += 1
        overall_percentage = (completed_codes / len(language_codes)) * 100
        print(f"Translation for '{lang_code}' saved to {filename}. Overall completion: {int(overall_percentage)}%")

    except Exception as e:
        print(f"Skipping '{lang_code}': {e}")
        continue

print("Translation completed for all valid language codes.")
