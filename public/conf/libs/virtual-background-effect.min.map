{"version": 3, "file": "virtual-background-effect.min.js", "mappings": "kCAAa,SAASA,EAAEA,EAAEC,GAAG,IAAIC,EAAE,OAAM,EAAG,MAAMC,EAAEH,EAAEI,OAAO,IAAIC,EAAEC,EAAEC,IAAIJ,GAAG,GAAG,MAAME,EAAE,CAAC,IAAIA,EAAEG,EAAEC,SAASN,KAAKF,EAAE,IAAI,IAAIO,EAAEE,SAAS,IAAIF,EAAEG,OAAOR,IAAIS,QAAQ,IAAI,CAAC,MAAMZ,GAAGK,GAAE,CAAE,CAACC,EAAEO,IAAIV,EAAEE,EAAE,CAAC,OAAOA,CAAC,CAAC,MAAMG,EAAEM,YAAYb,EAAE,mBAAAc,EAAAC,UAAAC,OAAIjB,EAAC,IAAAkB,MAAAH,GAAAI,EAAA,EAAAA,EAAAJ,EAAAI,IAADnB,EAACmB,GAAAH,UAAAG,GAAA,OAAGC,WAAWC,GAAG,EAAE,GAAG,IAAI,IAAI,EAAE,EAAE,EAAE,KAAKrB,EAAE,EAACG,EAAE,mBAAAmB,EAAAN,UAAAC,OAAIjB,EAAC,IAAAkB,MAAAI,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAADvB,EAACuB,GAAAP,UAAAO,GAAA,OAAGC,YAAYH,GAAG,WAAW,KAAKrB,EAAE,EAACK,EAAE,mBAAAoB,EAAAT,UAAAC,OAAIjB,EAAC,IAAAkB,MAAAO,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAD1B,EAAC0B,GAAAV,UAAAU,GAAA,OAAGzB,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,KAAKD,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAC2B,EAAE,mBAAAC,EAAAZ,UAAAC,OAAIjB,EAAC,IAAAkB,MAAAU,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAD7B,EAAC6B,GAAAb,UAAAa,GAAA,OAAGC,YAAYT,GAAG,MAAM,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAKrB,EAAE,EAAC+B,EAAE,mBAAAC,EAAAhB,UAAAC,OAAIjB,EAAC,IAAAkB,MAAAc,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAADjC,EAACiC,GAAAjB,UAAAiB,GAAA,OAAG9B,EAAE,WAAW,YAAYH,EAAE,SAAS,IAAI,EAACkC,EAAE,mBAAAC,EAAAnB,UAAAC,OAAIjB,EAAC,IAAAkB,MAAAiB,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAADpC,EAACoC,GAAApB,UAAAoB,GAAA,OAAGT,KAAK3B,EAAE,KAAK,KAAK,MAAM,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,EAACqC,EAAE,mBAAAC,EAAAtB,UAAAC,OAAIjB,EAAC,IAAAkB,MAAAoB,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAADvC,EAACuC,GAAAvB,UAAAuB,GAAA,OAAGZ,KAAK3B,EAAE,KAAK,MAAM,MAAM,IAAI,IAAI,EAAE,EAACE,EAAE,iBAAiBM,EAAEgC,EAAExC,GAAGE,GAAG,mBAAmBF,EAAEM,EAAE,IAAImC,QAAQC,EAAEvC,EAAE,WAAW,SAAS,UAAU,UAAU,UAAU,WAAW,UAAU,WAAW,SAAS,SAAS,KAAKwC,EAAEN,EAAE,IAAI,EAAE,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK,MAAMO,EAAEP,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,KAAK,MAAMQ,EAAE5C,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE,EAAE,GAAG6C,EAAEhB,YAAYT,GAAG,MAAM,MAAM,EAAE,EAAE,KAAK,MAAM,IAAI,MAAM,IAAI,EAAE,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,IAAI,IAAI,GAAG0B,EAAEb,EAAE,KAAK,KAAK,MAAM,EAAE,EAAE,KAAKc,EAAEd,EAAE,KAAK,KAAK,MAAM,OAAOe,EAAElB,EAAE,UAAU,UAAU,UAAU,WAAW,UAAUmB,EAAE7C,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG8C,EAAEpB,EAAE,SAAS,QAAQ,SAAS,QAAQ,UAAU,YAAYqB,EAAE/C,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,IAAIgD,EAAOzC,QAAQ,CAAC0C,QAAQ,eAAC9C,EAACQ,UAAAC,OAAA,QAAAsC,IAAAvC,UAAA,GAAAA,UAAA,GAAC,EAAC,OAAGhB,EAAEwB,YAAYH,GAAG,WAAWb,GAAG,EAAC,oBAAIgD,GAAmB,OAAOhB,EAAEhC,EAAEiD,qBAAqB,EAAEC,QAAQ,CAAC,UAAIC,GAAS,OAAO3D,EAAE0C,GAAE,EAAG,EAAE,QAAIkB,GAAO,OAAO5D,EAAE2C,EAAE,EAAE,cAAIkB,GAAa,OAAO7D,EAAE4C,EAAE,EAAE,iBAAIkB,GAAgB,OAAO9D,EAAE6C,EAAE,EAAE,cAAIkB,GAAa,OAAO/D,EAAE8C,EAAE,EAAE,uBAAIkB,GAAsB,OAAOhE,EAAE+C,EAAE,EAAE,kBAAIkB,GAAiB,OAAOjE,EAAEgD,EAAE,EAAE,YAAIkB,GAAW,OAAOlE,EAAEiD,EAAE,EAAE,WAAIkB,GAAU,OAAOnE,EAAEkD,EAAE,EAAE,QAAIkB,GAAO,OAAOpE,EAAEmD,EAAE,EAAE,cAAIkB,GAAa,OAAOrE,EAAEoD,EAAE,EAAE,kBAAIkB,GAAiB,OAAO9B,EAAEhC,EAAE+D,OAAOC,KAAK,EAAE,kBAAIC,GAAiB,OAAOjC,EAAEhC,EAAEkE,SAAS,G,oBCE/yDC,EADFC,GAEqCD,GADnCA,EAAiC,oBAAbE,UAA4BA,SAASC,cAAgBD,SAASC,cAAcC,SAAMxB,I,gFAG5G,SAASqB,GAGT,IAAqFI,EAAoBC,EAArGtE,OAAuC,KAFzCiE,EAAyBA,GAA0B,CAAC,GAECA,EAAuB,CAAC,EAA6CjE,EAAc,MAAE,IAAIuE,SAAQ,SAASC,EAAQC,GAAQJ,EAAoBG,EAAQF,EAAmBG,CAAM,IAAG,IAA2BC,EAAvBC,EAAgB,CAAC,EAAU,IAAID,KAAO1E,EAAWA,EAAO4E,eAAeF,KAAMC,EAAgBD,GAAK1E,EAAO0E,IAAM,IAAsKG,EAA8BC,EAA3FC,EAA6BC,EAAlIC,EAAW,GAAOC,EAAY,iBAAqBC,EAAM,SAASC,EAAOC,GAAS,MAAMA,CAAO,EAA4HN,EAAmC,iBAATO,OAAkBN,EAA6C,mBAAhBO,cAA2BV,EAAqC,iBAAVW,SAA8C,iBAAnBA,QAAQC,UAAoD,iBAAxBD,QAAQC,SAASC,KAAgBZ,GAAsBC,IAAqBF,IAAsBG,EAAsB,IAA4JW,EAAMC,EAAUC,EAA8BC,EAAWC,EAAjNC,EAAgB,GAA6MnB,GAA+CmB,EAAvBhB,EAAuCiB,EAAAA,KAAAA,QAAwBD,GAAiB,IAAyBE,KAAcP,EAAM,SAAoBQ,EAASC,GAAuH,OAA3GN,IAAOA,EAAOG,EAAQ,MAAUF,IAASA,EAASE,EAAQ,MAAQE,EAASJ,EAAoB,UAAEI,GAAiBL,EAAqB,aAAEK,EAASC,EAAO,KAAK,OAAO,EAAEP,EAAW,SAAoBM,GAAU,IAAIE,EAAIV,EAAMQ,GAAS,GAAiE,OAAvDE,EAAI5G,SAAQ4G,EAAI,IAAI5F,WAAW4F,IAAKC,EAAOD,EAAI5G,QAAe4G,CAAG,EAAKb,QAAc,KAAElF,OAAO,IAAG4E,EAAYM,QAAc,KAAE,GAAGe,QAAQ,MAAM,MAAKtB,EAAWO,QAAc,KAAEgB,MAAM,GAAGhB,QAAY,GAAE,qBAAoB,SAASiB,GAAI,KAAKA,aAAcC,IAAa,MAAMD,CAAG,IAAGjB,QAAY,GAAE,qBAAqBmB,GAAOxB,EAAM,SAASC,GAAQI,QAAc,KAAEJ,EAAO,EAAEpF,EAAgB,QAAE,WAAW,MAAM,4BAA4B,GAAU8E,GAAsC,oBAAN8B,OAAmBjB,EAAM,SAAoBpG,GAAG,OAAOqH,KAAKrH,EAAE,GAAEsG,EAAW,SAAoBtG,GAAG,IAAIsH,EAAK,MAAuB,mBAAbC,WAAgC,IAAIrG,WAAWqG,WAAWvH,KAA0B+G,EAAqB,iBAA3CO,EAAKD,KAAKrH,EAAE,YAAgDsH,EAAI,EAAwB,oBAAZE,WAAyB9B,EAAW8B,gBAAqC,IAAX1G,YAAwB4E,EAAW5E,WAA2B,mBAAP2G,OAAmB7B,EAAM,SAASC,GAAQ4B,KAAK5B,EAAO,GAAoB,oBAAR6B,QAAyC,oBAAVC,UAAsBA,QAAQ,CAAC,GAAEA,QAAQC,IAAIF,MAAMC,QAAQE,KAAKF,QAAQG,MAAwB,oBAAXC,SAAuBA,SAASL,SAAelC,GAAoBC,KAA0BA,EAAuBgB,EAAgBuB,KAAKC,SAASC,KAA+B,oBAAXvD,UAAwBA,SAASC,gBAAe6B,EAAgB9B,SAASC,cAAcC,KAAOJ,IAAYgC,EAAgBhC,GAAoDgC,EAAH,IAAnCA,EAAgB0B,QAAQ,SAA8B1B,EAAgB2B,OAAO,EAAE3B,EAAgB4B,YAAY,KAAK,GAAwB,GAAIjC,EAAM,SAASkC,GAAK,IAAIC,EAAI,IAAIC,eAAwD,OAAzCD,EAAIE,KAAK,MAAMH,GAAI,GAAOC,EAAIG,KAAK,MAAaH,EAAII,YAAY,EAAKlD,IAAuBa,EAAW,SAASgC,GAAK,IAAIC,EAAI,IAAIC,eAAuF,OAAxED,EAAIE,KAAK,MAAMH,GAAI,GAAOC,EAAIK,aAAa,cAAcL,EAAIG,KAAK,MAAa,IAAIxH,WAAWqH,EAAIM,SAAS,GAAExC,EAAU,SAASiC,EAAIQ,EAAOC,GAAS,IAAIR,EAAI,IAAIC,eAAeD,EAAIE,KAAK,MAAMH,GAAI,GAAMC,EAAIK,aAAa,cAAcL,EAAIO,OAAO,WAA0B,KAAZP,EAAI1C,QAAyB,GAAZ0C,EAAI1C,QAAW0C,EAAIM,SAAUC,EAAOP,EAAIM,UAAiBE,GAAS,EAAER,EAAIQ,QAAQA,EAAQR,EAAIG,KAAK,KAAK,GAA6D,IAAiYM,EAAuEC,EAA8JC,EAAlmBC,EAAI1I,EAAc,OAAGkH,QAAQC,IAAIwB,KAAKzB,SAAa0B,EAAI5I,EAAiB,UAAGkH,QAAQE,KAAKuB,KAAKzB,SAAS,IAAIxC,KAAOC,EAAoBA,EAAgBC,eAAeF,KAAM1E,EAAO0E,GAAKC,EAAgBD,IAAMC,EAAgB,KAAQ3E,EAAkB,YAAEiF,EAAWjF,EAAkB,WAAKA,EAAoB,cAAEkF,EAAYlF,EAAoB,aAAKA,EAAa,OAAEmF,EAAMnF,EAAa,MAAoBA,EAAmB,aAAEuI,EAAWvI,EAAmB,YAAuBA,EAAsB,gBAAEwI,EAAcxI,EAAsB,eAA0B,iBAAdG,aAAwBwG,EAAM,mCAAkD,IAAIkC,GAAM,EAAqB,SAASvC,EAAOwC,EAAUC,GAAUD,GAAWnC,EAAM,qBAAqBoC,EAAM,CAAC,IAAykCtJ,EAAOuJ,EAAMC,EAAsBC,EAAxmCC,EAAiC,oBAAdC,YAA0B,IAAIA,YAAY,aAAQxG,EAAU,SAASyG,EAAkBC,EAAKC,EAAIC,GAA6D,IAA7C,IAAIC,EAAOF,EAAIC,EAAmBE,EAAOH,EAAUD,EAAKI,MAAWA,GAAQD,MAAUC,EAAO,GAAGA,EAAOH,EAAI,IAAID,EAAKK,UAAUR,EAAa,OAAOA,EAAYS,OAAON,EAAKK,SAASJ,EAAIG,IAAyB,IAAX,IAAIG,EAAI,GAASN,EAAIG,GAAO,CAAC,IAAII,EAAGR,EAAKC,KAAO,GAAQ,IAAHO,EAAL,CAAoD,IAAIC,EAAe,GAAZT,EAAKC,KAAU,GAAa,MAAN,IAAHO,GAAJ,CAAmE,IAAIE,EAAe,GAAZV,EAAKC,KAAmG,IAAvEO,EAAL,MAAN,IAAHA,IAAqB,GAAHA,IAAQ,GAAGC,GAAI,EAAEC,GAAe,EAAHF,IAAO,GAAGC,GAAI,GAAGC,GAAI,EAAc,GAAZV,EAAKC,MAAgB,MAAOM,GAAKI,OAAOC,aAAaJ,OAAQ,CAAC,IAAIK,EAAGL,EAAG,MAAMD,GAAKI,OAAOC,aAAa,MAAMC,GAAI,GAAG,MAAS,KAAHA,EAAQ,CAAnO,MAAhDN,GAAKI,OAAOC,cAAiB,GAAHJ,IAAQ,EAAEC,EAA7E,MAArCF,GAAKI,OAAOC,aAAaJ,EAAyU,CAAE,OAAOD,CAAG,CAAoZ,SAASO,EAA2BC,GAAK5K,EAAO4K,EAAIrK,EAAc,MAAEgJ,EAAM,IAAIsB,UAAUD,GAAKrK,EAAe,OAAS,IAAIuK,WAAWF,GAAKrK,EAAe,OAAEkJ,EAAO,IAAIsB,WAAWH,GAAKrK,EAAe,OAAEiJ,EAAO,IAAIxI,WAAW4J,GAAKrK,EAAgB,QAAU,IAAImB,YAAYkJ,GAAKrK,EAAgB,QAAU,IAAIa,YAAYwJ,GAAKrK,EAAgB,QAAU,IAAIyK,aAAaJ,GAAKrK,EAAgB,QAAU,IAAI0K,aAAaL,EAAI,CAAoBrK,EAAuB,eAA1C,IAA0D2K,EAAcC,EAAa,GAAOC,EAAW,GAAOC,EAAW,GAAOC,EAAc,GAAwDF,EAAWG,KAAK,CAACC,KAAK,WAAWC,IAAoB,IAAutB,IAAIC,EAAgB,EAAMC,EAAqB,KAASC,EAAsB,KAA2iB,SAAS1E,EAAM2E,GAAStL,EAAgB,SAAGA,EAAgB,QAAEsL,GAAe1C,EAAT0C,GAAM,IAAazC,GAAM,EAAkByC,EAAK,SAASA,EAAK,+CAA+C,IAAIjM,EAAE,IAAIc,YAAYoL,aAAaD,GAA4B,MAAtBhH,EAAmBjF,GAASA,CAAC,CAAC,SAASmM,EAAU3B,EAAI4B,GAAQ,OAAOxB,OAAOyB,UAAUC,WAAW9B,EAAI8B,WAAWF,GAA8B,IAAtB5B,EAAInC,QAAQ+D,EAAW,CAAjazL,EAAwB,gBAAE,CAAC,EAAEA,EAAwB,gBAAE,CAAC,EAAoa,SAAS4L,EAAUzF,GAAU,OAAOqF,EAAUrF,EAAtF,wCAA6G,CAA6B,SAAS0F,EAAU1F,GAAU,OAAOqF,EAAUrF,EAAxD,UAA+E,CAAC,IAAhjO2F,EAAo0TC,EAAhxFC,EAAe,mBAA4F,SAASC,EAAUC,GAAM,IAAI,GAAGA,GAAMF,GAAgBzD,EAAY,OAAO,IAAI9H,WAAW8H,GAAY,GAAG1C,EAAY,OAAOA,EAAWqG,GAAW,KAAK,iDAAkD,CAAC,MAAMtD,GAAKjC,EAAMiC,EAAI,CAAC,CAAwkE,SAASuD,EAAqBC,GAAW,KAAMA,EAAU9L,OAAO,GAAE,CAAC,IAAI+L,EAASD,EAAUE,QAAQ,GAAoB,mBAAVD,EAAV,CAA0D,IAAIpB,EAAKoB,EAASpB,KAAsB,iBAAPA,OAAmCrI,IAAfyJ,EAASE,IAAiB5B,EAAU/K,IAAIqL,EAAdN,GAA2BA,EAAU/K,IAAIqL,EAAdN,CAAoB0B,EAASE,KAAWtB,OAAoBrI,IAAfyJ,EAASE,IAAgB,KAAKF,EAASE,IAA/L,MAAzBF,EAASrM,EAAoN,CAAC,CAAwU,SAASwM,EAASC,GAA4C,OAArCvD,EAAOwD,MAAqB,GAAGD,EAAaA,CAAK,CAA2tB,SAASE,EAA0BC,GAAM,IAAqG,OAAjGnE,EAAWoE,KAAKD,EAAKnN,EAAOqN,WAAW,QAAQ,IAAI1C,EAA2B3B,EAAWhJ,QAAe,CAAC,CAAC,MAAMJ,GAAG,CAAC,CAAz9HuM,EAAUI,KAApmOF,EAA+oOE,EAA1BA,EAA5mOhM,EAAmB,WAAUA,EAAmB,WAAE8L,EAAK9F,GAAwBA,EAAgB8F,GAAwwTC,EAArBlH,EAAyC,WAAW,IAAIhF,EAAE2F,QAAgB,SAAI,OAAY,IAAL3F,EAAE,GAAOA,EAAE,GAAG,GAAG,EAA2B,oBAAVkN,QAA2CA,QAAiC,WAAW,OAAOC,YAAYC,KAAK,EAA8pD,IAAIC,GAAI,CAAC,EAAoE,SAASC,KAAgB,IAAIA,GAAcC,QAAQ,CAAC,IAAuHC,EAAI,CAAC,KAAO,WAAW,QAAU,WAAW,KAAO,IAAI,IAAM,IAAI,KAAO,iBAAiB,MAAnL,iBAAZC,WAAsBA,UAAUC,WAAWD,UAAUC,UAAU,IAAI,KAAKhH,QAAQ,IAAI,KAAK,SAAkH,EAA9SrB,GAAa,kBAA0T,IAAI,IAAI3C,KAAK2K,GAAKG,EAAI9K,GAAG2K,GAAI3K,GAAG,IAAI6K,EAAQ,GAAG,IAAI,IAAI7K,KAAK8K,EAAKD,EAAQpC,KAAKzI,EAAE,IAAI8K,EAAI9K,IAAI4K,GAAcC,QAAQA,CAAO,CAAC,OAAOD,GAAcC,OAAO,CAAC,IAAkoKI,GAA9nKC,GAAS,CAACC,SAAS,CAAC,EAAEC,QAAQ,CAAC,KAAK,GAAG,IAAIC,UAAU,SAASC,EAAOC,GAAM,IAAIrO,EAAOgO,GAASE,QAAQE,GAAkB,IAAPC,GAAiB,KAAPA,IAAqB,IAATD,EAAWnF,EAAIE,GAAKS,EAAkB5J,EAAO,IAAIA,EAAOa,OAAO,GAAOb,EAAOuL,KAAK8C,EAAM,EAAEC,aAAQnL,EAAUhD,IAAI,WAAqE,OAA1D6N,GAASM,SAAS,EAAU7E,EAAOuE,GAASM,QAAQ,GAAG,EAAa,EAAEC,OAAO,SAASC,GAAK,IAAI5H,EAAz+Q,SAAsB4H,EAAIzE,GAAgB,OAAOyE,EAAI5E,EAAkBJ,EAAOgF,OAA+5QC,GAA34Q,EAAE,CAAy4QA,CAAaD,GAAK,OAAO5H,CAAG,EAAE8H,MAAM,SAASC,EAAIC,GAAM,OAAOD,CAAG,GAAs3EE,GAAc,CAAC,EAAtvK,WAAkB3H,GAAO,EAAwuK,EAA/2J,SAAwB4H,EAAOC,GAAI,IAAIvB,EAAI,GAAY,IAATsB,EAAYtB,EAAIwB,KAAKxB,UAAW,IAAa,IAATsB,GAAqB,IAATA,EAA2F,OAAb/B,EAAS,KAAW,EAAnDS,EAAIlB,GAAgD,CAA2D,OAA1D7C,EAAOsF,GAAI,GAAGvB,EAAI,IAAI,EAAE/D,EAAOsF,EAAG,GAAG,GAAGvB,EAAI,IAAI,IAAI,IAAI,EAAS,CAAC,EAA8nJ,EAA7nJ,SAAiB9G,EAASuI,GAAM/H,EAAM,8HAA8H,EAAq+I,EAAp+I,SAAgBgI,EAAOC,GAAQjI,EAAM,8HAA8H,EAA40I,EAA30I,SAAgCkI,EAAKzK,EAAI0K,GAAK7F,EAAO8F,WAAWF,EAAKzK,EAAIA,EAAI0K,EAAI,EAAqxI,EAAxjI,SAAiCE,GAAeA,KAA8B,EAAE,IAAIC,EAApQhG,EAAO3I,OAAqS4O,EAAY,WAAW,GAAGF,EAAcE,EAAa,OAAO,EAA+B,IAAzB,IAAz3N3M,EAA05N4M,EAAQ,EAAEA,GAAS,EAAEA,GAAS,EAAE,CAAC,IAAIC,EAAkBH,GAAS,EAAE,GAAGE,GAAyO,GAAhOC,EAAkBC,KAAKC,IAAIF,EAAkBJ,EAAc,WAAkIrC,EAA3G0C,KAAKC,IAAIJ,IAAnkO3M,EAAulO8M,KAAKE,IAAnN,SAAmOP,EAAcI,IAAmB,MAAnnO,IAAG7M,GAAgnO,MAApmOA,EAAomO,OAAllOA,KAA6pO,OAAO,CAAK,CAAC,OAAO,CAAK,EAA8hH,EAA7hH,SAAkCiN,GAAuC,IAAhC,IAAIC,EAAM1D,IAA4BA,IAAsB0D,EAAMD,IAAQ,EAAu8G,EAA3gF,SAAsBE,EAAUC,GAAa,IAAIC,EAAQ,EAAgK,OAA9JzC,KAAgB0C,SAAQ,SAASC,EAAO9O,GAAG,IAAIiN,EAAI0B,EAAYC,EAAQ1G,EAAOwG,EAAY,EAAF1O,GAAK,GAAGiN,EAAvmR,SAA4BpE,EAAIpK,EAAOsQ,GAAa,IAAI,IAAI/O,EAAE,EAAEA,EAAE6I,EAAIvJ,SAASU,EAAGgI,EAAgB,EAAVvJ,KAAaoK,EAAImG,WAAWhP,GAAmBgI,EAAc,EAARvJ,GAAW,CAAC,CAAk9QwQ,CAAmBH,EAAO7B,GAAK2B,GAASE,EAAOxP,OAAO,CAAC,IAAU,CAAC,EAA2zE,EAA1zE,SAA4B4P,EAAeC,GAAmB,IAAI/C,EAAQD,KAAgBjE,EAAOgH,GAAgB,GAAG9C,EAAQ9M,OAAO,IAAIsP,EAAQ,EAAmG,OAAjGxC,EAAQyC,SAAQ,SAASC,GAAQF,GAASE,EAAOxP,OAAO,CAAC,IAAG4I,EAAOiH,GAAmB,GAAGP,EAAe,CAAC,EAAulE,EAAtlE,SAAexK,IAAs9J,SAAcA,EAAOgL,GAA4D5H,IAAwDxI,EAAe,QAAEA,EAAe,OAAEoF,GAAQyD,GAAM,GAAK1D,EAAMC,EAAO,IAAIsB,GAAWtB,GAAQ,CAAhrKiL,CAAKjL,EAAO,EAA6jE,EAA5jE,SAAmBkL,GAAI,OAAO,CAAC,EAA2iE,EAA1iE,SAAkBA,EAAGC,EAAWC,EAAYC,EAAOC,GAAW,EAAy/D,EAAx/D,SAAmBJ,EAAGK,EAAIC,EAAOC,GAAgB,IAAV,IAAI/B,EAAI,EAAU9N,EAAE,EAAEA,EAAE4P,EAAO5P,IAAI,CAA2D,IAA1D,IAAIiN,EAAI/E,EAAOyH,EAAM,EAAF3P,GAAK,GAAO8P,EAAI5H,EAAOyH,GAAO,EAAF3P,EAAI,IAAI,GAAW+P,EAAE,EAAEA,EAAED,EAAIC,IAAKtD,GAASG,UAAU0C,EAAGrH,EAAOgF,EAAI8C,IAAIjC,GAAKgC,CAAG,CAAqB,OAApB5H,EAAO2H,GAAM,GAAG/B,EAAW,CAAC,EAAgyD,EAA/xD,WAA2B,OAAO,CAAC,EAAgxD,EAA/wD,WAAyB,OAAO,EAAE,EAA+vD,EAA9vD,SAAkBkC,GAAM,OAAOA,GAAM,KAAK,GAA0nC,KAAK,GAAG,OAAO,MAAznC,KAAK,GAA8B,OAAO9B,OAAkB,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,OAAO,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,EAAE,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,OAAO,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,EAAE,OAAO,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,WAAW,KAAK,GAAG,KAAK,EAAE,OAAO,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,KAAK,EAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,GAAG,OAAO,MAA2B,KAAK,GAAG,OAAO,IAAI,KAAK,GAAG,OAAO,IAAI,KAAK,GAAG,OAAO,IAAI,KAAK,GAAG,OAAO,IAAI,KAAK,EAAE,OAAO,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK,GAAG,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,EAAE,KAAK,GAAG,OAAO,EAAE,KAAK,GAAI,MAAsB,iBAAZ5B,WAA4BA,UAA+B,qBAAY,EAAgB,OAAbd,EAAS,KAAW,CAAC,GAAqUtB,IAA31O,WAAsB,IAAI+F,EAAK,CAAC,EAAI3C,IAAe,SAAS4C,EAAgBC,EAASzO,GAAQ,IAAIzC,EAAQkR,EAASlR,QAAQD,EAAY,IAAEC,EAAsCmK,GAA9B3B,EAAWzI,EAAY,IAAK,GAAwCP,QAAQkL,EAAU3K,EAAY,IAAK,EAAjqE,SAA6BoR,GAA6G,GAAzGjG,IAAqBnL,EAA+B,wBAAGA,EAA+B,uBAAEmL,GAAqC,GAAjBA,IAA8C,OAAvBC,IAA6BiG,cAAcjG,GAAsBA,EAAqB,MAAQC,GAAsB,CAAC,IAAIgB,EAAShB,EAAsBA,EAAsB,KAAKgB,GAAU,CAAE,CAAg0DiF,EAAuC,CAAsC,SAASC,EAA0BC,GAAQN,EAAgBM,EAAiB,SAAE,CAAC,SAASC,EAAuBC,GAAU,OAAnjC,WAA4B,IAAInJ,IAAaxD,GAAoBC,GAAuB,CAAC,GAAkB,mBAAR2M,QAAqB9F,EAAUG,GAAiB,OAAO2F,MAAM3F,EAAe,CAAC4F,YAAY,gBAAgBC,MAAK,SAASzJ,GAAU,IAAIA,EAAa,GAAG,KAAK,uCAAuC4D,EAAe,IAAI,OAAO5D,EAAsB,aAAG,IAAG0J,OAAM,WAAW,OAAO7F,EAAUD,EAAe,IAAQ,GAAGpG,EAAW,OAAO,IAAIrB,SAAQ,SAASC,EAAQC,GAAQmB,EAAUoG,GAAe,SAAS5D,GAAU5D,EAAQ,IAAI/D,WAAW2H,GAAU,GAAE3D,EAAO,GAAI,CAAC,OAAOF,QAAQC,UAAUqN,MAAK,WAAW,OAAO5F,EAAUD,EAAe,GAAE,CAAuc+F,GAAmBF,MAAK,SAASzL,GAAQ,OAAOjG,YAAY6R,YAAY5L,EAAO6K,EAAK,IAAGY,KAAKH,GAAS,SAASO,GAAQrJ,EAAI,0CAA0CqJ,GAAQtL,EAAMsL,EAAO,GAAE,CAA2lB,GAAhvG9G,IAAqBnL,EAA+B,wBAAGA,EAA+B,uBAAEmL,GAA2pGnL,EAAwB,gBAAG,IAAgE,OAAhDA,EAAwB,gBAAEiR,EAAKC,EAA+B,CAAC,MAAM7R,GAAgE,OAA7DuJ,EAAI,sDAAsDvJ,IAAU,CAAK,EAAzvBkJ,GAAsD,mBAAnCpI,YAAY2C,sBAAoC8I,EAAUI,IAAkBH,EAAUG,IAAgC,mBAAR2F,MAAsYF,EAAuBF,GAAlYI,MAAM3F,EAAe,CAAC4F,YAAY,gBAAgBC,MAAK,SAASzJ,GAAqE,OAAhDjI,YAAY2C,qBAAqBsF,EAAS6I,GAAoBY,KAAKN,GAA0B,SAASU,GAAuG,OAA/FrJ,EAAI,kCAAkCqJ,GAAQrJ,EAAI,6CAAoD6I,EAAuBF,EAA0B,GAAE,KAAsRO,MAAMxN,EAA4B,CAAw3L4N,GAAoClS,EAA2B,mBAAE,WAAW,OAAOkL,GAAmBlL,EAA2B,mBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,GAAyuDqM,IAAvsD1M,EAAoC,4BAAE,WAAW,OAAmCA,EAAoC,4BAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAA4BL,EAA8B,sBAAE,WAAW,OAA6BA,EAA8B,sBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAsBL,EAAwB,gBAAE,WAAW,OAAuBA,EAAwB,gBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAqBL,EAAuB,eAAE,WAAW,OAAsBA,EAAuB,eAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAA4BL,EAA8B,sBAAE,WAAW,OAA6BA,EAA8B,sBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAA6BL,EAA+B,uBAAE,WAAW,OAA8BA,EAA+B,uBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAuBL,EAAyB,iBAAE,WAAW,OAAwBA,EAAyB,iBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAsBL,EAAwB,gBAAE,WAAW,OAAuBA,EAAwB,gBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAA6BL,EAA+B,uBAAE,WAAW,OAA8BA,EAA+B,uBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAiBL,EAAmB,WAAE,WAAW,OAAkBA,EAAmB,WAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAoBL,EAAsB,cAAE,WAAW,OAAqBA,EAAsB,cAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAwBL,EAA0B,kBAAE,WAAW,OAAO0M,GAAkB1M,EAA0B,kBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,GAAgB,SAASqG,GAAWtB,GAAQgN,KAAKpB,KAAK,aAAaoB,KAAKC,QAAQ,gCAAgCjN,EAAO,IAAIgN,KAAKhN,OAAOA,CAAM,CAA+G,SAASkN,GAAIC,GAA+F,SAASC,IAAWhF,KAAiBA,IAAU,EAAKxN,EAAkB,WAAE,EAAQ6I,IAA53YsD,EAAqBtB,GAA+BsB,EAAqBrB,GAAw1YzG,EAAoBrE,GAAWA,EAA6B,sBAAEA,EAA6B,uBAA73Y,WAAmB,GAAGA,EAAgB,QAAiF,IAAjD,mBAAnBA,EAAgB,UAAcA,EAAgB,QAAE,CAACA,EAAgB,UAASA,EAAgB,QAAEM,QAA6JmS,EAAxIzS,EAAgB,QAAEsM,QAA0HvB,EAAc2H,QAAQD,GAAhD,IAAsBA,EAA5GtG,EAAqBpB,EAAc,CAA8pY4H,IAAS,CAAtTJ,EAAKA,GAAMtN,EAAckG,EAAgB,IAAtgZ,WAAkB,GAAGnL,EAAe,OAA8E,IAA/C,mBAAlBA,EAAe,SAAcA,EAAe,OAAE,CAACA,EAAe,SAASA,EAAe,OAAEM,QAAyfmS,EAArezS,EAAe,OAAEsM,QAAwd1B,EAAa8H,QAAQD,GAA9C,IAAqBA,EAA1ctG,EAAqBvB,EAAa,CAAszYgI,GAAYzH,EAAgB,IAA2OnL,EAAkB,WAAGA,EAAkB,UAAE,cAAc6S,YAAW,WAAWA,YAAW,WAAW7S,EAAkB,UAAE,GAAG,GAAE,GAAGwS,GAAO,GAAE,IAAQA,KAAQ,CAAsP,GAAz0BnH,EAAsB,SAASyH,IAAgBtF,IAAU8E,KAAU9E,KAAUnC,EAAsByH,EAAS,EAAwe9S,EAAY,IAAEsS,GAA0OtS,EAAgB,QAAiF,IAAjD,mBAAnBA,EAAgB,UAAcA,EAAgB,QAAE,CAACA,EAAgB,UAASA,EAAgB,QAAEM,OAAO,GAAGN,EAAgB,QAAE+S,KAAlB/S,GAG/1mB,OAH03mBwI,GAAc,EAAK8J,KAGt4mBrO,EAAuB+O,KAChC,GAIEtQ,EAAOzC,QAAUgE,C,oBCdbD,EADFiP,GAEqCjP,GADnCA,EAAiC,oBAAbE,UAA4BA,SAASC,cAAgBD,SAASC,cAAcC,SAAMxB,I,2EAG5G,SAASqQ,GAGT,IAA6E5O,EAAoBC,EAA7FtE,OAAmC,KAFrCiT,EAAqBA,GAAsB,CAAC,GAEKA,EAAmB,CAAC,EAA6CjT,EAAc,MAAE,IAAIuE,SAAQ,SAASC,EAAQC,GAAQJ,EAAoBG,EAAQF,EAAmBG,CAAM,IAAG,IAA2BC,EAAvBC,EAAgB,CAAC,EAAU,IAAID,KAAO1E,EAAWA,EAAO4E,eAAeF,KAAMC,EAAgBD,GAAK1E,EAAO0E,IAAM,IAAsKG,EAA8BC,EAA3FC,EAA6BC,EAAlIC,EAAW,GAAOC,EAAY,iBAAqBC,EAAM,SAASC,EAAOC,GAAS,MAAMA,CAAO,EAA4HN,EAAmC,iBAATO,OAAkBN,EAA6C,mBAAhBO,cAA2BV,EAAqC,iBAAVW,SAA8C,iBAAnBA,QAAQC,UAAoD,iBAAxBD,QAAQC,SAASC,KAAgBZ,GAAsBC,IAAqBF,IAAsBG,EAAsB,IAA4JW,EAAMC,EAAUC,EAA8BC,EAAWC,EAAjNC,EAAgB,GAA6MnB,GAA+CmB,EAAvBhB,EAAuCiB,EAAAA,KAAAA,QAAwBD,GAAiB,IAAyBE,KAAcP,EAAM,SAAoBQ,EAASC,GAAuH,OAA3GN,IAAOA,EAAOG,EAAQ,MAAUF,IAASA,EAASE,EAAQ,MAAQE,EAASJ,EAAoB,UAAEI,GAAiBL,EAAqB,aAAEK,EAASC,EAAO,KAAK,OAAO,EAAEP,EAAW,SAAoBM,GAAU,IAAIE,EAAIV,EAAMQ,GAAS,GAAiE,OAAvDE,EAAI5G,SAAQ4G,EAAI,IAAI5F,WAAW4F,IAAKC,EAAOD,EAAI5G,QAAe4G,CAAG,EAAKb,QAAc,KAAElF,OAAO,IAAG4E,EAAYM,QAAc,KAAE,GAAGe,QAAQ,MAAM,MAAKtB,EAAWO,QAAc,KAAEgB,MAAM,GAAGhB,QAAY,GAAE,qBAAoB,SAASiB,GAAI,KAAKA,aAAcC,IAAa,MAAMD,CAAG,IAAGjB,QAAY,GAAE,qBAAqBmB,GAAOxB,EAAM,SAASC,GAAQI,QAAc,KAAEJ,EAAO,EAAEpF,EAAgB,QAAE,WAAW,MAAM,4BAA4B,GAAU8E,GAAsC,oBAAN8B,OAAmBjB,EAAM,SAAoBpG,GAAG,OAAOqH,KAAKrH,EAAE,GAAEsG,EAAW,SAAoBtG,GAAG,IAAIsH,EAAK,MAAuB,mBAAbC,WAAgC,IAAIrG,WAAWqG,WAAWvH,KAA0B+G,EAAqB,iBAA3CO,EAAKD,KAAKrH,EAAE,YAAgDsH,EAAI,EAAwB,oBAAZE,WAAyB9B,EAAW8B,gBAAqC,IAAX1G,YAAwB4E,EAAW5E,WAA2B,mBAAP2G,OAAmB7B,EAAM,SAASC,GAAQ4B,KAAK5B,EAAO,GAAoB,oBAAR6B,QAAyC,oBAAVC,UAAsBA,QAAQ,CAAC,GAAEA,QAAQC,IAAIF,MAAMC,QAAQE,KAAKF,QAAQG,MAAwB,oBAAXC,SAAuBA,SAASL,SAAelC,GAAoBC,KAA0BA,EAAuBgB,EAAgBuB,KAAKC,SAASC,KAA+B,oBAAXvD,UAAwBA,SAASC,gBAAe6B,EAAgB9B,SAASC,cAAcC,KAAOJ,IAAYgC,EAAgBhC,GAAoDgC,EAAH,IAAnCA,EAAgB0B,QAAQ,SAA8B1B,EAAgB2B,OAAO,EAAE3B,EAAgB4B,YAAY,KAAK,GAAwB,GAAIjC,EAAM,SAASkC,GAAK,IAAIC,EAAI,IAAIC,eAAwD,OAAzCD,EAAIE,KAAK,MAAMH,GAAI,GAAOC,EAAIG,KAAK,MAAaH,EAAII,YAAY,EAAKlD,IAAuBa,EAAW,SAASgC,GAAK,IAAIC,EAAI,IAAIC,eAAuF,OAAxED,EAAIE,KAAK,MAAMH,GAAI,GAAOC,EAAIK,aAAa,cAAcL,EAAIG,KAAK,MAAa,IAAIxH,WAAWqH,EAAIM,SAAS,GAAExC,EAAU,SAASiC,EAAIQ,EAAOC,GAAS,IAAIR,EAAI,IAAIC,eAAeD,EAAIE,KAAK,MAAMH,GAAI,GAAMC,EAAIK,aAAa,cAAcL,EAAIO,OAAO,WAA0B,KAAZP,EAAI1C,QAAyB,GAAZ0C,EAAI1C,QAAW0C,EAAIM,SAAUC,EAAOP,EAAIM,UAAiBE,GAAS,EAAER,EAAIQ,QAAQA,EAAQR,EAAIG,KAAK,KAAK,GAA6D,IAAiYM,EAAuEC,EAA8JC,EAAlmBC,EAAI1I,EAAc,OAAGkH,QAAQC,IAAIwB,KAAKzB,SAAa0B,EAAI5I,EAAiB,UAAGkH,QAAQE,KAAKuB,KAAKzB,SAAS,IAAIxC,KAAOC,EAAoBA,EAAgBC,eAAeF,KAAM1E,EAAO0E,GAAKC,EAAgBD,IAAMC,EAAgB,KAAQ3E,EAAkB,YAAEiF,EAAWjF,EAAkB,WAAKA,EAAoB,cAAEkF,EAAYlF,EAAoB,aAAKA,EAAa,OAAEmF,EAAMnF,EAAa,MAAoBA,EAAmB,aAAEuI,EAAWvI,EAAmB,YAAuBA,EAAsB,gBAAEwI,EAAcxI,EAAsB,eAA0B,iBAAdG,aAAwBwG,EAAM,mCAAkD,IAAIkC,GAAM,EAAqB,SAASvC,EAAOwC,EAAUC,GAAUD,GAAWnC,EAAM,qBAAqBoC,EAAM,CAAC,IAAykCtJ,EAAOuJ,EAAMC,EAAsBC,EAAxmCC,EAAiC,oBAAdC,YAA0B,IAAIA,YAAY,aAAQxG,EAAU,SAASyG,EAAkBC,EAAKC,EAAIC,GAA6D,IAA7C,IAAIC,EAAOF,EAAIC,EAAmBE,EAAOH,EAAUD,EAAKI,MAAWA,GAAQD,MAAUC,EAAO,GAAGA,EAAOH,EAAI,IAAID,EAAKK,UAAUR,EAAa,OAAOA,EAAYS,OAAON,EAAKK,SAASJ,EAAIG,IAAyB,IAAX,IAAIG,EAAI,GAASN,EAAIG,GAAO,CAAC,IAAII,EAAGR,EAAKC,KAAO,GAAQ,IAAHO,EAAL,CAAoD,IAAIC,EAAe,GAAZT,EAAKC,KAAU,GAAa,MAAN,IAAHO,GAAJ,CAAmE,IAAIE,EAAe,GAAZV,EAAKC,KAAmG,IAAvEO,EAAL,MAAN,IAAHA,IAAqB,GAAHA,IAAQ,GAAGC,GAAI,EAAEC,GAAe,EAAHF,IAAO,GAAGC,GAAI,GAAGC,GAAI,EAAc,GAAZV,EAAKC,MAAgB,MAAOM,GAAKI,OAAOC,aAAaJ,OAAQ,CAAC,IAAIK,EAAGL,EAAG,MAAMD,GAAKI,OAAOC,aAAa,MAAMC,GAAI,GAAG,MAAS,KAAHA,EAAQ,CAAnO,MAAhDN,GAAKI,OAAOC,cAAiB,GAAHJ,IAAQ,EAAEC,EAA7E,MAArCF,GAAKI,OAAOC,aAAaJ,EAAyU,CAAE,OAAOD,CAAG,CAAoZ,SAASO,EAA2BC,GAAK5K,EAAO4K,EAAIrK,EAAc,MAAEgJ,EAAM,IAAIsB,UAAUD,GAAKrK,EAAe,OAAS,IAAIuK,WAAWF,GAAKrK,EAAe,OAAEkJ,EAAO,IAAIsB,WAAWH,GAAKrK,EAAe,OAAEiJ,EAAO,IAAIxI,WAAW4J,GAAKrK,EAAgB,QAAU,IAAImB,YAAYkJ,GAAKrK,EAAgB,QAAU,IAAIa,YAAYwJ,GAAKrK,EAAgB,QAAU,IAAIyK,aAAaJ,GAAKrK,EAAgB,QAAU,IAAI0K,aAAaL,EAAI,CAAoBrK,EAAuB,eAA1C,IAA0D2K,EAAcC,EAAa,GAAOC,EAAW,GAAOC,EAAW,GAAOC,EAAc,GAAwDF,EAAWG,KAAK,CAACC,KAAK,WAAWC,IAAoB,IAAutB,IAAIC,EAAgB,EAAMC,EAAqB,KAASC,EAAsB,KAA2iB,SAAS1E,EAAM2E,GAAStL,EAAgB,SAAGA,EAAgB,QAAEsL,GAAe1C,EAAT0C,GAAM,IAAazC,GAAM,EAAkByC,EAAK,SAASA,EAAK,+CAA+C,IAAIjM,EAAE,IAAIc,YAAYoL,aAAaD,GAA4B,MAAtBhH,EAAmBjF,GAASA,CAAC,CAAC,SAASmM,EAAU3B,EAAI4B,GAAQ,OAAOxB,OAAOyB,UAAUC,WAAW9B,EAAI8B,WAAWF,GAA8B,IAAtB5B,EAAInC,QAAQ+D,EAAW,CAAjazL,EAAwB,gBAAE,CAAC,EAAEA,EAAwB,gBAAE,CAAC,EAAoa,SAAS4L,EAAUzF,GAAU,OAAOqF,EAAUrF,EAAtF,wCAA6G,CAA6B,SAAS0F,EAAU1F,GAAU,OAAOqF,EAAUrF,EAAxD,UAA+E,CAAC,IAAhjO2F,EAA+zTC,EAA3wFC,EAAe,cAAuF,SAASC,EAAUC,GAAM,IAAI,GAAGA,GAAMF,GAAgBzD,EAAY,OAAO,IAAI9H,WAAW8H,GAAY,GAAG1C,EAAY,OAAOA,EAAWqG,GAAW,KAAK,iDAAkD,CAAC,MAAMtD,GAAKjC,EAAMiC,EAAI,CAAC,CAAwkE,SAASuD,EAAqBC,GAAW,KAAMA,EAAU9L,OAAO,GAAE,CAAC,IAAI+L,EAASD,EAAUE,QAAQ,GAAoB,mBAAVD,EAAV,CAA0D,IAAIpB,EAAKoB,EAASpB,KAAsB,iBAAPA,OAAmCrI,IAAfyJ,EAASE,IAAiB5B,EAAU/K,IAAIqL,EAAdN,GAA2BA,EAAU/K,IAAIqL,EAAdN,CAAoB0B,EAASE,KAAWtB,OAAoBrI,IAAfyJ,EAASE,IAAgB,KAAKF,EAASE,IAA/L,MAAzBF,EAASrM,EAAoN,CAAC,CAAwU,SAASwM,EAASC,GAA4C,OAArCvD,EAAOwD,MAAqB,GAAGD,EAAaA,CAAK,CAA2tB,SAASE,EAA0BC,GAAM,IAAqG,OAAjGnE,EAAWoE,KAAKD,EAAKnN,EAAOqN,WAAW,QAAQ,IAAI1C,EAA2B3B,EAAWhJ,QAAe,CAAC,CAAC,MAAMJ,GAAG,CAAC,CAAz9HuM,EAAUI,KAA/lOF,EAA0oOE,EAA1BA,EAAvmOhM,EAAmB,WAAUA,EAAmB,WAAE8L,EAAK9F,GAAwBA,EAAgB8F,GAAmwTC,EAArBlH,EAAyC,WAAW,IAAIhF,EAAE2F,QAAgB,SAAI,OAAY,IAAL3F,EAAE,GAAOA,EAAE,GAAG,GAAG,EAA2B,oBAAVkN,QAA2CA,QAAiC,WAAW,OAAOC,YAAYC,KAAK,EAA8pD,IAAIC,GAAI,CAAC,EAAoE,SAASC,KAAgB,IAAIA,GAAcC,QAAQ,CAAC,IAAuHC,EAAI,CAAC,KAAO,WAAW,QAAU,WAAW,KAAO,IAAI,IAAM,IAAI,KAAO,iBAAiB,MAAnL,iBAAZC,WAAsBA,UAAUC,WAAWD,UAAUC,UAAU,IAAI,KAAKhH,QAAQ,IAAI,KAAK,SAAkH,EAA9SrB,GAAa,kBAA0T,IAAI,IAAI3C,KAAK2K,GAAKG,EAAI9K,GAAG2K,GAAI3K,GAAG,IAAI6K,EAAQ,GAAG,IAAI,IAAI7K,KAAK8K,EAAKD,EAAQpC,KAAKzI,EAAE,IAAI8K,EAAI9K,IAAI4K,GAAcC,QAAQA,CAAO,CAAC,OAAOD,GAAcC,OAAO,CAAC,IAAkoKI,GAA9nKC,GAAS,CAACC,SAAS,CAAC,EAAEC,QAAQ,CAAC,KAAK,GAAG,IAAIC,UAAU,SAASC,EAAOC,GAAM,IAAIrO,EAAOgO,GAASE,QAAQE,GAAkB,IAAPC,GAAiB,KAAPA,IAAqB,IAATD,EAAWnF,EAAIE,GAAKS,EAAkB5J,EAAO,IAAIA,EAAOa,OAAO,GAAOb,EAAOuL,KAAK8C,EAAM,EAAEC,aAAQnL,EAAUhD,IAAI,WAAqE,OAA1D6N,GAASM,SAAS,EAAU7E,EAAOuE,GAASM,QAAQ,GAAG,EAAa,EAAEC,OAAO,SAASC,GAAK,IAAI5H,EAAp+Q,SAAsB4H,EAAIzE,GAAgB,OAAOyE,EAAI5E,EAAkBJ,EAAOgF,OAA05QC,GAAt4Q,EAAE,CAAo4QA,CAAaD,GAAK,OAAO5H,CAAG,EAAE8H,MAAM,SAASC,EAAIC,GAAM,OAAOD,CAAG,GAAs3EE,GAAc,CAAC,EAAtvK,WAAkB3H,GAAO,EAAwuK,EAA/2J,SAAwB4H,EAAOC,GAAI,IAAIvB,EAAI,GAAY,IAATsB,EAAYtB,EAAIwB,KAAKxB,UAAW,IAAa,IAATsB,GAAqB,IAATA,EAA2F,OAAb/B,EAAS,KAAW,EAAnDS,EAAIlB,GAAgD,CAA2D,OAA1D7C,EAAOsF,GAAI,GAAGvB,EAAI,IAAI,EAAE/D,EAAOsF,EAAG,GAAG,GAAGvB,EAAI,IAAI,IAAI,IAAI,EAAS,CAAC,EAA8nJ,EAA7nJ,SAAiB9G,EAASuI,GAAM/H,EAAM,8HAA8H,EAAq+I,EAAp+I,SAAgBgI,EAAOC,GAAQjI,EAAM,8HAA8H,EAA40I,EAA30I,SAAgCkI,EAAKzK,EAAI0K,GAAK7F,EAAO8F,WAAWF,EAAKzK,EAAIA,EAAI0K,EAAI,EAAqxI,EAAxjI,SAAiCE,GAAeA,KAA8B,EAAE,IAAIC,EAApQhG,EAAO3I,OAAqS4O,EAAY,WAAW,GAAGF,EAAcE,EAAa,OAAO,EAA+B,IAAzB,IAAp3N3M,EAAq5N4M,EAAQ,EAAEA,GAAS,EAAEA,GAAS,EAAE,CAAC,IAAIC,EAAkBH,GAAS,EAAE,GAAGE,GAAyO,GAAhOC,EAAkBC,KAAKC,IAAIF,EAAkBJ,EAAc,WAAkIrC,EAA3G0C,KAAKC,IAAIJ,IAA9jO3M,EAAklO8M,KAAKE,IAAnN,SAAmOP,EAAcI,IAAmB,MAA9mO,IAAG7M,GAA2mO,MAA/lOA,EAA+lO,OAA7kOA,KAAwpO,OAAO,CAAK,CAAC,OAAO,CAAK,EAA8hH,EAA7hH,SAAkCiN,GAAuC,IAAhC,IAAIC,EAAM1D,IAA4BA,IAAsB0D,EAAMD,IAAQ,EAAu8G,EAA3gF,SAAsBE,EAAUC,GAAa,IAAIC,EAAQ,EAAgK,OAA9JzC,KAAgB0C,SAAQ,SAASC,EAAO9O,GAAG,IAAIiN,EAAI0B,EAAYC,EAAQ1G,EAAOwG,EAAY,EAAF1O,GAAK,GAAGiN,EAAlmR,SAA4BpE,EAAIpK,EAAOsQ,GAAa,IAAI,IAAI/O,EAAE,EAAEA,EAAE6I,EAAIvJ,SAASU,EAAGgI,EAAgB,EAAVvJ,KAAaoK,EAAImG,WAAWhP,GAAmBgI,EAAc,EAARvJ,GAAW,CAAC,CAA68QwQ,CAAmBH,EAAO7B,GAAK2B,GAASE,EAAOxP,OAAO,CAAC,IAAU,CAAC,EAA2zE,EAA1zE,SAA4B4P,EAAeC,GAAmB,IAAI/C,EAAQD,KAAgBjE,EAAOgH,GAAgB,GAAG9C,EAAQ9M,OAAO,IAAIsP,EAAQ,EAAmG,OAAjGxC,EAAQyC,SAAQ,SAASC,GAAQF,GAASE,EAAOxP,OAAO,CAAC,IAAG4I,EAAOiH,GAAmB,GAAGP,EAAe,CAAC,EAAulE,EAAtlE,SAAexK,IAAs9J,SAAcA,EAAOgL,GAA4D5H,IAAwDxI,EAAe,QAAEA,EAAe,OAAEoF,GAAQyD,GAAM,GAAK1D,EAAMC,EAAO,IAAIsB,GAAWtB,GAAQ,CAAhrKiL,CAAKjL,EAAO,EAA6jE,EAA5jE,SAAmBkL,GAAI,OAAO,CAAC,EAA2iE,EAA1iE,SAAkBA,EAAGC,EAAWC,EAAYC,EAAOC,GAAW,EAAy/D,EAAx/D,SAAmBJ,EAAGK,EAAIC,EAAOC,GAAgB,IAAV,IAAI/B,EAAI,EAAU9N,EAAE,EAAEA,EAAE4P,EAAO5P,IAAI,CAA2D,IAA1D,IAAIiN,EAAI/E,EAAOyH,EAAM,EAAF3P,GAAK,GAAO8P,EAAI5H,EAAOyH,GAAO,EAAF3P,EAAI,IAAI,GAAW+P,EAAE,EAAEA,EAAED,EAAIC,IAAKtD,GAASG,UAAU0C,EAAGrH,EAAOgF,EAAI8C,IAAIjC,GAAKgC,CAAG,CAAqB,OAApB5H,EAAO2H,GAAM,GAAG/B,EAAW,CAAC,EAAgyD,EAA/xD,WAA2B,OAAO,CAAC,EAAgxD,EAA/wD,WAAyB,OAAO,EAAE,EAA+vD,EAA9vD,SAAkBkC,GAAM,OAAOA,GAAM,KAAK,GAA0nC,KAAK,GAAG,OAAO,MAAznC,KAAK,GAA8B,OAAO9B,OAAkB,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,OAAO,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,EAAE,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,OAAO,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,EAAE,OAAO,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,WAAW,KAAK,GAAG,KAAK,EAAE,OAAO,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,KAAK,EAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,GAAG,OAAO,MAA2B,KAAK,GAAG,OAAO,IAAI,KAAK,GAAG,OAAO,IAAI,KAAK,GAAG,OAAO,IAAI,KAAK,GAAG,OAAO,IAAI,KAAK,EAAE,OAAO,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK,GAAG,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,EAAE,KAAK,GAAG,OAAO,EAAE,KAAK,GAAI,MAAsB,iBAAZ5B,WAA4BA,UAA+B,qBAAY,EAAgB,OAAbd,EAAS,KAAW,CAAC,GAAqUtB,IAA31O,WAAsB,IAAI+F,EAAK,CAAC,EAAI3C,IAAe,SAAS4C,EAAgBC,EAASzO,GAAQ,IAAIzC,EAAQkR,EAASlR,QAAQD,EAAY,IAAEC,EAAsCmK,GAA9B3B,EAAWzI,EAAY,IAAK,GAAwCP,QAAQkL,EAAU3K,EAAY,IAAK,EAA5pE,SAA6BoR,GAA6G,GAAzGjG,IAAqBnL,EAA+B,wBAAGA,EAA+B,uBAAEmL,GAAqC,GAAjBA,IAA8C,OAAvBC,IAA6BiG,cAAcjG,GAAsBA,EAAqB,MAAQC,GAAsB,CAAC,IAAIgB,EAAShB,EAAsBA,EAAsB,KAAKgB,GAAU,CAAE,CAA2zDiF,EAAuC,CAAsC,SAASC,EAA0BC,GAAQN,EAAgBM,EAAiB,SAAE,CAAC,SAASC,EAAuBC,GAAU,OAAnjC,WAA4B,IAAInJ,IAAaxD,GAAoBC,GAAuB,CAAC,GAAkB,mBAAR2M,QAAqB9F,EAAUG,GAAiB,OAAO2F,MAAM3F,EAAe,CAAC4F,YAAY,gBAAgBC,MAAK,SAASzJ,GAAU,IAAIA,EAAa,GAAG,KAAK,uCAAuC4D,EAAe,IAAI,OAAO5D,EAAsB,aAAG,IAAG0J,OAAM,WAAW,OAAO7F,EAAUD,EAAe,IAAQ,GAAGpG,EAAW,OAAO,IAAIrB,SAAQ,SAASC,EAAQC,GAAQmB,EAAUoG,GAAe,SAAS5D,GAAU5D,EAAQ,IAAI/D,WAAW2H,GAAU,GAAE3D,EAAO,GAAI,CAAC,OAAOF,QAAQC,UAAUqN,MAAK,WAAW,OAAO5F,EAAUD,EAAe,GAAE,CAAuc+F,GAAmBF,MAAK,SAASzL,GAAQ,OAAOjG,YAAY6R,YAAY5L,EAAO6K,EAAK,IAAGY,KAAKH,GAAS,SAASO,GAAQrJ,EAAI,0CAA0CqJ,GAAQtL,EAAMsL,EAAO,GAAE,CAA2lB,GAA3uG9G,IAAqBnL,EAA+B,wBAAGA,EAA+B,uBAAEmL,GAAspGnL,EAAwB,gBAAG,IAAgE,OAAhDA,EAAwB,gBAAEiR,EAAKC,EAA+B,CAAC,MAAM7R,GAAgE,OAA7DuJ,EAAI,sDAAsDvJ,IAAU,CAAK,EAAzvBkJ,GAAsD,mBAAnCpI,YAAY2C,sBAAoC8I,EAAUI,IAAkBH,EAAUG,IAAgC,mBAAR2F,MAAsYF,EAAuBF,GAAlYI,MAAM3F,EAAe,CAAC4F,YAAY,gBAAgBC,MAAK,SAASzJ,GAAqE,OAAhDjI,YAAY2C,qBAAqBsF,EAAS6I,GAAoBY,KAAKN,GAA0B,SAASU,GAAuG,OAA/FrJ,EAAI,kCAAkCqJ,GAAQrJ,EAAI,6CAAoD6I,EAAuBF,EAA0B,GAAE,KAAsRO,MAAMxN,EAA4B,CAAw3L4N,GAAoClS,EAA2B,mBAAE,WAAW,OAAOkL,GAAmBlL,EAA2B,mBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,GAAyuDqM,IAAvsD1M,EAAoC,4BAAE,WAAW,OAAmCA,EAAoC,4BAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAA4BL,EAA8B,sBAAE,WAAW,OAA6BA,EAA8B,sBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAsBL,EAAwB,gBAAE,WAAW,OAAuBA,EAAwB,gBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAqBL,EAAuB,eAAE,WAAW,OAAsBA,EAAuB,eAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAA4BL,EAA8B,sBAAE,WAAW,OAA6BA,EAA8B,sBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAA6BL,EAA+B,uBAAE,WAAW,OAA8BA,EAA+B,uBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAuBL,EAAyB,iBAAE,WAAW,OAAwBA,EAAyB,iBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAsBL,EAAwB,gBAAE,WAAW,OAAuBA,EAAwB,gBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAA6BL,EAA+B,uBAAE,WAAW,OAA8BA,EAA+B,uBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAiBL,EAAmB,WAAE,WAAW,OAAkBA,EAAmB,WAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAoBL,EAAsB,cAAE,WAAW,OAAqBA,EAAsB,cAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,EAAwBL,EAA0B,kBAAE,WAAW,OAAO0M,GAAkB1M,EAA0B,kBAAEA,EAAY,IAAK,GAAGmS,MAAM,KAAK9R,UAAU,GAAgB,SAASqG,GAAWtB,GAAQgN,KAAKpB,KAAK,aAAaoB,KAAKC,QAAQ,gCAAgCjN,EAAO,IAAIgN,KAAKhN,OAAOA,CAAM,CAA+G,SAASkN,GAAIC,GAA+F,SAASC,IAAWhF,KAAiBA,IAAU,EAAKxN,EAAkB,WAAE,EAAQ6I,IAAv3YsD,EAAqBtB,GAA+BsB,EAAqBrB,GAAm1YzG,EAAoBrE,GAAWA,EAA6B,sBAAEA,EAA6B,uBAAx3Y,WAAmB,GAAGA,EAAgB,QAAiF,IAAjD,mBAAnBA,EAAgB,UAAcA,EAAgB,QAAE,CAACA,EAAgB,UAASA,EAAgB,QAAEM,QAA6JmS,EAAxIzS,EAAgB,QAAEsM,QAA0HvB,EAAc2H,QAAQD,GAAhD,IAAsBA,EAA5GtG,EAAqBpB,EAAc,CAAypY4H,IAAS,CAAtTJ,EAAKA,GAAMtN,EAAckG,EAAgB,IAAjgZ,WAAkB,GAAGnL,EAAe,OAA8E,IAA/C,mBAAlBA,EAAe,SAAcA,EAAe,OAAE,CAACA,EAAe,SAASA,EAAe,OAAEM,QAAyfmS,EAArezS,EAAe,OAAEsM,QAAwd1B,EAAa8H,QAAQD,GAA9C,IAAqBA,EAA1ctG,EAAqBvB,EAAa,CAAizYgI,GAAYzH,EAAgB,IAA2OnL,EAAkB,WAAGA,EAAkB,UAAE,cAAc6S,YAAW,WAAWA,YAAW,WAAW7S,EAAkB,UAAE,GAAG,GAAE,GAAGwS,GAAO,GAAE,IAAQA,KAAQ,CAAsP,GAAz0BnH,EAAsB,SAASyH,IAAgBtF,IAAU8E,KAAU9E,KAAUnC,EAAsByH,EAAS,EAAwe9S,EAAY,IAAEsS,GAA0OtS,EAAgB,QAAiF,IAAjD,mBAAnBA,EAAgB,UAAcA,EAAgB,QAAE,CAACA,EAAgB,UAASA,EAAgB,QAAEM,OAAO,GAAGN,EAAgB,QAAE+S,KAAlB/S,GAGl1mB,OAH62mBwI,GAAc,EAAK8J,KAGz3mBW,EAAmBD,KAC5B,GAIEtQ,EAAOzC,QAAUgT,C,yBCffC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBxQ,IAAjByQ,EACH,OAAOA,EAAapT,QAGrB,IAAIyC,EAASwQ,EAAyBE,GAAY,CAGjDnT,QAAS,CAAC,GAOX,OAHAqT,EAAoBF,GAAU1Q,EAAQA,EAAOzC,QAASkT,GAG/CzQ,EAAOzC,OACf,CCrBAkT,EAAoB3T,EAAKkD,IACxB,IAAI6Q,EAAS7Q,GAAUA,EAAO8Q,WAC7B,IAAO9Q,EAAiB,QACxB,IAAM,EAEP,OADAyQ,EAAoB9Q,EAAEkR,EAAQ,CAAEhS,EAAGgS,IAC5BA,CAAM,ECLdJ,EAAoB9Q,EAAI,CAACpC,EAASwT,KACjC,IAAI,IAAI/O,KAAO+O,EACXN,EAAoB/R,EAAEqS,EAAY/O,KAASyO,EAAoB/R,EAAEnB,EAASyE,IAC5EgP,OAAOC,eAAe1T,EAASyE,EAAK,CAAEkP,YAAY,EAAMhU,IAAK6T,EAAW/O,IAE1E,ECNDyO,EAAoB/R,EAAI,CAACyS,EAAKC,IAAUJ,OAAOhI,UAAU9G,eAAemP,KAAKF,EAAKC,GCClFX,EAAoB7T,EAAKW,IACH,oBAAX+T,QAA0BA,OAAOC,aAC1CP,OAAOC,eAAe1T,EAAS+T,OAAOC,YAAa,CAAExH,MAAO,WAE7DiH,OAAOC,eAAe1T,EAAS,aAAc,CAAEwM,OAAO,GAAO,E,6FCMvD,MA+CMyH,EAAoBC,IAAIC,gBAAgB,IAAIC,KAAK,CArBjD,8ZAqB2D,CAAExQ,KAAM,4BC5CjE,MAAMyQ,EAyBjBC,WAAAA,CAAYC,EAAeC,GACvBrC,KAAKsC,SAAWD,EAEZrC,KAAKsC,SAASC,kBAAkBC,sBAChCxC,KAAKyC,cAAgB3Q,SAAS4Q,cAAc,OAC5C1C,KAAKyC,cAAcE,YAAc,YACjC3C,KAAKyC,cAAczQ,IAAMgO,KAAKsC,SAASC,kBAAkBK,eAE7D5C,KAAK6C,OAAST,EACdpC,KAAKsC,SAAWD,EAChBrC,KAAK8C,wBAA0B9C,KAAKsC,SAASS,MAAQ/C,KAAKsC,SAASU,OAGnEhD,KAAKiD,kBAAoBjD,KAAKiD,kBAAkB1M,KAAKyJ,MAGrDA,KAAKkD,qBAAuBpR,SAAS4Q,cAAc,UACnD1C,KAAKkD,qBAAqBC,WAAW,MACrCnD,KAAKoD,mBAAqBtR,SAAS4Q,cAAc,QACrD,CASA,uBAAMO,CAAkBjN,GDrCA,ICsChBA,EAASvB,KAAKuK,UACRgB,KAAKqD,aAEnB,CAOAC,iBAAAA,GACItD,KAAKuD,iBAAiBC,yBAA2B,OAM7CxD,KAAKsC,SAASC,kBAAkBC,oBAChCxC,KAAKuD,iBAAiBE,OAAS,YAE/BzD,KAAKuD,iBAAiBE,OAAS,YAGnCzD,KAAKuD,iBAAiBG,UAClB1D,KAAK2D,wBACL,EACA,EACA3D,KAAKsC,SAASS,MACd/C,KAAKsC,SAASU,OACd,EACA,EACAhD,KAAKoD,mBAAmBL,MACxB/C,KAAKoD,mBAAmBJ,QAE5BhD,KAAKuD,iBAAiBC,yBAA2B,YACjDxD,KAAKuD,iBAAiBE,OAAS,OAK/BzD,KAAKuD,iBAAiBG,UAAU1D,KAAKoD,mBAAoB,EAAG,GAK5DpD,KAAKuD,iBAAiBC,yBAA2B,mBAC7CxD,KAAKsC,SAASC,kBAAkBC,oBAChCxC,KAAKuD,iBAAiBG,UAClB1D,KAAKyC,cACL,EACA,EACAzC,KAAKoD,mBAAmBL,MACxB/C,KAAKoD,mBAAmBJ,SAG5BhD,KAAKuD,iBAAiBE,OAAS,aAC/BzD,KAAKuD,iBAAiBG,UAAU1D,KAAKoD,mBAAoB,EAAG,GAEpE,CAOAQ,YAAAA,GACI5D,KAAK6C,OAAOgB,gBACZ,MAAMC,EAAqB9D,KAAK6C,OAAOkB,yBAA2B,EAElE,IAAK,IAAInV,EAAI,EAAGA,EAAIoR,KAAK8C,wBAAyBlU,IAAK,CACnD,MAAMoV,EAAahE,KAAK6C,OAAOoB,QAAQH,EAA0B,EAAJlV,GACvDsV,EAASlE,KAAK6C,OAAOoB,QAAQH,EAA0B,EAAJlV,EAAS,GAC5DsL,EAAQ+C,KAAKE,IAAI6G,EAAYE,GAC7BC,EAAgBlH,KAAKmH,IAAIJ,EAAa9J,GACtCmK,EAAYpH,KAAKmH,IAAIF,EAAShK,GAGpC8F,KAAKsE,kBAAkB7P,KAAU,EAAJ7F,EAAS,GAAM,IAAMyV,GAAcF,EAAgBE,EACpF,CACArE,KAAKuE,qBAAqBC,aAAaxE,KAAKsE,kBAAmB,EAAG,EACtE,CAQAjB,WAAAA,GACIrD,KAAKyE,eACLzE,KAAK4D,eACL5D,KAAKsD,oBAELtD,KAAK0E,sBAAsBC,YAAY,CACnC3F,GDvJe,ECwJf4F,OAAQ,IAAO,IAEvB,CAOAH,YAAAA,GACIzE,KAAKuE,qBAAqBb,UACtB1D,KAAKoD,mBACL,EACA,EACApD,KAAKoD,mBAAmBL,MACxB/C,KAAKoD,mBAAmBJ,OACxB,EACA,EACAhD,KAAKsC,SAASS,MACd/C,KAAKsC,SAASU,QAGlB,MAAM6B,EAAY7E,KAAKuE,qBAAqBO,aACxC,EACA,EACA9E,KAAKsC,SAASS,MACd/C,KAAKsC,SAASU,QAEZ+B,EAAoB/E,KAAK6C,OAAOmC,wBAA0B,EAEhE,IAAK,IAAIpW,EAAI,EAAGA,EAAIoR,KAAK8C,wBAAyBlU,IAC9CoR,KAAK6C,OAAOoB,QAAQc,EAAyB,EAAJnW,GAAUiW,EAAUpQ,KAAS,EAAJ7F,GAAS,IAC3EoR,KAAK6C,OAAOoB,QAAQc,EAAyB,EAAJnW,EAAS,GAAKiW,EAAUpQ,KAAU,EAAJ7F,EAAS,GAAK,IACrFoR,KAAK6C,OAAOoB,QAAQc,EAAyB,EAAJnW,EAAS,GAAKiW,EAAUpQ,KAAU,EAAJ7F,EAAS,GAAK,GAE7F,CASAqW,SAAAA,CAAUC,GACN,OAAOA,EAAaC,gBAA6C,WAA3BD,EAAaE,SACvD,CAQAC,WAAAA,CAAY5J,GACRuE,KAAK0E,sBAAwB,IAAIY,OAAOxD,EAAmB,CAAElD,KAAM,uBACnEoB,KAAK0E,sBAAsBa,UAAYvF,KAAKiD,kBAC5C,MAAMuC,EAAkB/J,EAAOgK,iBAAiB,IAC1C,OAAEzC,EAAM,UAAE0C,EAAS,MAAE3C,GACrByC,EAAgBG,YAAcH,EAAgBG,cAAgBH,EAAgBI,iBAsBpF,OApBA5F,KAAKsE,kBAAoB,IAAIuB,UAAU7F,KAAKsC,SAASS,MAAO/C,KAAKsC,SAASU,QAC1EhD,KAAK2D,wBAA0B7R,SAAS4Q,cAAc,UACtD1C,KAAK2D,wBAAwBZ,MAAQ/C,KAAKsC,SAASS,MACnD/C,KAAK2D,wBAAwBX,OAAShD,KAAKsC,SAASU,OACpDhD,KAAKuE,qBAAuBvE,KAAK2D,wBAAwBR,WAAW,MAEpEnD,KAAKkD,qBAAqBH,MAAQ+C,SAAS/C,EAAO,IAClD/C,KAAKkD,qBAAqBF,OAAS8C,SAAS9C,EAAQ,IACpDhD,KAAKuD,iBAAmBvD,KAAKkD,qBAAqBC,WAAW,MAC7DnD,KAAKoD,mBAAmBL,MAAQ+C,SAAS/C,EAAO,IAChD/C,KAAKoD,mBAAmBJ,OAAS8C,SAAS9C,EAAQ,IAClDhD,KAAKoD,mBAAmB2C,UAAW,EACnC/F,KAAKoD,mBAAmB4C,UAAYvK,EACpCuE,KAAKoD,mBAAmB6C,aAAe,KACnCjG,KAAK0E,sBAAsBC,YAAY,CACnC3F,GDpOW,ECqOX4F,OAAQ,IAAO,IACjB,EAGC5E,KAAKkD,qBAAqBgD,cAAcJ,SAASJ,EAAW,IACvE,CAOAS,UAAAA,GACQnG,KAAK0E,uBACL1E,KAAK0E,sBAAsBC,YAAY,CACnC3F,GD1Oa,IC+OrBgB,KAAK0E,sBAAsB0B,WAC/B,E,wCC7PJ,MAAMC,EAAS,CACXC,QAAS,6BACTC,SAAU,8BAGRC,EAAyB,CAC3BF,QAAS,CACLtD,OAAQ,GACRD,MAAO,KAEXwD,SAAU,CACNvD,OAAQ,IACRD,MAAO,MAYR0D,eAAeC,EAA8BnE,GAChD,IAAKoE,iBAAiBrN,UAAUqM,cAAgBgB,iBAAiBrN,UAAUsM,eACvE,MAAM,IAAIgB,MAAM,2CAEpB,IAAIC,EAGAA,EADAC,EAAAA,QAAAA,WACejV,YAEAgP,MAGnB,MAAMkG,EAAoBF,EAAOG,8BAC3BC,QAAsB1H,MAAMuH,EAAAA,QAAAA,KAAyBT,EAAOE,SAAWF,EAAOC,SAEpF,IAAKW,EAAcC,GACf,MAAM,IAAIN,MAAM,oCAGpB,MAAMxE,QAAc6E,EAAcE,cAElCN,EAAOhQ,OAAO/I,IAAI,IAAIO,WAAW+T,GAAQ2E,GAEzCF,EAAOO,WAAWhF,EAAM1H,YAExB,MAAM2H,EAAU,IACTyE,EAAAA,QAAAA,KAAyBN,EAAuBD,SAAWC,EAAuBF,QACrF/D,qBAGJ,OAAO,IAAIL,EAAyB2E,EAAQxE,EAChD,C", "sources": ["webpack://MeetHourJS.app.effects/./node_modules/wasm-check/dist/wasm-check.min.js", "webpack://MeetHourJS.app.effects/./react/features/stream-effects/virtual-background/vendor/tflite/tflite-simd.js", "webpack://MeetHourJS.app.effects/./react/features/stream-effects/virtual-background/vendor/tflite/tflite.js", "webpack://MeetHourJS.app.effects/webpack/bootstrap", "webpack://MeetHourJS.app.effects/webpack/runtime/compat get default export", "webpack://MeetHourJS.app.effects/webpack/runtime/define property getters", "webpack://MeetHourJS.app.effects/webpack/runtime/hasOwnProperty shorthand", "webpack://MeetHourJS.app.effects/webpack/runtime/make namespace object", "webpack://MeetHourJS.app.effects/./react/features/stream-effects/virtual-background/TimerWorker.js", "webpack://MeetHourJS.app.effects/./react/features/stream-effects/virtual-background/MHStreamBackgroundEffect.js", "webpack://MeetHourJS.app.effects/./react/features/stream-effects/virtual-background/index.js"], "sourcesContent": ["\"use strict\";function e(e,r){if(!f)return!1;const n=e.buffer;let u=l.get(n);if(null==u){if((u=t.validate(n))&&r)try{new t.Instance(new t.Module(n)).exports[0]()}catch(e){u=!1}l.set(n,u)}return u}const t=WebAssembly,r=(...e)=>Uint8Array.of(0,97,115,109,1,0,0,0,...e),n=(...e)=>Uint32Array.of(1836278016,1,...e),u=(...e)=>r(1,4,1,96,0,0,3,2,1,0,...e,11,0,10,4,110,97,109,101,2,3,1,0,0),i=(...e)=>Uint16Array.of(24832,28019,1,0,1025,24577,0,515,1,...e),o=(...e)=>n(1610679297,33751040,...e,40239360,259),a=(...e)=>i(...e,2842,4096,28164,28001,357,260,256,560,259,0),s=(...e)=>i(...e,2560,28164,28001,613,259,0),f=\"object\"==typeof t,g=e=>f&&\"function\"==typeof e,l=new WeakMap,c=n(1610679553,58589440,117440770,805372165,101318656,1107297281,268438272,1835101700,17039717,36700416,259),p=s(773,1,2561,269,11,65,65,65,3068,2816),y=s(781,1,2560,265,7,16390,2311,2827),b=r(2,8,1,1,97,1,98,3,127,1,6,6,1,127,1,65,0,11,7,5,1,1,97,3,1,0,8,4,110,97,109,101,2,1,0),m=Uint16Array.of(24832,28019,1,0,1537,24577,512,32639,515,1,2058,1537,16640,16640,2816,2560,28164,28001,613,259,0),A=a(3082,2561,17152,0,0,252),d=a(2058,1537,16640,49152),U=o(101318657,301990913,268438272,1835101700,17039717),x=u(5,4,1,3,1,1,10,7,1,5,0,254,3,0),w=o(84344833,6357249,17369600,4259847,186257917,1845758464),M=u(10,7,1,5,0,208,112,26);module.exports={support:(t=1)=>e(Uint32Array.of(1836278016,t)),get supportStreaming(){return g(t.instantiateStreaming)},feature:{get bigInt(){return e(c,!0)},get bulk(){return e(p)},get exceptions(){return e(y)},get mutableGlobal(){return e(b)},get multiValue(){return e(m)},get saturateConversions(){return e(A)},get signExtensions(){return e(d)},get tailCall(){return e(U)},get threads(){return e(x)},get simd(){return e(w)},get references(){return e(M)},get typeReflection(){return g(t.Memory.type)},get funcReferences(){return g(t.Function)}}};", "\nvar createTFLiteSIMDModule = (function() {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;\n  return (\nfunction(createTFLiteSIMDModule) {\n  createTFLiteSIMDModule = createTFLiteSIMDModule || {};\n\nvar Module=typeof createTFLiteSIMDModule!==\"undefined\"?createTFLiteSIMDModule:{};var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram=\"./this.program\";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;ENVIRONMENT_IS_WEB=typeof window===\"object\";ENVIRONMENT_IS_WORKER=typeof importScripts===\"function\";ENVIRONMENT_IS_NODE=typeof process===\"object\"&&typeof process.versions===\"object\"&&typeof process.versions.node===\"string\";ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;var nodeFS;var nodePath;if(ENVIRONMENT_IS_NODE){if(ENVIRONMENT_IS_WORKER){scriptDirectory=require(\"path\").dirname(scriptDirectory)+\"/\"}else{scriptDirectory=__dirname+\"/\"}read_=function shell_read(filename,binary){if(!nodeFS)nodeFS=require(\"fs\");if(!nodePath)nodePath=require(\"path\");filename=nodePath[\"normalize\"](filename);return nodeFS[\"readFileSync\"](filename,binary?null:\"utf8\")};readBinary=function readBinary(filename){var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};if(process[\"argv\"].length>1){thisProgram=process[\"argv\"][1].replace(/\\\\/g,\"/\")}arguments_=process[\"argv\"].slice(2);process[\"on\"](\"uncaughtException\",function(ex){if(!(ex instanceof ExitStatus)){throw ex}});process[\"on\"](\"unhandledRejection\",abort);quit_=function(status){process[\"exit\"](status)};Module[\"inspect\"]=function(){return\"[Emscripten Module object]\"}}else if(ENVIRONMENT_IS_SHELL){if(typeof read!=\"undefined\"){read_=function shell_read(f){return read(f)}}readBinary=function readBinary(f){var data;if(typeof readbuffer===\"function\"){return new Uint8Array(readbuffer(f))}data=read(f,\"binary\");assert(typeof data===\"object\");return data};if(typeof scriptArgs!=\"undefined\"){arguments_=scriptArgs}else if(typeof arguments!=\"undefined\"){arguments_=arguments}if(typeof quit===\"function\"){quit_=function(status){quit(status)}}if(typeof print!==\"undefined\"){if(typeof console===\"undefined\")console={};console.log=print;console.warn=console.error=typeof printErr!==\"undefined\"?printErr:print}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!==\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];var noExitRuntime;if(Module[\"noExitRuntime\"])noExitRuntime=Module[\"noExitRuntime\"];if(typeof WebAssembly!==\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(\"Assertion failed: \"+text)}}var UTF8Decoder=typeof TextDecoder!==\"undefined\"?new TextDecoder(\"utf8\"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str=\"\";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\"}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module[\"HEAP8\"]=HEAP8=new Int8Array(buf);Module[\"HEAP16\"]=HEAP16=new Int16Array(buf);Module[\"HEAP32\"]=HEAP32=new Int32Array(buf);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(buf);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(buf);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(buf);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(buf);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module[\"INITIAL_MEMORY\"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;__ATINIT__.push({func:function(){___wasm_call_ctors()}});function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){runtimeExited=true}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module[\"preloadedImages\"]={};Module[\"preloadedAudios\"]={};function abort(what){if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}what+=\"\";err(what);ABORT=true;EXITSTATUS=1;what=\"abort(\"+what+\"). Build with -s ASSERTIONS=1 for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}function hasPrefix(str,prefix){return String.prototype.startsWith?str.startsWith(prefix):str.indexOf(prefix)===0}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return hasPrefix(filename,dataURIPrefix)}var fileURIPrefix=\"file://\";function isFileURI(filename){return hasPrefix(filename,fileURIPrefix)}var wasmBinaryFile=\"tflite-simd.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw\"both async and sync fetching of the wasm failed\"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch===\"function\"&&!isFileURI(wasmBinaryFile)){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+wasmBinaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(function(){return getBinary(wasmBinaryFile)})}else{if(readAsync){return new Promise(function(resolve,reject){readAsync(wasmBinaryFile,function(response){resolve(new Uint8Array(response))},reject)})}}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={\"a\":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module[\"asm\"]=exports;wasmMemory=Module[\"asm\"][\"q\"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module[\"asm\"][\"D\"];removeRunDependency(\"wasm-instantiate\")}addRunDependency(\"wasm-instantiate\");function receiveInstantiatedSource(output){receiveInstance(output[\"instance\"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(receiver,function(reason){err(\"failed to asynchronously prepare wasm: \"+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming===\"function\"&&!isDataURI(wasmBinaryFile)&&!isFileURI(wasmBinaryFile)&&typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiatedSource,function(reason){err(\"wasm streaming compile failed: \"+reason);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(receiveInstantiatedSource)})})}else{return instantiateArrayBuffer(receiveInstantiatedSource)}}if(Module[\"instantiateWasm\"]){try{var exports=Module[\"instantiateWasm\"](info,receiveInstance);return exports}catch(e){err(\"Module.instantiateWasm callback failed with error: \"+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback==\"function\"){callback(Module);continue}var func=callback.func;if(typeof func===\"number\"){if(callback.arg===undefined){wasmTable.get(func)()}else{wasmTable.get(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}function _abort(){abort()}var _emscripten_get_now;if(ENVIRONMENT_IS_NODE){_emscripten_get_now=function(){var t=process[\"hrtime\"]();return t[0]*1e3+t[1]/1e6}}else if(typeof dateNow!==\"undefined\"){_emscripten_get_now=dateNow}else _emscripten_get_now=function(){return performance.now()};var _emscripten_get_now_is_monotonic=true;function setErrNo(value){HEAP32[___errno_location()>>2]=value;return value}function _clock_gettime(clk_id,tp){var now;if(clk_id===0){now=Date.now()}else if((clk_id===1||clk_id===4)&&_emscripten_get_now_is_monotonic){now=_emscripten_get_now()}else{setErrNo(28);return-1}HEAP32[tp>>2]=now/1e3|0;HEAP32[tp+4>>2]=now%1e3*1e3*1e3|0;return 0}function _dlopen(filename,flag){abort(\"To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking\")}function _dlsym(handle,symbol){abort(\"To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking\")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function _emscripten_get_heap_size(){return HEAPU8.length}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){requestedSize=requestedSize>>>0;var oldSize=_emscripten_get_heap_size();var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}var minHeapSize=16777216;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(minHeapSize,requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}function _emscripten_thread_sleep(msecs){var start=_emscripten_get_now();while(_emscripten_get_now()-start<msecs){}}var ENV={};function getExecutableName(){return thisProgram||\"./this.program\"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator===\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>\",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(x+\"=\"+env[x])}getEnvStrings.strings=strings}return getEnvStrings.strings}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function _environ_get(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAP32[__environ+i*4>>2]=ptr;writeAsciiToMemory(string,ptr);bufSize+=string.length+1});return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAP32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});HEAP32[penviron_buf_size>>2]=bufSize;return 0}function _exit(status){exit(status)}function _fd_close(fd){return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov+i*8>>2];var len=HEAP32[iov+(i*8+4)>>2];for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}function _pthread_create(){return 6}function _pthread_join(){return 28}function _sysconf(name){switch(name){case 30:return 16384;case 85:var maxHeapSize=2147483648;return maxHeapSize/16384;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:case 80:case 81:case 79:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:{if(typeof navigator===\"object\")return navigator[\"hardwareConcurrency\"]||1;return 1}}setErrNo(28);return-1}var asmLibraryArg={\"a\":_abort,\"n\":_clock_gettime,\"i\":_dlopen,\"e\":_dlsym,\"l\":_emscripten_memcpy_big,\"m\":_emscripten_resize_heap,\"o\":_emscripten_thread_sleep,\"p\":_environ_get,\"g\":_environ_sizes_get,\"j\":_exit,\"h\":_fd_close,\"k\":_fd_seek,\"c\":_fd_write,\"d\":_pthread_create,\"f\":_pthread_join,\"b\":_sysconf};var asm=createWasm();var ___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=function(){return(___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=Module[\"asm\"][\"r\"]).apply(null,arguments)};var _getModelBufferMemoryOffset=Module[\"_getModelBufferMemoryOffset\"]=function(){return(_getModelBufferMemoryOffset=Module[\"_getModelBufferMemoryOffset\"]=Module[\"asm\"][\"s\"]).apply(null,arguments)};var _getInputMemoryOffset=Module[\"_getInputMemoryOffset\"]=function(){return(_getInputMemoryOffset=Module[\"_getInputMemoryOffset\"]=Module[\"asm\"][\"t\"]).apply(null,arguments)};var _getInputHeight=Module[\"_getInputHeight\"]=function(){return(_getInputHeight=Module[\"_getInputHeight\"]=Module[\"asm\"][\"u\"]).apply(null,arguments)};var _getInputWidth=Module[\"_getInputWidth\"]=function(){return(_getInputWidth=Module[\"_getInputWidth\"]=Module[\"asm\"][\"v\"]).apply(null,arguments)};var _getInputChannelCount=Module[\"_getInputChannelCount\"]=function(){return(_getInputChannelCount=Module[\"_getInputChannelCount\"]=Module[\"asm\"][\"w\"]).apply(null,arguments)};var _getOutputMemoryOffset=Module[\"_getOutputMemoryOffset\"]=function(){return(_getOutputMemoryOffset=Module[\"_getOutputMemoryOffset\"]=Module[\"asm\"][\"x\"]).apply(null,arguments)};var _getOutputHeight=Module[\"_getOutputHeight\"]=function(){return(_getOutputHeight=Module[\"_getOutputHeight\"]=Module[\"asm\"][\"y\"]).apply(null,arguments)};var _getOutputWidth=Module[\"_getOutputWidth\"]=function(){return(_getOutputWidth=Module[\"_getOutputWidth\"]=Module[\"asm\"][\"z\"]).apply(null,arguments)};var _getOutputChannelCount=Module[\"_getOutputChannelCount\"]=function(){return(_getOutputChannelCount=Module[\"_getOutputChannelCount\"]=Module[\"asm\"][\"A\"]).apply(null,arguments)};var _loadModel=Module[\"_loadModel\"]=function(){return(_loadModel=Module[\"_loadModel\"]=Module[\"asm\"][\"B\"]).apply(null,arguments)};var _runInference=Module[\"_runInference\"]=function(){return(_runInference=Module[\"_runInference\"]=Module[\"asm\"][\"C\"]).apply(null,arguments)};var ___errno_location=Module[\"___errno_location\"]=function(){return(___errno_location=Module[\"___errno_location\"]=Module[\"asm\"][\"E\"]).apply(null,arguments)};var calledRun;function ExitStatus(status){this.name=\"ExitStatus\";this.message=\"Program terminated with exit(\"+status+\")\";this.status=status}dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();preMain();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}Module[\"run\"]=run;function exit(status,implicit){if(implicit&&noExitRuntime&&status===0){return}if(noExitRuntime){}else{EXITSTATUS=status;exitRuntime();if(Module[\"onExit\"])Module[\"onExit\"](status);ABORT=true}quit_(status,new ExitStatus(status))}if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}noExitRuntime=true;run();\n\n\n  return createTFLiteSIMDModule.ready\n}\n);\n})();\nif (typeof exports === 'object' && typeof module === 'object')\n  module.exports = createTFLiteSIMDModule;\nelse if (typeof define === 'function' && define['amd'])\n  define([], function() { return createTFLiteSIMDModule; });\nelse if (typeof exports === 'object')\n  exports[\"createTFLiteSIMDModule\"] = createTFLiteSIMDModule;\n", "\nvar createTFLiteModule = (function() {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;\n  return (\nfunction(createTFLiteModule) {\n  createTFLiteModule = createTFLiteModule || {};\n\nvar Module=typeof createTFLiteModule!==\"undefined\"?createTFLiteModule:{};var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram=\"./this.program\";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;ENVIRONMENT_IS_WEB=typeof window===\"object\";ENVIRONMENT_IS_WORKER=typeof importScripts===\"function\";ENVIRONMENT_IS_NODE=typeof process===\"object\"&&typeof process.versions===\"object\"&&typeof process.versions.node===\"string\";ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;var nodeFS;var nodePath;if(ENVIRONMENT_IS_NODE){if(ENVIRONMENT_IS_WORKER){scriptDirectory=require(\"path\").dirname(scriptDirectory)+\"/\"}else{scriptDirectory=__dirname+\"/\"}read_=function shell_read(filename,binary){if(!nodeFS)nodeFS=require(\"fs\");if(!nodePath)nodePath=require(\"path\");filename=nodePath[\"normalize\"](filename);return nodeFS[\"readFileSync\"](filename,binary?null:\"utf8\")};readBinary=function readBinary(filename){var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};if(process[\"argv\"].length>1){thisProgram=process[\"argv\"][1].replace(/\\\\/g,\"/\")}arguments_=process[\"argv\"].slice(2);process[\"on\"](\"uncaughtException\",function(ex){if(!(ex instanceof ExitStatus)){throw ex}});process[\"on\"](\"unhandledRejection\",abort);quit_=function(status){process[\"exit\"](status)};Module[\"inspect\"]=function(){return\"[Emscripten Module object]\"}}else if(ENVIRONMENT_IS_SHELL){if(typeof read!=\"undefined\"){read_=function shell_read(f){return read(f)}}readBinary=function readBinary(f){var data;if(typeof readbuffer===\"function\"){return new Uint8Array(readbuffer(f))}data=read(f,\"binary\");assert(typeof data===\"object\");return data};if(typeof scriptArgs!=\"undefined\"){arguments_=scriptArgs}else if(typeof arguments!=\"undefined\"){arguments_=arguments}if(typeof quit===\"function\"){quit_=function(status){quit(status)}}if(typeof print!==\"undefined\"){if(typeof console===\"undefined\")console={};console.log=print;console.warn=console.error=typeof printErr!==\"undefined\"?printErr:print}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!==\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];var noExitRuntime;if(Module[\"noExitRuntime\"])noExitRuntime=Module[\"noExitRuntime\"];if(typeof WebAssembly!==\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(\"Assertion failed: \"+text)}}var UTF8Decoder=typeof TextDecoder!==\"undefined\"?new TextDecoder(\"utf8\"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str=\"\";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\"}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module[\"HEAP8\"]=HEAP8=new Int8Array(buf);Module[\"HEAP16\"]=HEAP16=new Int16Array(buf);Module[\"HEAP32\"]=HEAP32=new Int32Array(buf);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(buf);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(buf);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(buf);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(buf);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module[\"INITIAL_MEMORY\"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;__ATINIT__.push({func:function(){___wasm_call_ctors()}});function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){runtimeExited=true}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module[\"preloadedImages\"]={};Module[\"preloadedAudios\"]={};function abort(what){if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}what+=\"\";err(what);ABORT=true;EXITSTATUS=1;what=\"abort(\"+what+\"). Build with -s ASSERTIONS=1 for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}function hasPrefix(str,prefix){return String.prototype.startsWith?str.startsWith(prefix):str.indexOf(prefix)===0}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return hasPrefix(filename,dataURIPrefix)}var fileURIPrefix=\"file://\";function isFileURI(filename){return hasPrefix(filename,fileURIPrefix)}var wasmBinaryFile=\"tflite.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw\"both async and sync fetching of the wasm failed\"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch===\"function\"&&!isFileURI(wasmBinaryFile)){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+wasmBinaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(function(){return getBinary(wasmBinaryFile)})}else{if(readAsync){return new Promise(function(resolve,reject){readAsync(wasmBinaryFile,function(response){resolve(new Uint8Array(response))},reject)})}}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={\"a\":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module[\"asm\"]=exports;wasmMemory=Module[\"asm\"][\"q\"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module[\"asm\"][\"D\"];removeRunDependency(\"wasm-instantiate\")}addRunDependency(\"wasm-instantiate\");function receiveInstantiatedSource(output){receiveInstance(output[\"instance\"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(receiver,function(reason){err(\"failed to asynchronously prepare wasm: \"+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming===\"function\"&&!isDataURI(wasmBinaryFile)&&!isFileURI(wasmBinaryFile)&&typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiatedSource,function(reason){err(\"wasm streaming compile failed: \"+reason);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(receiveInstantiatedSource)})})}else{return instantiateArrayBuffer(receiveInstantiatedSource)}}if(Module[\"instantiateWasm\"]){try{var exports=Module[\"instantiateWasm\"](info,receiveInstance);return exports}catch(e){err(\"Module.instantiateWasm callback failed with error: \"+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback==\"function\"){callback(Module);continue}var func=callback.func;if(typeof func===\"number\"){if(callback.arg===undefined){wasmTable.get(func)()}else{wasmTable.get(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}function _abort(){abort()}var _emscripten_get_now;if(ENVIRONMENT_IS_NODE){_emscripten_get_now=function(){var t=process[\"hrtime\"]();return t[0]*1e3+t[1]/1e6}}else if(typeof dateNow!==\"undefined\"){_emscripten_get_now=dateNow}else _emscripten_get_now=function(){return performance.now()};var _emscripten_get_now_is_monotonic=true;function setErrNo(value){HEAP32[___errno_location()>>2]=value;return value}function _clock_gettime(clk_id,tp){var now;if(clk_id===0){now=Date.now()}else if((clk_id===1||clk_id===4)&&_emscripten_get_now_is_monotonic){now=_emscripten_get_now()}else{setErrNo(28);return-1}HEAP32[tp>>2]=now/1e3|0;HEAP32[tp+4>>2]=now%1e3*1e3*1e3|0;return 0}function _dlopen(filename,flag){abort(\"To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking\")}function _dlsym(handle,symbol){abort(\"To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking\")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function _emscripten_get_heap_size(){return HEAPU8.length}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){requestedSize=requestedSize>>>0;var oldSize=_emscripten_get_heap_size();var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}var minHeapSize=16777216;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(minHeapSize,requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}function _emscripten_thread_sleep(msecs){var start=_emscripten_get_now();while(_emscripten_get_now()-start<msecs){}}var ENV={};function getExecutableName(){return thisProgram||\"./this.program\"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator===\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>\",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(x+\"=\"+env[x])}getEnvStrings.strings=strings}return getEnvStrings.strings}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function _environ_get(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAP32[__environ+i*4>>2]=ptr;writeAsciiToMemory(string,ptr);bufSize+=string.length+1});return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAP32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});HEAP32[penviron_buf_size>>2]=bufSize;return 0}function _exit(status){exit(status)}function _fd_close(fd){return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov+i*8>>2];var len=HEAP32[iov+(i*8+4)>>2];for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}function _pthread_create(){return 6}function _pthread_join(){return 28}function _sysconf(name){switch(name){case 30:return 16384;case 85:var maxHeapSize=2147483648;return maxHeapSize/16384;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:case 80:case 81:case 79:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:{if(typeof navigator===\"object\")return navigator[\"hardwareConcurrency\"]||1;return 1}}setErrNo(28);return-1}var asmLibraryArg={\"a\":_abort,\"n\":_clock_gettime,\"i\":_dlopen,\"e\":_dlsym,\"l\":_emscripten_memcpy_big,\"m\":_emscripten_resize_heap,\"o\":_emscripten_thread_sleep,\"p\":_environ_get,\"g\":_environ_sizes_get,\"j\":_exit,\"h\":_fd_close,\"k\":_fd_seek,\"c\":_fd_write,\"d\":_pthread_create,\"f\":_pthread_join,\"b\":_sysconf};var asm=createWasm();var ___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=function(){return(___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=Module[\"asm\"][\"r\"]).apply(null,arguments)};var _getModelBufferMemoryOffset=Module[\"_getModelBufferMemoryOffset\"]=function(){return(_getModelBufferMemoryOffset=Module[\"_getModelBufferMemoryOffset\"]=Module[\"asm\"][\"s\"]).apply(null,arguments)};var _getInputMemoryOffset=Module[\"_getInputMemoryOffset\"]=function(){return(_getInputMemoryOffset=Module[\"_getInputMemoryOffset\"]=Module[\"asm\"][\"t\"]).apply(null,arguments)};var _getInputHeight=Module[\"_getInputHeight\"]=function(){return(_getInputHeight=Module[\"_getInputHeight\"]=Module[\"asm\"][\"u\"]).apply(null,arguments)};var _getInputWidth=Module[\"_getInputWidth\"]=function(){return(_getInputWidth=Module[\"_getInputWidth\"]=Module[\"asm\"][\"v\"]).apply(null,arguments)};var _getInputChannelCount=Module[\"_getInputChannelCount\"]=function(){return(_getInputChannelCount=Module[\"_getInputChannelCount\"]=Module[\"asm\"][\"w\"]).apply(null,arguments)};var _getOutputMemoryOffset=Module[\"_getOutputMemoryOffset\"]=function(){return(_getOutputMemoryOffset=Module[\"_getOutputMemoryOffset\"]=Module[\"asm\"][\"x\"]).apply(null,arguments)};var _getOutputHeight=Module[\"_getOutputHeight\"]=function(){return(_getOutputHeight=Module[\"_getOutputHeight\"]=Module[\"asm\"][\"y\"]).apply(null,arguments)};var _getOutputWidth=Module[\"_getOutputWidth\"]=function(){return(_getOutputWidth=Module[\"_getOutputWidth\"]=Module[\"asm\"][\"z\"]).apply(null,arguments)};var _getOutputChannelCount=Module[\"_getOutputChannelCount\"]=function(){return(_getOutputChannelCount=Module[\"_getOutputChannelCount\"]=Module[\"asm\"][\"A\"]).apply(null,arguments)};var _loadModel=Module[\"_loadModel\"]=function(){return(_loadModel=Module[\"_loadModel\"]=Module[\"asm\"][\"B\"]).apply(null,arguments)};var _runInference=Module[\"_runInference\"]=function(){return(_runInference=Module[\"_runInference\"]=Module[\"asm\"][\"C\"]).apply(null,arguments)};var ___errno_location=Module[\"___errno_location\"]=function(){return(___errno_location=Module[\"___errno_location\"]=Module[\"asm\"][\"E\"]).apply(null,arguments)};var calledRun;function ExitStatus(status){this.name=\"ExitStatus\";this.message=\"Program terminated with exit(\"+status+\")\";this.status=status}dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();preMain();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}Module[\"run\"]=run;function exit(status,implicit){if(implicit&&noExitRuntime&&status===0){return}if(noExitRuntime){}else{EXITSTATUS=status;exitRuntime();if(Module[\"onExit\"])Module[\"onExit\"](status);ABORT=true}quit_(status,new ExitStatus(status))}if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}noExitRuntime=true;run();\n\n\n  return createTFLiteModule.ready\n}\n);\n})();\nif (typeof exports === 'object' && typeof module === 'object')\n  module.exports = createTFLiteModule;\nelse if (typeof define === 'function' && define['amd'])\n  define([], function() { return createTFLiteModule; });\nelse if (typeof exports === 'object')\n  exports[\"createTFLiteModule\"] = createTFLiteModule;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "\n/**\n * SET_TIMEOUT constant is used to set interval and it is set in\n * the id property of the request.data property. TimeMs property must\n * also be set. Request.data example:\n *\n * {\n *      id: SET_TIMEOUT,\n *      timeMs: 33\n * }.\n */\nexport const SET_TIMEOUT = 1;\n\n/**\n * C<PERSON>AR_TIMEOUT constant is used to clear the interval and it is set in\n * the id property of the request.data property.\n *\n * {\n *      id: CLEAR_TIMEOUT\n * }.\n */\nexport const CLEAR_TIMEOUT = 2;\n\n/**\n * TIMEOUT_TICK constant is used as response and it is set in the id property.\n *\n * {\n *      id: TIMEOUT_TICK\n * }.\n */\nexport const TIMEOUT_TICK = 3;\n\n/**\n * The following code is needed as string to create a URL from a Blob.\n * The URL is then passed to a WebWorker. Reason for this is to enable\n * use of setInterval that is not throttled when tab is inactive.\n */\nconst code = `\n    var timer;\n\n    onmessage = function(request) {\n        switch (request.data.id) {\n        case ${SET_TIMEOUT}: {\n            timer = setTimeout(() => {\n                postMessage({ id: ${TIMEOUT_TICK} });\n            }, request.data.timeMs);\n            break;\n        }\n        case ${CLEAR_TIMEOUT}: {\n            if (timer) {\n                clearTimeout(timer);\n            }\n            break;\n        }\n        }\n    };\n`;\n\nexport const timerWorkerScript = URL.createObjectURL(new Blob([ code ], { type: 'application/javascript' }));\n", "// @flow\nimport {\n    CLEAR_TIMEOUT,\n    SET_TIMEOUT,\n    TIMEOUT_TICK,\n    timerWorkerScript\n} from './TimerWorker';\nconst blurValue = '25px';\n\n/**\n * Represents a modified MediaStream that adds effects to video background.\n * <tt>MHStreamBackgroundEffect</tt> does the processing of the original\n * video stream.\n */\nexport default class MHStreamBackgroundEffect {\n    _model: Object;\n    _options: Object;\n    _segmentationPixelCount: number;\n    _inputVideoElement: HTMLVideoElement;\n    _onMaskFrameTimer: Function;\n    _maskFrameTimerWorker: Worker;\n    _outputCanvasElement: HTMLCanvasElement;\n    _outputCanvasCtx: Object;\n    _segmentationMaskCtx: Object;\n    _segmentationMask: Object;\n    _segmentationMaskCanvas: Object;\n    _renderMask: Function;\n    _virtualImage: HTMLImageElement;\n    isEnabled: Function;\n    startEffect: Function;\n    stopEffect: Function;\n\n    /**\n     * Represents a modified video MediaStream track.\n     *\n     * @class\n     * @param {Object} model - Meet model.\n     * @param {Object} options - Segmentation dimensions.\n     */\n    constructor(model: Object, options: Object) {\n        this._options = options;\n\n        if (this._options.virtualBackground.isVirtualBackground) {\n            this._virtualImage = document.createElement('img');\n            this._virtualImage.crossOrigin = 'anonymous';\n            this._virtualImage.src = this._options.virtualBackground.virtualSource;\n        }\n        this._model = model;\n        this._options = options;\n        this._segmentationPixelCount = this._options.width * this._options.height;\n\n        // Bind event handler so it is only bound once for every instance.\n        this._onMaskFrameTimer = this._onMaskFrameTimer.bind(this);\n\n        // Workaround for FF issue https://bugzilla.mozilla.org/show_bug.cgi?id=1388974\n        this._outputCanvasElement = document.createElement('canvas');\n        this._outputCanvasElement.getContext('2d');\n        this._inputVideoElement = document.createElement('video');\n    }\n\n    /**\n     * EventHandler onmessage for the maskFrameTimerWorker WebWorker.\n     *\n     * @private\n     * @param {EventHandler} response - The onmessage EventHandler parameter.\n     * @returns {void}\n     */\n    async _onMaskFrameTimer(response: Object) {\n        if (response.data.id === TIMEOUT_TICK) {\n            await this._renderMask();\n        }\n    }\n\n    /**\n     * Represents the run post processing.\n     *\n     * @returns {void}\n     */\n    runPostProcessing() {\n        this._outputCanvasCtx.globalCompositeOperation = 'copy';\n\n        // Draw segmentation mask.\n        //\n\n        // Smooth out the edges.\n        if (this._options.virtualBackground.isVirtualBackground) {\n            this._outputCanvasCtx.filter = 'blur(4px)';\n        } else {\n            this._outputCanvasCtx.filter = 'blur(8px)';\n        }\n\n        this._outputCanvasCtx.drawImage(\n            this._segmentationMaskCanvas,\n            0,\n            0,\n            this._options.width,\n            this._options.height,\n            0,\n            0,\n            this._inputVideoElement.width,\n            this._inputVideoElement.height\n        );\n        this._outputCanvasCtx.globalCompositeOperation = 'source-in';\n        this._outputCanvasCtx.filter = 'none';\n\n        // Draw the foreground video.\n        //\n\n        this._outputCanvasCtx.drawImage(this._inputVideoElement, 0, 0);\n\n        // Draw the background.\n        //\n\n        this._outputCanvasCtx.globalCompositeOperation = 'destination-over';\n        if (this._options.virtualBackground.isVirtualBackground) {\n            this._outputCanvasCtx.drawImage(\n                this._virtualImage,\n                0,\n                0,\n                this._inputVideoElement.width,\n                this._inputVideoElement.height\n            );\n        } else {\n            this._outputCanvasCtx.filter = `blur(${blurValue})`;\n            this._outputCanvasCtx.drawImage(this._inputVideoElement, 0, 0);\n        }\n    }\n\n    /**\n     * Represents the run Tensorflow Interference.\n     *\n     * @returns {void}\n     */\n    runInference() {\n        this._model._runInference();\n        const outputMemoryOffset = this._model._getOutputMemoryOffset() / 4;\n\n        for (let i = 0; i < this._segmentationPixelCount; i++) {\n            const background = this._model.HEAPF32[outputMemoryOffset + (i * 2)];\n            const person = this._model.HEAPF32[outputMemoryOffset + (i * 2) + 1];\n            const shift = Math.max(background, person);\n            const backgroundExp = Math.exp(background - shift);\n            const personExp = Math.exp(person - shift);\n\n            // Sets only the alpha component of each pixel.\n            this._segmentationMask.data[(i * 4) + 3] = (255 * personExp) / (backgroundExp + personExp);\n        }\n        this._segmentationMaskCtx.putImageData(this._segmentationMask, 0, 0);\n    }\n\n    /**\n     * Loop function to render the background mask.\n     *\n     * @private\n     * @returns {void}\n     */\n    _renderMask() {\n        this.resizeSource();\n        this.runInference();\n        this.runPostProcessing();\n\n        this._maskFrameTimerWorker.postMessage({\n            id: SET_TIMEOUT,\n            timeMs: 1000 / 30\n        });\n    }\n\n    /**\n     * Represents the resize source process.\n     *\n     * @returns {void}\n     */\n    resizeSource() {\n        this._segmentationMaskCtx.drawImage(\n            this._inputVideoElement,\n            0,\n            0,\n            this._inputVideoElement.width,\n            this._inputVideoElement.height,\n            0,\n            0,\n            this._options.width,\n            this._options.height\n        );\n\n        const imageData = this._segmentationMaskCtx.getImageData(\n            0,\n            0,\n            this._options.width,\n            this._options.height\n        );\n        const inputMemoryOffset = this._model._getInputMemoryOffset() / 4;\n\n        for (let i = 0; i < this._segmentationPixelCount; i++) {\n            this._model.HEAPF32[inputMemoryOffset + (i * 3)] = imageData.data[i * 4] / 255;\n            this._model.HEAPF32[inputMemoryOffset + (i * 3) + 1] = imageData.data[(i * 4) + 1] / 255;\n            this._model.HEAPF32[inputMemoryOffset + (i * 3) + 2] = imageData.data[(i * 4) + 2] / 255;\n        }\n    }\n\n    /**\n     * Checks if the local track supports this effect.\n     *\n     * @param {MHLocalTrack} mHLocalTrack - Track to apply effect.\n     * @returns {boolean} - Returns true if this effect can run on the specified track\n     * false otherwise.\n     */\n    isEnabled(mHLocalTrack: Object) {\n        return mHLocalTrack.isVideoTrack() && mHLocalTrack.videoType === 'camera';\n    }\n\n    /**\n     * Starts loop to capture video frame and render the segmentation mask.\n     *\n     * @param {MediaStream} stream - Stream to be used for processing.\n     * @returns {MediaStream} - The stream with the applied effect.\n     */\n    startEffect(stream: MediaStream) {\n        this._maskFrameTimerWorker = new Worker(timerWorkerScript, { name: 'Blur effect worker' });\n        this._maskFrameTimerWorker.onmessage = this._onMaskFrameTimer;\n        const firstVideoTrack = stream.getVideoTracks()[0];\n        const { height, frameRate, width }\n            = firstVideoTrack.getSettings ? firstVideoTrack.getSettings() : firstVideoTrack.getConstraints();\n\n        this._segmentationMask = new ImageData(this._options.width, this._options.height);\n        this._segmentationMaskCanvas = document.createElement('canvas');\n        this._segmentationMaskCanvas.width = this._options.width;\n        this._segmentationMaskCanvas.height = this._options.height;\n        this._segmentationMaskCtx = this._segmentationMaskCanvas.getContext('2d');\n\n        this._outputCanvasElement.width = parseInt(width, 10);\n        this._outputCanvasElement.height = parseInt(height, 10);\n        this._outputCanvasCtx = this._outputCanvasElement.getContext('2d');\n        this._inputVideoElement.width = parseInt(width, 10);\n        this._inputVideoElement.height = parseInt(height, 10);\n        this._inputVideoElement.autoplay = true;\n        this._inputVideoElement.srcObject = stream;\n        this._inputVideoElement.onloadeddata = () => {\n            this._maskFrameTimerWorker.postMessage({\n                id: SET_TIMEOUT,\n                timeMs: 1000 / 30\n            });\n        };\n\n        return this._outputCanvasElement.captureStream(parseInt(frameRate, 10));\n    }\n\n    /**\n     * Stops the capture and render loop.\n     *\n     * @returns {void}\n     */\n    stopEffect() {\n        if (this._maskFrameTimerWorker) {\n            this._maskFrameTimerWorker.postMessage({\n                id: CLEAR_TIMEOUT\n            });\n        }\n\n\n        this._maskFrameTimerWorker.terminate();\n    }\n}\n", "// @flow\n\nimport * as wasmCheck from 'wasm-check';\n\nimport MHStreamBackgroundEffect from './MHStreamBackgroundEffect';\nimport createTFLiteModule from './vendor/tflite/tflite';\nimport createTFLiteSIMDModule from './vendor/tflite/tflite-simd';\n\nconst models = {\n    model96: 'libs/segm_lite_v681.tflite',\n    model144: 'libs/segm_full_v679.tflite'\n};\n\nconst segmentationDimensions = {\n    model96: {\n        height: 96,\n        width: 160\n    },\n    model144: {\n        height: 144,\n        width: 256\n    }\n};\n\n/**\n * Creates a new instance of MHStreamBackgroundEffect. This loads the Meet background model that is used to\n * extract person segmentation.\n *\n * @param {Object} virtualBackground - The virtual object that contains the background image source and\n * the isVirtualBackground flag that indicates if virtual image is activated.\n * @returns {Promise<MHStreamBackgroundEffect>}\n */\nexport async function createVirtualBackgroundEffect(virtualBackground: Object) {\n    if (!MediaStreamTrack.prototype.getSettings && !MediaStreamTrack.prototype.getConstraints) {\n        throw new Error('MHStreamBackgroundEffect not supported!');\n    }\n    let tflite;\n\n    if (wasmCheck.feature.simd) {\n        tflite = await createTFLiteSIMDModule();\n    } else {\n        tflite = await createTFLiteModule();\n    }\n\n    const modelBufferOffset = tflite._getModelBufferMemoryOffset();\n    const modelResponse = await fetch(wasmCheck.feature.simd ? models.model144 : models.model96);\n\n    if (!modelResponse.ok) {\n        throw new Error('Failed to download tflite model!');\n    }\n\n    const model = await modelResponse.arrayBuffer();\n\n    tflite.HEAPU8.set(new Uint8Array(model), modelBufferOffset);\n\n    tflite._loadModel(model.byteLength);\n\n    const options = {\n        ...wasmCheck.feature.simd ? segmentationDimensions.model144 : segmentationDimensions.model96,\n        virtualBackground\n    };\n\n    return new MHStreamBackgroundEffect(tflite, options);\n}\n"], "names": ["e", "r", "f", "n", "buffer", "u", "l", "get", "t", "validate", "Instance", "<PERSON><PERSON><PERSON>", "exports", "set", "WebAssembly", "_len", "arguments", "length", "Array", "_key", "Uint8Array", "of", "_len2", "_key2", "Uint32Array", "_len3", "_key3", "i", "_len4", "_key4", "Uint16Array", "o", "_len5", "_key5", "a", "_len6", "_key6", "s", "_len7", "_key7", "g", "WeakMap", "c", "p", "y", "b", "m", "A", "d", "U", "x", "w", "M", "module", "support", "undefined", "supportStreaming", "instantiateStreaming", "feature", "bigInt", "bulk", "exceptions", "mutableGlobal", "multiValue", "saturateConversions", "signExtensions", "tailCall", "threads", "simd", "references", "typeReflection", "Memory", "type", "funcReferences", "Function", "_scriptDir", "createTFLiteSIMDModule", "document", "currentScript", "src", "readyPromiseResolve", "readyPromiseReject", "Promise", "resolve", "reject", "key", "moduleOverrides", "hasOwnProperty", "ENVIRONMENT_IS_NODE", "ENVIRONMENT_IS_SHELL", "ENVIRONMENT_IS_WEB", "ENVIRONMENT_IS_WORKER", "arguments_", "thisProgram", "quit_", "status", "toThrow", "window", "importScripts", "process", "versions", "node", "read_", "readAsync", "readBinary", "nodeFS", "nodePath", "scriptDirectory", "require", "__dirname", "filename", "binary", "ret", "assert", "replace", "slice", "ex", "ExitStatus", "abort", "read", "data", "readbuffer", "scriptArgs", "quit", "print", "console", "log", "warn", "error", "printErr", "self", "location", "href", "indexOf", "substr", "lastIndexOf", "url", "xhr", "XMLHttpRequest", "open", "send", "responseText", "responseType", "response", "onload", "onerror", "wasmBinary", "noExitRuntime", "was<PERSON><PERSON><PERSON><PERSON>", "out", "bind", "err", "ABORT", "condition", "text", "HEAP8", "HEAPU8", "HEAP32", "UTF8Decoder", "TextDecoder", "UTF8ArrayToString", "heap", "idx", "maxBytesToRead", "endIdx", "endPtr", "subarray", "decode", "str", "u0", "u1", "u2", "String", "fromCharCode", "ch", "updateGlobalBufferAndViews", "buf", "Int8Array", "Int16Array", "Int32Array", "Float32Array", "Float64Array", "wasmTable", "__ATPRERUN__", "__ATINIT__", "__ATMAIN__", "__ATPOSTRUN__", "push", "func", "___wasm_call_ctors", "runDependencies", "runDependencyWatcher", "dependenciesFulfilled", "what", "RuntimeError", "hasPrefix", "prefix", "prototype", "startsWith", "isDataURI", "isFileURI", "path", "_emscripten_get_now", "wasmBinaryFile", "getBinary", "file", "callRuntimeCallbacks", "callbacks", "callback", "shift", "arg", "setErrNo", "value", "___errno_location", "emscripten_realloc_buffer", "size", "grow", "byteLength", "dateNow", "performance", "now", "ENV", "getEnvStrings", "strings", "env", "navigator", "languages", "calledRun", "SYSCALLS", "mappings", "buffers", "printChar", "stream", "curr", "varargs", "getStr", "ptr", "UTF8ToString", "get64", "low", "high", "asmLibraryArg", "clk_id", "tp", "Date", "flag", "handle", "symbol", "dest", "num", "copyWithin", "requestedSize", "oldSize", "maxHeapSize", "cutDown", "overGrownHeapSize", "Math", "min", "max", "msecs", "start", "__environ", "environ_buf", "bufSize", "for<PERSON>ach", "string", "dontAdd<PERSON>ull", "charCodeAt", "writeAsciiToMemory", "penviron_count", "penviron_buf_size", "implicit", "exit", "fd", "offset_low", "offset_high", "whence", "newOffset", "iov", "iovcnt", "pnum", "len", "j", "name", "info", "receiveInstance", "instance", "id", "clearInterval", "removeRunDependency", "receiveInstantiatedSource", "output", "instantiateArrayBuffer", "receiver", "fetch", "credentials", "then", "catch", "getBinaryPromise", "instantiate", "reason", "createWasm", "apply", "this", "message", "run", "args", "doRun", "cb", "unshift", "postRun", "preRun", "setTimeout", "runCaller", "pop", "ready", "createTFLiteModule", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "getter", "__esModule", "definition", "Object", "defineProperty", "enumerable", "obj", "prop", "call", "Symbol", "toStringTag", "timerWorkerScript", "URL", "createObjectURL", "Blob", "MHStreamBackgroundEffect", "constructor", "model", "options", "_options", "virtualBackground", "isVirtualBackground", "_virtualImage", "createElement", "crossOrigin", "virtualSource", "_model", "_segmentationPixelCount", "width", "height", "_onMaskFrameTimer", "_outputCanvasElement", "getContext", "_inputVideoElement", "_renderMask", "runPostProcessing", "_outputCanvasCtx", "globalCompositeOperation", "filter", "drawImage", "_segmentationMaskCanvas", "runInference", "_runInference", "outputMemoryOffset", "_getOutputMemoryOffset", "background", "HEAPF32", "person", "backgroundExp", "exp", "personExp", "_segmentationMask", "_segmentationMaskCtx", "putImageData", "resizeSource", "_maskFrameTimerWorker", "postMessage", "timeMs", "imageData", "getImageData", "inputMemoryOffset", "_getInputMemoryOffset", "isEnabled", "mHLocalTrack", "isVideoTrack", "videoType", "startEffect", "Worker", "onmessage", "firstVideoTrack", "getVideoTracks", "frameRate", "getSettings", "getConstraints", "ImageData", "parseInt", "autoplay", "srcObject", "onloadeddata", "captureStream", "stopEffect", "terminate", "models", "model96", "model144", "segmentationDimensions", "async", "createVirtualBackgroundEffect", "MediaStreamTrack", "Error", "tflite", "wasm<PERSON><PERSON><PERSON>", "modelBufferOffset", "_getModelBufferMemoryOffset", "modelResponse", "ok", "arrayBuffer", "_loadModel"], "sourceRoot": ""}