{"version": 3, "file": "donorbox-script.min.js", "mappings": "CACC,SAAUA,EAAGC,GACV,aAEA,IAAID,EAAEE,eAAN,CACAF,EAAEE,gBAAiB,EAEnB,IAAIC,EAEJ,WACE,IAAIC,EAAO,CAAC,EACRC,EAAcC,OAAOC,SAASC,KAAKC,MAAM,KAAK,GAClD,GAAGJ,EAAa,CACd,IAAIK,EAAqB,CAAC,aAAc,eAAgB,aAAc,WAAY,eACrEL,EAAYI,MAAM,KACxBE,KAAI,SAASC,GAClB,IAAIC,EAAWD,EAAEH,MAAM,KAAOK,EAAMD,EAAS,GAAIE,EAAQF,EAAS,GAE/DH,EAAmBM,QAAQF,IAAQ,IACpCV,EAAKU,GAAOC,EAEhB,GACF,CAEA,OAAOX,CACT,CAlBiBa,GAqBbC,EAAU,wBAwEdlB,EAAEmB,QAAU,WACR,IA7CIC,EAA+CC,EASjDC,EAoCEC,GA7CAH,EAAQnB,EAAEuB,uBA7BC,wBA6BoCH,EAAM,SAAUI,GAC/D,OAAOC,MAAMC,UAAUC,MAAMC,KAAKJ,EACtC,EACyB,iBAAdzB,EAAE8B,UAAwB9B,EAAE8B,SAASC,sBAC5CX,EAAQC,EAAID,GAAOY,OAAOX,EAAIpB,EAAEuB,uBAAuBxB,EAAE8B,SAASC,wBAC/DX,GAwCuBa,EAAI,EAAGC,EAAMX,EAAQY,OACnD,GAAW,GAAPD,EAAU,KAAM,oHAEpB,GAvCEZ,EAAQc,UAAUC,UAAUC,cACzB,mTAAmTC,KAAKjB,IACxT,0kDAA0kDiB,KAAKjB,EAAMkB,OAAO,EAAE,IAsCjmD,KAAMP,EAAIC,EAAKD,IACXQ,EAAiBlB,EAAQU,GAAI,QAASS,QAE1C,KAAMT,EAAIC,EAAKD,IACXQ,EAAiBlB,EAAQU,GAAI,QAASS,EAChD,EAEAD,EAAiBzC,EAAG,WAAW,SAAU2C,GAAG,IAAAC,EA9D5BC,EA+DK,iBAAVF,EAAEvC,MAAoC,SAAV,QAANwC,EAAAD,EAAEvC,YAAI,IAAAwC,OAAA,EAANA,EAAQE,QAClB,IAAjBH,EAAEvC,KAAK2C,SAhEGF,EAgEsB5C,EAAE+C,eAAe9B,IA/DhD+B,WAAWC,YAAYL,GAC1BM,IA+DJ,IAEAnD,EAAEmB,SAjH0B,CA0C5B,SAASgC,EAAgBC,GACrBnD,EAAEoD,KAAKC,MAAMC,SAAWH,EAAU,SAAW,MACjD,CAsBA,SAASX,EAAiBe,EAAQC,EAAIC,GAC9BF,EAAOf,iBACPe,EAAOf,iBAAiBgB,EAAIC,GAAU,GACjCF,EAAOG,YACZH,EAAOG,YAAY,KAAOF,EAAIC,GAC7BF,EAAO,KAAOC,GAAMA,CAC7B,CAEA,SAASf,EAAaC,GAClBA,EAAEiB,iBACF,IAnD2BN,EAmDvBE,EAASb,EAAEkB,eAAiBlB,EAAEa,OAC9BM,EAAQ7D,EAAE8D,cAAc,UAE5BZ,GAAgB,GAChBW,EAAME,GAAK9C,EACX4C,EAAMG,YAAc,EACpBH,EAAMI,aAAa,uBAAuB,GAC1CJ,EAAMK,IAAMX,EAAOhD,OAAqC,GAA7BgD,EAAOhD,KAAKQ,QAAQ,KAAa,IAAM,KAAO,cA1D9CsC,EA2DZQ,EAAMR,OA1Dbc,SAAW,QACjBd,EAAMe,QAAU,QAChBf,EAAMgB,KAAO,MACbhB,EAAMiB,IAAM,MACZjB,EAAMkB,MAAQ,OACdlB,EAAMmB,OAAS,OACfnB,EAAMoB,OAAS,MACfpB,EAAMqB,QAAU,MAChBrB,EAAMsB,OAAS,OACftB,EAAMuB,UAAY,SAClBvB,EAAMwB,UAAY,OAClBxB,EAAMyB,WAAa,UACnBzB,EAAM0B,gBAAkB,cACxB1B,EAAM2B,OAAS,WA8CjBhF,EAAEoD,KAAK6B,YAAYpB,GACnBA,EAAMqB,QAGHC,OAAOC,KAAKlF,GAAYgC,OAAS,IAClC2B,EAAMwB,OAAS,WACbxB,EAAMyB,cAAcC,YAAY,CAACC,OAAQ,iBAAkBC,IAAKvF,GAAa,IAC/E,EAEN,CAoBH,CArHA,CAqHCG,OAAQqF", "sources": ["webpack://meet-hour/./donorbox-script.js"], "sourcesContent": ["/* eslint-disable */\n(function (w, d) {\n    'use strict';\n\n    if (w.DBOX_INSTALLED) return;\n    w.DBOX_INSTALLED = true;\n\n    var UTM_PARAMS = extractUtmParams();\n\n    function extractUtmParams() {\n      var data = {};\n      var queryString = window.location.href.split('?')[1];\n      if(queryString) {\n        var supportedUtmParams = ['utm_source', 'utm_campaign', 'utm_medium', 'utm_term', 'utm_content'];\n        var params = queryString.split('&');\n        params.map(function(p){\n          var splitted = p.split('='),  key = splitted[0], value = splitted[1];\n\n          if(supportedUtmParams.indexOf(key) >= 0) {\n            data[key] = value;\n          }\n        });\n      }\n\n      return data;\n    }\n\n    var buttonClass  = 'dbox-donation-button',\n        frameID = 'donorbox_widget_frame',\n        setFrameStyles = function (style) {\n          style.position = 'fixed';\n          style.display = 'block';\n          style.left = '0px';\n          style.top = '0px';\n          style.width = '100%';\n          style.height = '100%';\n          style.margin = '0px';\n          style.padding = '0px';\n          style.border = 'none';\n          style.overflowX = 'hidden';\n          style.overflowY = 'auto';\n          style.visibility = 'visible';\n          style.backgroundColor = 'transparent';\n          style.zIndex = 2147483647;\n        };\n\n    function toggleScrolling(disable) {\n        d.body.style.overflow = disable ? 'hidden' : 'auto';\n    }\n\n    function remove(el) {\n        el.parentNode.removeChild(el);\n        toggleScrolling()\n    }\n\n    function queryButtons() {\n        var links = d.getElementsByClassName(buttonClass), arr = function (arrayLike) {\n            return Array.prototype.slice.call(arrayLike)\n        };\n        if (typeof w.DonorBox == 'object' && w.DonorBox.widgetLinkClassName)\n            links = arr(links).concat(arr(d.getElementsByClassName(w.DonorBox.widgetLinkClassName)));\n        return links;\n    }\n\n    function shouldOpenNewTab() {\n      var agent = navigator.userAgent.toLowerCase();\n      return /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(agent)||\n             /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i.test(agent.substr(0,4));\n    }\n\n    function addEventListener(target, ev, listener) {\n        if (target.addEventListener)\n            target.addEventListener(ev, listener, false);\n        else if (target.attachEvent)\n            target.attachEvent('on' + ev, listener);\n        else target['on' + ev] = ev\n    }\n\n    function openTheModal(e) {\n        e.preventDefault();\n        var target = e.currentTarget || e.target,\n            frame = d.createElement('iframe');\n\n        toggleScrolling(true);\n        frame.id = frameID;\n        frame.frameborder = 0;\n        frame.setAttribute('allowpaymentrequest', true);\n        frame.src = target.href + (target.href.indexOf('?') == -1 ? '?' : '&') + 'modal=true';\n        setFrameStyles(frame.style);\n        d.body.appendChild(frame);\n        frame.focus();\n        \n        // Send UTM Params to donorbox iframes\n        if(Object.keys(UTM_PARAMS).length > 0) {\n          frame.onload = function(){\n            frame.contentWindow.postMessage({action: 'set-utm-params', msg: UTM_PARAMS}, '*');\n          };\n        }\n    }\n\n    w.dw_open = function () {\n        var buttons = queryButtons(), i = 0, len = buttons.length;\n        if (len == 0) throw 'Donation widget button is not exists. If you see these on your WEB page, please, check button installation steps.';\n\n        if (shouldOpenNewTab())\n          for(; i < len; i++)\n              addEventListener(buttons[i], 'click', openTheModal) //  buttons[i].setAttribute('target', '_blank');\n        else\n          for(; i < len; i++)\n              addEventListener(buttons[i], 'click', openTheModal)\n    };\n\n    addEventListener(w, 'message', function (e) {\n        typeof e.data == 'object' && e.data?.from == 'dbox' &&\n          e.data.close === true && remove(d.getElementById(frameID))\n    });\n\n    w.dw_open();\n}(window, document));"], "names": ["w", "d", "DBOX_INSTALLED", "UTM_PARAMS", "data", "queryString", "window", "location", "href", "split", "supportedUtmParams", "map", "p", "splitted", "key", "value", "indexOf", "extractUtmParams", "frameID", "dw_open", "links", "arr", "agent", "buttons", "getElementsByClassName", "arrayLike", "Array", "prototype", "slice", "call", "DonorBox", "widgetLinkClassName", "concat", "i", "len", "length", "navigator", "userAgent", "toLowerCase", "test", "substr", "addEventListener", "openTheModal", "e", "_e$data", "el", "from", "close", "getElementById", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "toggleScrolling", "disable", "body", "style", "overflow", "target", "ev", "listener", "attachEvent", "preventDefault", "currentTarget", "frame", "createElement", "id", "frameborder", "setAttribute", "src", "position", "display", "left", "top", "width", "height", "margin", "padding", "border", "overflowX", "overflowY", "visibility", "backgroundColor", "zIndex", "append<PERSON><PERSON><PERSON>", "focus", "Object", "keys", "onload", "contentWindow", "postMessage", "action", "msg", "document"], "sourceRoot": ""}