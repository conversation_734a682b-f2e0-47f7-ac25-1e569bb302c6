var Module=typeof Module!=="undefined"?Module:{};((function(root,factory){var lib,env;if(typeof define==="function"&&define.amd){define((function(){var _lib=factory(root);lib=_lib;return _lib}))}else if(typeof module==="object"&&module.exports){env=typeof process!=="undefined"&&process&&process.env?process.env:root;lib=factory(env);module.exports=lib}else{lib=factory(root);root.Flac=lib}if(env?!env.FLAC_UMD_MODE:!root.FLAC_UMD_MODE){root=env&&env!==root&&typeof global!=="undefined"&&global?global:root;root.Flac=lib}}))(typeof self!=="undefined"?self:this,(function(global){"use strict";var Module=Module||{};var _flac_ready=false;Module["onRuntimeInitialized"]=(function(){_flac_ready=true;if(!_exported){setTimeout((function(){if(_exported.onready){_exported.onready()}}),0)}else{if(_exported.onready){_exported.onready()}}});if(global&&global.FLAC_SCRIPT_LOCATION){Module["memoryInitializerPrefixURL"]=global.FLAC_SCRIPT_LOCATION;Module["locateFile"]=(function(fileName){var path=global.FLAC_SCRIPT_LOCATION||"";path+=path&&!/\/$/.test(path)?"/":"";return path+fileName});Module["readBinary"]=(function(filePath){if(ENVIRONMENT_IS_NODE){var ret=Module["read"](filePath,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret}return new Promise((function(resolve,reject){var xhr=new XMLHttpRequest;xhr.responseType="arraybuffer";xhr.addEventListener("load",(function(evt){resolve(xhr.response)}));xhr.addEventListener("error",(function(err){reject(err)}));xhr.open("GET",filePath);xhr.send()}))})}var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}Module["arguments"]=[];Module["thisProgram"]="./this.program";Module["quit"]=(function(status,toThrow){throw toThrow});Module["preRun"]=[];Module["postRun"]=[];var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;ENVIRONMENT_IS_WEB=typeof window==="object";ENVIRONMENT_IS_WORKER=typeof importScripts==="function";ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof require==="function"&&!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER;ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(ENVIRONMENT_IS_NODE){var nodeFS;var nodePath;Module["read"]=function shell_read(filename,binary){var ret;if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);ret=nodeFS["readFileSync"](filename);return binary?ret:ret.toString()};Module["readBinary"]=function readBinary(filename){var ret=Module["read"](filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};if(process["argv"].length>1){Module["thisProgram"]=process["argv"][1].replace(/\\/g,"/")}Module["arguments"]=process["argv"].slice(2);if(typeof module!=="undefined"){module["exports"]=Module}process["on"]("uncaughtException",(function(ex){if(!(ex instanceof ExitStatus)){throw ex}}));process["on"]("unhandledRejection",(function(reason,p){process["exit"](1)}));Module["quit"]=(function(status){process["exit"](status)});Module["inspect"]=(function(){return"[Emscripten Module object]"})}else if(ENVIRONMENT_IS_SHELL){if(typeof read!="undefined"){Module["read"]=function shell_read(f){return read(f)}}Module["readBinary"]=function readBinary(f){var data;if(typeof readbuffer==="function"){return new Uint8Array(readbuffer(f))}data=read(f,"binary");assert(typeof data==="object");return data};if(typeof scriptArgs!="undefined"){Module["arguments"]=scriptArgs}else if(typeof arguments!="undefined"){Module["arguments"]=arguments}if(typeof quit==="function"){Module["quit"]=(function(status){quit(status)})}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){Module["read"]=function shell_read(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){Module["readBinary"]=function readBinary(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}Module["readAsync"]=function readAsync(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function xhr_onload(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)};Module["setWindowTitle"]=(function(title){document.title=title})}else{}var out=Module["print"]||(typeof console!=="undefined"?console.log.bind(console):typeof print!=="undefined"?print:null);var err=Module["printErr"]||(typeof printErr!=="undefined"?printErr:typeof console!=="undefined"&&console.warn.bind(console)||out);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=undefined;var STACK_ALIGN=16;function staticAlloc(size){var ret=STATICTOP;STATICTOP=STATICTOP+size+15&-16;return ret}function dynamicAlloc(size){var ret=HEAP32[DYNAMICTOP_PTR>>2];var end=ret+size+15&-16;HEAP32[DYNAMICTOP_PTR>>2]=end;if(end>=TOTAL_MEMORY){var success=enlargeMemory();if(!success){HEAP32[DYNAMICTOP_PTR>>2]=ret;return 0}}return ret}function alignMemory(size,factor){if(!factor)factor=STACK_ALIGN;var ret=size=Math.ceil(size/factor)*factor;return ret}function getNativeTypeSize(type){switch(type){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:{if(type[type.length-1]==="*"){return 4}else if(type[0]==="i"){var bits=parseInt(type.substr(1));assert(bits%8===0);return bits/8}else{return 0}}}}function warnOnce(text){if(!warnOnce.shown)warnOnce.shown={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;err(text)}}var jsCallStartIndex=1;var functionPointers=new Array(5);function addFunction(func,sig){var base=0;for(var i=base;i<base+5;i++){if(!functionPointers[i]){functionPointers[i]=func;return jsCallStartIndex+i}}throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."}var funcWrappers={};function dynCall(sig,ptr,args){if(args&&args.length){return Module["dynCall_"+sig].apply(null,[ptr].concat(args))}else{return Module["dynCall_"+sig].call(null,ptr)}}var GLOBAL_BASE=8;var ABORT=0;var EXITSTATUS=0;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}function getCFunc(ident){var func=Module["_"+ident];assert(func,"Cannot call unknown function "+ident+", make sure it is exported");return func}var JSfuncs={"stackSave":(function(){stackSave()}),"stackRestore":(function(){stackRestore()}),"arrayToC":(function(arr){var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}),"stringToC":(function(str){var ret=0;if(str!==null&&str!==undefined&&str!==0){var len=(str.length<<2)+1;ret=stackAlloc(len);stringToUTF8(str,ret,len)}return ret})};var toC={"string":JSfuncs["stringToC"],"array":JSfuncs["arrayToC"]};function ccall(ident,returnType,argTypes,args,opts){function convertReturnValue(ret){if(returnType==="string")return Pointer_stringify(ret);if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);ret=convertReturnValue(ret);if(stack!==0)stackRestore(stack);return ret}function cwrap(ident,returnType,argTypes,opts){argTypes=argTypes||[];var numericArgs=argTypes.every((function(type){return type==="number"}));var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return(function(){return ccall(ident,returnType,argTypes,arguments,opts)})}function setValue(ptr,value,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":HEAP8[ptr>>0]=value;break;case"i8":HEAP8[ptr>>0]=value;break;case"i16":HEAP16[ptr>>1]=value;break;case"i32":HEAP32[ptr>>2]=value;break;case"i64":tempI64=[value>>>0,(tempDouble=value,+Math_abs(tempDouble)>=+1?tempDouble>+0?(Math_min(+Math_floor(tempDouble/+4294967296),+4294967295)|0)>>>0:~~+Math_ceil((tempDouble- +(~~tempDouble>>>0))/+4294967296)>>>0:0)],HEAP32[ptr>>2]=tempI64[0],HEAP32[ptr+4>>2]=tempI64[1];break;case"float":HEAPF32[ptr>>2]=value;break;case"double":HEAPF64[ptr>>3]=value;break;default:abort("invalid type for setValue: "+type)}}function getValue(ptr,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":return HEAP8[ptr>>0];case"i8":return HEAP8[ptr>>0];case"i16":return HEAP16[ptr>>1];case"i32":return HEAP32[ptr>>2];case"i64":return HEAP32[ptr>>2];case"float":return HEAPF32[ptr>>2];case"double":return HEAPF64[ptr>>3];default:abort("invalid type for getValue: "+type)}return null}var ALLOC_STATIC=2;var ALLOC_NONE=4;function Pointer_stringify(ptr,length){if(length===0||!ptr)return"";var hasUtf=0;var t;var i=0;while(1){t=HEAPU8[ptr+i>>0];hasUtf|=t;if(t==0&&!length)break;i++;if(length&&i==length)break}if(!length)length=i;var ret="";if(hasUtf<128){var MAX_CHUNK=1024;var curr;while(length>0){curr=String.fromCharCode.apply(String,HEAPU8.subarray(ptr,ptr+Math.min(length,MAX_CHUNK)));ret=ret?ret+curr:curr;ptr+=MAX_CHUNK;length-=MAX_CHUNK}return ret}return UTF8ToString(ptr)}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(u8Array,idx){var endPtr=idx;while(u8Array[endPtr])++endPtr;if(endPtr-idx>16&&u8Array.subarray&&UTF8Decoder){return UTF8Decoder.decode(u8Array.subarray(idx,endPtr))}else{var u0,u1,u2,u3,u4,u5;var str="";while(1){u0=u8Array[idx++];if(!u0)return str;if(!(u0&128)){str+=String.fromCharCode(u0);continue}u1=u8Array[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}u2=u8Array[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u3=u8Array[idx++]&63;if((u0&248)==240){u0=(u0&7)<<18|u1<<12|u2<<6|u3}else{u4=u8Array[idx++]&63;if((u0&252)==248){u0=(u0&3)<<24|u1<<18|u2<<12|u3<<6|u4}else{u5=u8Array[idx++]&63;u0=(u0&1)<<30|u1<<24|u2<<18|u3<<12|u4<<6|u5}}}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}}function UTF8ToString(ptr){return UTF8ArrayToString(HEAPU8,ptr)}function stringToUTF8Array(str,outU8Array,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127){if(outIdx>=endIdx)break;outU8Array[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;outU8Array[outIdx++]=192|u>>6;outU8Array[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;outU8Array[outIdx++]=224|u>>12;outU8Array[outIdx++]=128|u>>6&63;outU8Array[outIdx++]=128|u&63}else if(u<=2097151){if(outIdx+3>=endIdx)break;outU8Array[outIdx++]=240|u>>18;outU8Array[outIdx++]=128|u>>12&63;outU8Array[outIdx++]=128|u>>6&63;outU8Array[outIdx++]=128|u&63}else if(u<=67108863){if(outIdx+4>=endIdx)break;outU8Array[outIdx++]=248|u>>24;outU8Array[outIdx++]=128|u>>18&63;outU8Array[outIdx++]=128|u>>12&63;outU8Array[outIdx++]=128|u>>6&63;outU8Array[outIdx++]=128|u&63}else{if(outIdx+5>=endIdx)break;outU8Array[outIdx++]=252|u>>30;outU8Array[outIdx++]=128|u>>24&63;outU8Array[outIdx++]=128|u>>18&63;outU8Array[outIdx++]=128|u>>12&63;outU8Array[outIdx++]=128|u>>6&63;outU8Array[outIdx++]=128|u&63}}outU8Array[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127){++len}else if(u<=2047){len+=2}else if(u<=65535){len+=3}else if(u<=2097151){len+=4}else if(u<=67108863){len+=5}else{len+=6}}return len}var UTF16Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf-16le"):undefined;function demangle(func){return func}function demangleAll(text){var regex=/__Z[\w\d_]+/g;return text.replace(regex,(function(x){var y=demangle(x);return x===y?x:x+" ["+y+"]"}))}function jsStackTrace(){var err=new Error;if(!err.stack){try{throw new Error(0)}catch(e){err=e}if(!err.stack){return"(no stack trace available)"}}return err.stack.toString()}function stackTrace(){var js=jsStackTrace();if(Module["extraStackTrace"])js+="\n"+Module["extraStackTrace"]();return demangleAll(js)}var WASM_PAGE_SIZE=65536;var ASMJS_PAGE_SIZE=16777216;var MIN_TOTAL_MEMORY=16777216;function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBuffer(buf){Module["buffer"]=buffer=buf}function updateGlobalBufferViews(){Module["HEAP8"]=HEAP8=new Int8Array(buffer);Module["HEAP16"]=HEAP16=new Int16Array(buffer);Module["HEAP32"]=HEAP32=new Int32Array(buffer);Module["HEAPU8"]=HEAPU8=new Uint8Array(buffer);Module["HEAPU16"]=HEAPU16=new Uint16Array(buffer);Module["HEAPU32"]=HEAPU32=new Uint32Array(buffer);Module["HEAPF32"]=HEAPF32=new Float32Array(buffer);Module["HEAPF64"]=HEAPF64=new Float64Array(buffer)}var STATIC_BASE,STATICTOP,staticSealed;var STACK_BASE,STACKTOP,STACK_MAX;var DYNAMIC_BASE,DYNAMICTOP_PTR;STATIC_BASE=STATICTOP=STACK_BASE=STACKTOP=STACK_MAX=DYNAMIC_BASE=DYNAMICTOP_PTR=0;staticSealed=false;function abortOnCannotGrowMemory(){abort("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+TOTAL_MEMORY+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime but prevents some optimizations, (3) set Module.TOTAL_MEMORY to a higher value before the program runs, or (4) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}if(!Module["reallocBuffer"])Module["reallocBuffer"]=(function(size){var ret;try{if(ArrayBuffer.transfer){ret=ArrayBuffer.transfer(buffer,size)}else{var oldHEAP8=HEAP8;ret=new ArrayBuffer(size);var temp=new Int8Array(ret);temp.set(oldHEAP8)}}catch(e){return false}var success=_emscripten_replace_memory(ret);if(!success)return false;return ret});function enlargeMemory(){var PAGE_MULTIPLE=Module["usingWasm"]?WASM_PAGE_SIZE:ASMJS_PAGE_SIZE;var LIMIT=2147483648-PAGE_MULTIPLE;if(HEAP32[DYNAMICTOP_PTR>>2]>LIMIT){return false}var OLD_TOTAL_MEMORY=TOTAL_MEMORY;TOTAL_MEMORY=Math.max(TOTAL_MEMORY,MIN_TOTAL_MEMORY);while(TOTAL_MEMORY<HEAP32[DYNAMICTOP_PTR>>2]){if(TOTAL_MEMORY<=536870912){TOTAL_MEMORY=alignUp(2*TOTAL_MEMORY,PAGE_MULTIPLE)}else{TOTAL_MEMORY=Math.min(alignUp((3*TOTAL_MEMORY+2147483648)/4,PAGE_MULTIPLE),LIMIT)}}var replacement=Module["reallocBuffer"](TOTAL_MEMORY);if(!replacement||replacement.byteLength!=TOTAL_MEMORY){TOTAL_MEMORY=OLD_TOTAL_MEMORY;return false}updateGlobalBuffer(replacement);updateGlobalBufferViews();return true}var byteLength;try{byteLength=Function.prototype.call.bind(Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get);byteLength(new ArrayBuffer(4))}catch(e){byteLength=(function(buffer){return buffer.byteLength})}var TOTAL_STACK=Module["TOTAL_STACK"]||5242880;var TOTAL_MEMORY=Module["TOTAL_MEMORY"]||16777216;if(TOTAL_MEMORY<TOTAL_STACK)err("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+TOTAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")");if(Module["buffer"]){buffer=Module["buffer"]}else{{buffer=new ArrayBuffer(TOTAL_MEMORY)}Module["buffer"]=buffer}updateGlobalBufferViews();function getTotalMemory(){return TOTAL_MEMORY}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback();continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){Module["dynCall_v"](func)}else{Module["dynCall_vi"](func,callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATEXIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){if(runtimeInitialized)return;runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){callRuntimeCallbacks(__ATEXIT__);runtimeExited=true}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}function writeArrayToMemory(array,buffer){HEAP8.set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}var Math_abs=Math.abs;var Math_ceil=Math.ceil;var Math_floor=Math.floor;var Math_min=Math.min;var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function getUniqueRunDependency(id){return id}function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};var memoryInitializer=null;var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return String.prototype.startsWith?filename.startsWith(dataURIPrefix):filename.indexOf(dataURIPrefix)===0}STATIC_BASE=GLOBAL_BASE;STATICTOP=STATIC_BASE+6144;__ATINIT__.push();memoryInitializer="libflac4-1.3.2.min.js.mem";var tempDoublePtr=STATICTOP;STATICTOP+=16;function ___lock(){}var ERRNO_CODES={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};var ERRNO_MESSAGES={0:"Success",1:"Not super-user",2:"No such file or directory",3:"No such process",4:"Interrupted system call",5:"I/O error",6:"No such device or address",7:"Arg list too long",8:"Exec format error",9:"Bad file number",10:"No children",11:"No more processes",12:"Not enough core",13:"Permission denied",14:"Bad address",15:"Block device required",16:"Mount device busy",17:"File exists",18:"Cross-device link",19:"No such device",20:"Not a directory",21:"Is a directory",22:"Invalid argument",23:"Too many open files in system",24:"Too many open files",25:"Not a typewriter",26:"Text file busy",27:"File too large",28:"No space left on device",29:"Illegal seek",30:"Read only file system",31:"Too many links",32:"Broken pipe",33:"Math arg out of domain of func",34:"Math result not representable",35:"File locking deadlock error",36:"File or path name too long",37:"No record locks available",38:"Function not implemented",39:"Directory not empty",40:"Too many symbolic links",42:"No message of desired type",43:"Identifier removed",44:"Channel number out of range",45:"Level 2 not synchronized",46:"Level 3 halted",47:"Level 3 reset",48:"Link number out of range",49:"Protocol driver not attached",50:"No CSI structure available",51:"Level 2 halted",52:"Invalid exchange",53:"Invalid request descriptor",54:"Exchange full",55:"No anode",56:"Invalid request code",57:"Invalid slot",59:"Bad font file fmt",60:"Device not a stream",61:"No data (for no delay io)",62:"Timer expired",63:"Out of streams resources",64:"Machine is not on the network",65:"Package not installed",66:"The object is remote",67:"The link has been severed",68:"Advertise error",69:"Srmount error",70:"Communication error on send",71:"Protocol error",72:"Multihop attempted",73:"Cross mount point (not really error)",74:"Trying to read unreadable message",75:"Value too large for defined data type",76:"Given log. name not unique",77:"f.d. invalid for this operation",78:"Remote address changed",79:"Can   access a needed shared lib",80:"Accessing a corrupted shared lib",81:".lib section in a.out corrupted",82:"Attempting to link in too many libs",83:"Attempting to exec a shared library",84:"Illegal byte sequence",86:"Streams pipe error",87:"Too many users",88:"Socket operation on non-socket",89:"Destination address required",90:"Message too long",91:"Protocol wrong type for socket",92:"Protocol not available",93:"Unknown protocol",94:"Socket type not supported",95:"Not supported",96:"Protocol family not supported",97:"Address family not supported by protocol family",98:"Address already in use",99:"Address not available",100:"Network interface is not configured",101:"Network is unreachable",102:"Connection reset by network",103:"Connection aborted",104:"Connection reset by peer",105:"No buffer space available",106:"Socket is already connected",107:"Socket is not connected",108:"Can't send after socket shutdown",109:"Too many references",110:"Connection timed out",111:"Connection refused",112:"Host is down",113:"Host is unreachable",114:"Socket already connected",115:"Connection already in progress",116:"Stale file handle",122:"Quota exceeded",123:"No medium (in tape drive)",125:"Operation canceled",130:"Previous owner died",131:"State not recoverable"};function ___setErrNo(value){if(Module["___errno_location"])HEAP32[Module["___errno_location"]()>>2]=value;return value}var PATH={splitPath:(function(filename){var splitPathRe=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;return splitPathRe.exec(filename).slice(1)}),normalizeArray:(function(parts,allowAboveRoot){var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last==="."){parts.splice(i,1)}else if(last===".."){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift("..")}}return parts}),normalize:(function(path){var isAbsolute=path.charAt(0)==="/",trailingSlash=path.substr(-1)==="/";path=PATH.normalizeArray(path.split("/").filter((function(p){return!!p})),!isAbsolute).join("/");if(!path&&!isAbsolute){path="."}if(path&&trailingSlash){path+="/"}return(isAbsolute?"/":"")+path}),dirname:(function(path){var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return"."}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir}),basename:(function(path){if(path==="/")return"/";var lastSlash=path.lastIndexOf("/");if(lastSlash===-1)return path;return path.substr(lastSlash+1)}),extname:(function(path){return PATH.splitPath(path)[3]}),join:(function(){var paths=Array.prototype.slice.call(arguments,0);return PATH.normalize(paths.join("/"))}),join2:(function(l,r){return PATH.normalize(l+"/"+r)}),resolve:(function(){var resolvedPath="",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!=="string"){throw new TypeError("Arguments to path.resolve must be strings")}else if(!path){return""}resolvedPath=path+"/"+resolvedPath;resolvedAbsolute=path.charAt(0)==="/"}resolvedPath=PATH.normalizeArray(resolvedPath.split("/").filter((function(p){return!!p})),!resolvedAbsolute).join("/");return(resolvedAbsolute?"/":"")+resolvedPath||"."}),relative:(function(from,to){from=PATH.resolve(from).substr(1);to=PATH.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!=="")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!=="")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split("/"));var toParts=trim(to.split("/"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push("..")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join("/")})};var TTY={ttys:[],init:(function(){}),shutdown:(function(){}),register:(function(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)}),stream_ops:{open:(function(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(ERRNO_CODES.ENODEV)}stream.tty=tty;stream.seekable=false}),close:(function(stream){stream.tty.ops.flush(stream.tty)}),flush:(function(stream){stream.tty.ops.flush(stream.tty)}),read:(function(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(ERRNO_CODES.ENXIO)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EIO)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(ERRNO_CODES.EAGAIN)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead}),write:(function(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(ERRNO_CODES.ENXIO)}for(var i=0;i<length;i++){try{stream.tty.ops.put_char(stream.tty,buffer[offset+i])}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EIO)}}if(length){stream.node.timestamp=Date.now()}return i})},default_tty_ops:{get_char:(function(tty){if(!tty.input.length){var result=null;if(ENVIRONMENT_IS_NODE){var BUFSIZE=256;var buf=new Buffer(BUFSIZE);var bytesRead=0;var isPosixPlatform=process.platform!="win32";var fd=process.stdin.fd;if(isPosixPlatform){var usingDevice=false;try{fd=fs.openSync("/dev/stdin","r");usingDevice=true}catch(e){}}try{bytesRead=fs.readSync(fd,buf,0,BUFSIZE,null)}catch(e){if(e.toString().indexOf("EOF")!=-1)bytesRead=0;else throw e}if(usingDevice){fs.closeSync(fd)}if(bytesRead>0){result=buf.slice(0,bytesRead).toString("utf-8")}else{result=null}}else if(typeof window!="undefined"&&typeof window.prompt=="function"){result=window.prompt("Input: ");if(result!==null){result+="\n"}}else if(typeof readline=="function"){result=readline();if(result!==null){result+="\n"}}if(!result){return null}tty.input=intArrayFromString(result,true)}return tty.input.shift()}),put_char:(function(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}}),flush:(function(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}})},default_tty1_ops:{put_char:(function(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}}),flush:(function(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}})}};var MEMFS={ops_table:null,mount:(function(mount){return MEMFS.createNode(null,"/",16384|511,0)}),createNode:(function(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node}return node}),getFileDataAsRegularArray:(function(node){if(node.contents&&node.contents.subarray){var arr=[];for(var i=0;i<node.usedBytes;++i)arr.push(node.contents[i]);return arr}return node.contents}),getFileDataAsTypedArray:(function(node){if(!node.contents)return new Uint8Array;if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)}),expandFileStorage:(function(node,newCapacity){if(node.contents&&node.contents.subarray&&newCapacity>node.contents.length){node.contents=MEMFS.getFileDataAsRegularArray(node);node.usedBytes=node.contents.length}if(!node.contents||node.contents.subarray){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)|0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0);return}if(!node.contents&&newCapacity>0)node.contents=[];while(node.contents.length<newCapacity)node.contents.push(0)}),resizeFileStorage:(function(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0;return}if(!node.contents||node.contents.subarray){var oldContents=node.contents;node.contents=new Uint8Array(new ArrayBuffer(newSize));if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize;return}if(!node.contents)node.contents=[];if(node.contents.length>newSize)node.contents.length=newSize;else while(node.contents.length<newSize)node.contents.push(0);node.usedBytes=newSize}),node_ops:{getattr:(function(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr}),setattr:(function(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}}),lookup:(function(parent,name){throw FS.genericErrors[ERRNO_CODES.ENOENT]}),mknod:(function(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)}),rename:(function(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(ERRNO_CODES.ENOTEMPTY)}}}delete old_node.parent.contents[old_node.name];old_node.name=new_name;new_dir.contents[new_name]=old_node;old_node.parent=new_dir}),unlink:(function(parent,name){delete parent.contents[name]}),rmdir:(function(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(ERRNO_CODES.ENOTEMPTY)}delete parent.contents[name]}),readdir:(function(node){var entries=[".",".."];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries}),symlink:(function(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node}),readlink:(function(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}return node.link})},stream_ops:{read:(function(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);assert(size>=0);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size}),write:(function(stream,buffer,offset,length,position,canOwn){if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=new Uint8Array(buffer.subarray(offset,offset+length));node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray)node.contents.set(buffer.subarray(offset,offset+length),position);else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length}),llseek:(function(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}return position}),allocate:(function(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)}),mmap:(function(stream,buffer,offset,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(ERRNO_CODES.ENODEV)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&(contents.buffer===buffer||contents.buffer===buffer.buffer)){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<stream.node.usedBytes){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=_malloc(length);if(!ptr){throw new FS.ErrnoError(ERRNO_CODES.ENOMEM)}buffer.set(contents,ptr)}return{ptr:ptr,allocated:allocated}}),msync:(function(stream,buffer,offset,length,mmapFlags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(ERRNO_CODES.ENODEV)}if(mmapFlags&2){return 0}var bytesWritten=MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0})}};var IDBFS={dbs:{},indexedDB:(function(){if(typeof indexedDB!=="undefined")return indexedDB;var ret=null;if(typeof window==="object")ret=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB;assert(ret,"IDBFS used, but indexedDB not supported");return ret}),DB_VERSION:21,DB_STORE_NAME:"FILE_DATA",mount:(function(mount){return MEMFS.mount.apply(null,arguments)}),syncfs:(function(mount,populate,callback){IDBFS.getLocalSet(mount,(function(err,local){if(err)return callback(err);IDBFS.getRemoteSet(mount,(function(err,remote){if(err)return callback(err);var src=populate?remote:local;var dst=populate?local:remote;IDBFS.reconcile(src,dst,callback)}))}))}),getDB:(function(name,callback){var db=IDBFS.dbs[name];if(db){return callback(null,db)}var req;try{req=IDBFS.indexedDB().open(name,IDBFS.DB_VERSION)}catch(e){return callback(e)}if(!req){return callback("Unable to connect to IndexedDB")}req.onupgradeneeded=(function(e){var db=e.target.result;var transaction=e.target.transaction;var fileStore;if(db.objectStoreNames.contains(IDBFS.DB_STORE_NAME)){fileStore=transaction.objectStore(IDBFS.DB_STORE_NAME)}else{fileStore=db.createObjectStore(IDBFS.DB_STORE_NAME)}if(!fileStore.indexNames.contains("timestamp")){fileStore.createIndex("timestamp","timestamp",{unique:false})}});req.onsuccess=(function(){db=req.result;IDBFS.dbs[name]=db;callback(null,db)});req.onerror=(function(e){callback(this.error);e.preventDefault()})}),getLocalSet:(function(mount,callback){var entries={};function isRealDir(p){return p!=="."&&p!==".."}function toAbsolute(root){return(function(p){return PATH.join2(root,p)})}var check=FS.readdir(mount.mountpoint).filter(isRealDir).map(toAbsolute(mount.mountpoint));while(check.length){var path=check.pop();var stat;try{stat=FS.stat(path)}catch(e){return callback(e)}if(FS.isDir(stat.mode)){check.push.apply(check,FS.readdir(path).filter(isRealDir).map(toAbsolute(path)))}entries[path]={timestamp:stat.mtime}}return callback(null,{type:"local",entries:entries})}),getRemoteSet:(function(mount,callback){var entries={};IDBFS.getDB(mount.mountpoint,(function(err,db){if(err)return callback(err);try{var transaction=db.transaction([IDBFS.DB_STORE_NAME],"readonly");transaction.onerror=(function(e){callback(this.error);e.preventDefault()});var store=transaction.objectStore(IDBFS.DB_STORE_NAME);var index=store.index("timestamp");index.openKeyCursor().onsuccess=(function(event){var cursor=event.target.result;if(!cursor){return callback(null,{type:"remote",db:db,entries:entries})}entries[cursor.primaryKey]={timestamp:cursor.key};cursor.continue()})}catch(e){return callback(e)}}))}),loadLocalEntry:(function(path,callback){var stat,node;try{var lookup=FS.lookupPath(path);node=lookup.node;stat=FS.stat(path)}catch(e){return callback(e)}if(FS.isDir(stat.mode)){return callback(null,{timestamp:stat.mtime,mode:stat.mode})}else if(FS.isFile(stat.mode)){node.contents=MEMFS.getFileDataAsTypedArray(node);return callback(null,{timestamp:stat.mtime,mode:stat.mode,contents:node.contents})}else{return callback(new Error("node type not supported"))}}),storeLocalEntry:(function(path,entry,callback){try{if(FS.isDir(entry.mode)){FS.mkdir(path,entry.mode)}else if(FS.isFile(entry.mode)){FS.writeFile(path,entry.contents,{canOwn:true})}else{return callback(new Error("node type not supported"))}FS.chmod(path,entry.mode);FS.utime(path,entry.timestamp,entry.timestamp)}catch(e){return callback(e)}callback(null)}),removeLocalEntry:(function(path,callback){try{var lookup=FS.lookupPath(path);var stat=FS.stat(path);if(FS.isDir(stat.mode)){FS.rmdir(path)}else if(FS.isFile(stat.mode)){FS.unlink(path)}}catch(e){return callback(e)}callback(null)}),loadRemoteEntry:(function(store,path,callback){var req=store.get(path);req.onsuccess=(function(event){callback(null,event.target.result)});req.onerror=(function(e){callback(this.error);e.preventDefault()})}),storeRemoteEntry:(function(store,path,entry,callback){var req=store.put(entry,path);req.onsuccess=(function(){callback(null)});req.onerror=(function(e){callback(this.error);e.preventDefault()})}),removeRemoteEntry:(function(store,path,callback){var req=store.delete(path);req.onsuccess=(function(){callback(null)});req.onerror=(function(e){callback(this.error);e.preventDefault()})}),reconcile:(function(src,dst,callback){var total=0;var create=[];Object.keys(src.entries).forEach((function(key){var e=src.entries[key];var e2=dst.entries[key];if(!e2||e.timestamp>e2.timestamp){create.push(key);total++}}));var remove=[];Object.keys(dst.entries).forEach((function(key){var e=dst.entries[key];var e2=src.entries[key];if(!e2){remove.push(key);total++}}));if(!total){return callback(null)}var completed=0;var db=src.type==="remote"?src.db:dst.db;var transaction=db.transaction([IDBFS.DB_STORE_NAME],"readwrite");var store=transaction.objectStore(IDBFS.DB_STORE_NAME);function done(err){if(err){if(!done.errored){done.errored=true;return callback(err)}return}if(++completed>=total){return callback(null)}}transaction.onerror=(function(e){done(this.error);e.preventDefault()});create.sort().forEach((function(path){if(dst.type==="local"){IDBFS.loadRemoteEntry(store,path,(function(err,entry){if(err)return done(err);IDBFS.storeLocalEntry(path,entry,done)}))}else{IDBFS.loadLocalEntry(path,(function(err,entry){if(err)return done(err);IDBFS.storeRemoteEntry(store,path,entry,done)}))}}));remove.sort().reverse().forEach((function(path){if(dst.type==="local"){IDBFS.removeLocalEntry(path,done)}else{IDBFS.removeRemoteEntry(store,path,done)}}))})};var NODEFS={isWindows:false,staticInit:(function(){NODEFS.isWindows=!!process.platform.match(/^win/);var flags=process["binding"]("constants");if(flags["fs"]){flags=flags["fs"]}NODEFS.flagsForNodeMap={"1024":flags["O_APPEND"],"64":flags["O_CREAT"],"128":flags["O_EXCL"],"0":flags["O_RDONLY"],"2":flags["O_RDWR"],"4096":flags["O_SYNC"],"512":flags["O_TRUNC"],"1":flags["O_WRONLY"]}}),bufferFrom:(function(arrayBuffer){return Buffer.alloc?Buffer.from(arrayBuffer):new Buffer(arrayBuffer)}),mount:(function(mount){assert(ENVIRONMENT_IS_NODE);return NODEFS.createNode(null,"/",NODEFS.getMode(mount.opts.root),0)}),createNode:(function(parent,name,mode,dev){if(!FS.isDir(mode)&&!FS.isFile(mode)&&!FS.isLink(mode)){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}var node=FS.createNode(parent,name,mode);node.node_ops=NODEFS.node_ops;node.stream_ops=NODEFS.stream_ops;return node}),getMode:(function(path){var stat;try{stat=fs.lstatSync(path);if(NODEFS.isWindows){stat.mode=stat.mode|(stat.mode&292)>>2}}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}return stat.mode}),realPath:(function(node){var parts=[];while(node.parent!==node){parts.push(node.name);node=node.parent}parts.push(node.mount.opts.root);parts.reverse();return PATH.join.apply(null,parts)}),flagsForNode:(function(flags){flags&=~2097152;flags&=~2048;flags&=~32768;flags&=~524288;var newFlags=0;for(var k in NODEFS.flagsForNodeMap){if(flags&k){newFlags|=NODEFS.flagsForNodeMap[k];flags^=k}}if(!flags){return newFlags}else{throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}}),node_ops:{getattr:(function(node){var path=NODEFS.realPath(node);var stat;try{stat=fs.lstatSync(path)}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}if(NODEFS.isWindows&&!stat.blksize){stat.blksize=4096}if(NODEFS.isWindows&&!stat.blocks){stat.blocks=(stat.size+stat.blksize-1)/stat.blksize|0}return{dev:stat.dev,ino:stat.ino,mode:stat.mode,nlink:stat.nlink,uid:stat.uid,gid:stat.gid,rdev:stat.rdev,size:stat.size,atime:stat.atime,mtime:stat.mtime,ctime:stat.ctime,blksize:stat.blksize,blocks:stat.blocks}}),setattr:(function(node,attr){var path=NODEFS.realPath(node);try{if(attr.mode!==undefined){fs.chmodSync(path,attr.mode);node.mode=attr.mode}if(attr.timestamp!==undefined){var date=new Date(attr.timestamp);fs.utimesSync(path,date,date)}if(attr.size!==undefined){fs.truncateSync(path,attr.size)}}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),lookup:(function(parent,name){var path=PATH.join2(NODEFS.realPath(parent),name);var mode=NODEFS.getMode(path);return NODEFS.createNode(parent,name,mode)}),mknod:(function(parent,name,mode,dev){var node=NODEFS.createNode(parent,name,mode,dev);var path=NODEFS.realPath(node);try{if(FS.isDir(node.mode)){fs.mkdirSync(path,node.mode)}else{fs.writeFileSync(path,"",{mode:node.mode})}}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}return node}),rename:(function(oldNode,newDir,newName){var oldPath=NODEFS.realPath(oldNode);var newPath=PATH.join2(NODEFS.realPath(newDir),newName);try{fs.renameSync(oldPath,newPath)}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),unlink:(function(parent,name){var path=PATH.join2(NODEFS.realPath(parent),name);try{fs.unlinkSync(path)}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),rmdir:(function(parent,name){var path=PATH.join2(NODEFS.realPath(parent),name);try{fs.rmdirSync(path)}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),readdir:(function(node){var path=NODEFS.realPath(node);try{return fs.readdirSync(path)}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),symlink:(function(parent,newName,oldPath){var newPath=PATH.join2(NODEFS.realPath(parent),newName);try{fs.symlinkSync(oldPath,newPath)}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),readlink:(function(node){var path=NODEFS.realPath(node);try{path=fs.readlinkSync(path);path=NODEJS_PATH.relative(NODEJS_PATH.resolve(node.mount.opts.root),path);return path}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}})},stream_ops:{open:(function(stream){var path=NODEFS.realPath(stream.node);try{if(FS.isFile(stream.node.mode)){stream.nfd=fs.openSync(path,NODEFS.flagsForNode(stream.flags))}}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),close:(function(stream){try{if(FS.isFile(stream.node.mode)&&stream.nfd){fs.closeSync(stream.nfd)}}catch(e){if(!e.code)throw e;throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),read:(function(stream,buffer,offset,length,position){if(length===0)return 0;try{return fs.readSync(stream.nfd,NODEFS.bufferFrom(buffer.buffer),offset,length,position)}catch(e){throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),write:(function(stream,buffer,offset,length,position){try{return fs.writeSync(stream.nfd,NODEFS.bufferFrom(buffer.buffer),offset,length,position)}catch(e){throw new FS.ErrnoError(ERRNO_CODES[e.code])}}),llseek:(function(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){try{var stat=fs.fstatSync(stream.nfd);position+=stat.size}catch(e){throw new FS.ErrnoError(ERRNO_CODES[e.code])}}}if(position<0){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}return position})}};var WORKERFS={DIR_MODE:16895,FILE_MODE:33279,reader:null,mount:(function(mount){assert(ENVIRONMENT_IS_WORKER);if(!WORKERFS.reader)WORKERFS.reader=new FileReaderSync;var root=WORKERFS.createNode(null,"/",WORKERFS.DIR_MODE,0);var createdParents={};function ensureParent(path){var parts=path.split("/");var parent=root;for(var i=0;i<parts.length-1;i++){var curr=parts.slice(0,i+1).join("/");if(!createdParents[curr]){createdParents[curr]=WORKERFS.createNode(parent,parts[i],WORKERFS.DIR_MODE,0)}parent=createdParents[curr]}return parent}function base(path){var parts=path.split("/");return parts[parts.length-1]}Array.prototype.forEach.call(mount.opts["files"]||[],(function(file){WORKERFS.createNode(ensureParent(file.name),base(file.name),WORKERFS.FILE_MODE,0,file,file.lastModifiedDate)}));(mount.opts["blobs"]||[]).forEach((function(obj){WORKERFS.createNode(ensureParent(obj["name"]),base(obj["name"]),WORKERFS.FILE_MODE,0,obj["data"])}));(mount.opts["packages"]||[]).forEach((function(pack){pack["metadata"].files.forEach((function(file){var name=file.filename.substr(1);WORKERFS.createNode(ensureParent(name),base(name),WORKERFS.FILE_MODE,0,pack["blob"].slice(file.start,file.end))}))}));return root}),createNode:(function(parent,name,mode,dev,contents,mtime){var node=FS.createNode(parent,name,mode);node.mode=mode;node.node_ops=WORKERFS.node_ops;node.stream_ops=WORKERFS.stream_ops;node.timestamp=(mtime||new Date).getTime();assert(WORKERFS.FILE_MODE!==WORKERFS.DIR_MODE);if(mode===WORKERFS.FILE_MODE){node.size=contents.size;node.contents=contents}else{node.size=4096;node.contents={}}if(parent){parent.contents[name]=node}return node}),node_ops:{getattr:(function(node){return{dev:1,ino:undefined,mode:node.mode,nlink:1,uid:0,gid:0,rdev:undefined,size:node.size,atime:new Date(node.timestamp),mtime:new Date(node.timestamp),ctime:new Date(node.timestamp),blksize:4096,blocks:Math.ceil(node.size/4096)}}),setattr:(function(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}}),lookup:(function(parent,name){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)}),mknod:(function(parent,name,mode,dev){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}),rename:(function(oldNode,newDir,newName){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}),unlink:(function(parent,name){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}),rmdir:(function(parent,name){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}),readdir:(function(node){var entries=[".",".."];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries}),symlink:(function(parent,newName,oldPath){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}),readlink:(function(node){throw new FS.ErrnoError(ERRNO_CODES.EPERM)})},stream_ops:{read:(function(stream,buffer,offset,length,position){if(position>=stream.node.size)return 0;var chunk=stream.node.contents.slice(position,position+length);var ab=WORKERFS.reader.readAsArrayBuffer(chunk);buffer.set(new Uint8Array(ab),offset);return chunk.size}),write:(function(stream,buffer,offset,length,position){throw new FS.ErrnoError(ERRNO_CODES.EIO)}),llseek:(function(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.size}}if(position<0){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}return position})}};STATICTOP+=16;STATICTOP+=16;STATICTOP+=16;var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,handleFSError:(function(e){if(!(e instanceof FS.ErrnoError))throw e+" : "+stackTrace();return ___setErrNo(e.errno)}),lookupPath:(function(path,opts){path=PATH.resolve(FS.cwd(),path);opts=opts||{};if(!path)return{path:"",node:null};var defaults={follow_mount:true,recurse_count:0};for(var key in defaults){if(opts[key]===undefined){opts[key]=defaults[key]}}if(opts.recurse_count>8){throw new FS.ErrnoError(ERRNO_CODES.ELOOP)}var parts=PATH.normalizeArray(path.split("/").filter((function(p){return!!p})),false);var current=FS.root;var current_path="/";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count});current=lookup.node;if(count++>40){throw new FS.ErrnoError(ERRNO_CODES.ELOOP)}}}}return{path:current_path,node:current}}),getPath:(function(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!=="/"?mount+"/"+path:mount+path}path=path?node.name+"/"+path:node.name;node=node.parent}}),hashName:(function(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length}),hashAddNode:(function(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node}),hashRemoveNode:(function(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}}),lookupNode:(function(parent,name){var err=FS.mayLookup(parent);if(err){throw new FS.ErrnoError(err,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)}),createNode:(function(parent,name,mode,rdev){if(!FS.FSNode){FS.FSNode=(function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev});FS.FSNode.prototype={};var readMode=292|73;var writeMode=146;Object.defineProperties(FS.FSNode.prototype,{read:{get:(function(){return(this.mode&readMode)===readMode}),set:(function(val){val?this.mode|=readMode:this.mode&=~readMode})},write:{get:(function(){return(this.mode&writeMode)===writeMode}),set:(function(val){val?this.mode|=writeMode:this.mode&=~writeMode})},isFolder:{get:(function(){return FS.isDir(this.mode)})},isDevice:{get:(function(){return FS.isChrdev(this.mode)})}})}var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node}),destroyNode:(function(node){FS.hashRemoveNode(node)}),isRoot:(function(node){return node===node.parent}),isMountpoint:(function(node){return!!node.mounted}),isFile:(function(mode){return(mode&61440)===32768}),isDir:(function(mode){return(mode&61440)===16384}),isLink:(function(mode){return(mode&61440)===40960}),isChrdev:(function(mode){return(mode&61440)===8192}),isBlkdev:(function(mode){return(mode&61440)===24576}),isFIFO:(function(mode){return(mode&61440)===4096}),isSocket:(function(mode){return(mode&49152)===49152}),flagModes:{"r":0,"rs":1052672,"r+":2,"w":577,"wx":705,"xw":705,"w+":578,"wx+":706,"xw+":706,"a":1089,"ax":1217,"xa":1217,"a+":1090,"ax+":1218,"xa+":1218},modeStringToFlags:(function(str){var flags=FS.flagModes[str];if(typeof flags==="undefined"){throw new Error("Unknown file open mode: "+str)}return flags}),flagsToPermissionString:(function(flag){var perms=["r","w","rw"][flag&3];if(flag&512){perms+="w"}return perms}),nodePermissions:(function(node,perms){if(FS.ignorePermissions){return 0}if(perms.indexOf("r")!==-1&&!(node.mode&292)){return ERRNO_CODES.EACCES}else if(perms.indexOf("w")!==-1&&!(node.mode&146)){return ERRNO_CODES.EACCES}else if(perms.indexOf("x")!==-1&&!(node.mode&73)){return ERRNO_CODES.EACCES}return 0}),mayLookup:(function(dir){var err=FS.nodePermissions(dir,"x");if(err)return err;if(!dir.node_ops.lookup)return ERRNO_CODES.EACCES;return 0}),mayCreate:(function(dir,name){try{var node=FS.lookupNode(dir,name);return ERRNO_CODES.EEXIST}catch(e){}return FS.nodePermissions(dir,"wx")}),mayDelete:(function(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var err=FS.nodePermissions(dir,"wx");if(err){return err}if(isdir){if(!FS.isDir(node.mode)){return ERRNO_CODES.ENOTDIR}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return ERRNO_CODES.EBUSY}}else{if(FS.isDir(node.mode)){return ERRNO_CODES.EISDIR}}return 0}),mayOpen:(function(node,flags){if(!node){return ERRNO_CODES.ENOENT}if(FS.isLink(node.mode)){return ERRNO_CODES.ELOOP}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!=="r"||flags&512){return ERRNO_CODES.EISDIR}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))}),MAX_OPEN_FDS:4096,nextfd:(function(fd_start,fd_end){fd_start=fd_start||0;fd_end=fd_end||FS.MAX_OPEN_FDS;for(var fd=fd_start;fd<=fd_end;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(ERRNO_CODES.EMFILE)}),getStream:(function(fd){return FS.streams[fd]}),createStream:(function(stream,fd_start,fd_end){if(!FS.FSStream){FS.FSStream=(function(){});FS.FSStream.prototype={};Object.defineProperties(FS.FSStream.prototype,{object:{get:(function(){return this.node}),set:(function(val){this.node=val})},isRead:{get:(function(){return(this.flags&2097155)!==1})},isWrite:{get:(function(){return(this.flags&2097155)!==0})},isAppend:{get:(function(){return this.flags&1024})}})}var newStream=new FS.FSStream;for(var p in stream){newStream[p]=stream[p]}stream=newStream;var fd=FS.nextfd(fd_start,fd_end);stream.fd=fd;FS.streams[fd]=stream;return stream}),closeStream:(function(fd){FS.streams[fd]=null}),chrdev_stream_ops:{open:(function(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}}),llseek:(function(){throw new FS.ErrnoError(ERRNO_CODES.ESPIPE)})},major:(function(dev){return dev>>8}),minor:(function(dev){return dev&255}),makedev:(function(ma,mi){return ma<<8|mi}),registerDevice:(function(dev,ops){FS.devices[dev]={stream_ops:ops}}),getDevice:(function(dev){return FS.devices[dev]}),getMounts:(function(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts}),syncfs:(function(populate,callback){if(typeof populate==="function"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){console.log("warning: "+FS.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work")}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(err){assert(FS.syncFSRequests>0);FS.syncFSRequests--;return callback(err)}function done(err){if(err){if(!done.errored){done.errored=true;return doCallback(err)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach((function(mount){if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)}))}),mount:(function(type,opts,mountpoint){var root=mountpoint==="/";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(ERRNO_CODES.EBUSY)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(ERRNO_CODES.EBUSY)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(ERRNO_CODES.ENOTDIR)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot}),unmount:(function(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach((function(hash){var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.indexOf(current.mount)!==-1){FS.destroyNode(current)}current=next}}));node.mounted=null;var idx=node.mount.mounts.indexOf(mount);assert(idx!==-1);node.mount.mounts.splice(idx,1)}),lookup:(function(parent,name){return parent.node_ops.lookup(parent,name)}),mknod:(function(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name==="."||name===".."){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}var err=FS.mayCreate(parent,name);if(err){throw new FS.ErrnoError(err)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}return parent.node_ops.mknod(parent,name,mode,dev)}),create:(function(path,mode){mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)}),mkdir:(function(path,mode){mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)}),mkdirTree:(function(path,mode){var dirs=path.split("/");var d="";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+="/"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=ERRNO_CODES.EEXIST)throw e}}}),mkdev:(function(path,mode,dev){if(typeof dev==="undefined"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)}),symlink:(function(oldpath,newpath){if(!PATH.resolve(oldpath)){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)}var newname=PATH.basename(newpath);var err=FS.mayCreate(parent,newname);if(err){throw new FS.ErrnoError(err)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}return parent.node_ops.symlink(parent,newname,oldpath)}),rename:(function(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;try{lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EBUSY)}if(!old_dir||!new_dir)throw new FS.ErrnoError(ERRNO_CODES.ENOENT);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(ERRNO_CODES.EXDEV)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH.relative(old_path,new_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}relative=PATH.relative(new_path,old_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(ERRNO_CODES.ENOTEMPTY)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var err=FS.mayDelete(old_dir,old_name,isdir);if(err){throw new FS.ErrnoError(err)}err=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(err){throw new FS.ErrnoError(err)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(ERRNO_CODES.EBUSY)}if(new_dir!==old_dir){err=FS.nodePermissions(old_dir,"w");if(err){throw new FS.ErrnoError(err)}}try{if(FS.trackingDelegate["willMovePath"]){FS.trackingDelegate["willMovePath"](old_path,new_path)}}catch(e){console.log("FS.trackingDelegate['willMovePath']('"+old_path+"', '"+new_path+"') threw an exception: "+e.message)}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}try{if(FS.trackingDelegate["onMovePath"])FS.trackingDelegate["onMovePath"](old_path,new_path)}catch(e){console.log("FS.trackingDelegate['onMovePath']('"+old_path+"', '"+new_path+"') threw an exception: "+e.message)}}),rmdir:(function(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var err=FS.mayDelete(parent,name,true);if(err){throw new FS.ErrnoError(err)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(ERRNO_CODES.EBUSY)}try{if(FS.trackingDelegate["willDeletePath"]){FS.trackingDelegate["willDeletePath"](path)}}catch(e){console.log("FS.trackingDelegate['willDeletePath']('"+path+"') threw an exception: "+e.message)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node);try{if(FS.trackingDelegate["onDeletePath"])FS.trackingDelegate["onDeletePath"](path)}catch(e){console.log("FS.trackingDelegate['onDeletePath']('"+path+"') threw an exception: "+e.message)}}),readdir:(function(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(ERRNO_CODES.ENOTDIR)}return node.node_ops.readdir(node)}),unlink:(function(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var err=FS.mayDelete(parent,name,false);if(err){throw new FS.ErrnoError(err)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(ERRNO_CODES.EBUSY)}try{if(FS.trackingDelegate["willDeletePath"]){FS.trackingDelegate["willDeletePath"](path)}}catch(e){console.log("FS.trackingDelegate['willDeletePath']('"+path+"') threw an exception: "+e.message)}parent.node_ops.unlink(parent,name);FS.destroyNode(node);try{if(FS.trackingDelegate["onDeletePath"])FS.trackingDelegate["onDeletePath"](path)}catch(e){console.log("FS.trackingDelegate['onDeletePath']('"+path+"') threw an exception: "+e.message)}}),readlink:(function(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)}if(!link.node_ops.readlink){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}return PATH.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))}),stat:(function(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)}if(!node.node_ops.getattr){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}return node.node_ops.getattr(node)}),lstat:(function(path){return FS.stat(path,true)}),chmod:(function(path,mode,dontFollow){var node;if(typeof path==="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})}),lchmod:(function(path,mode){FS.chmod(path,mode,true)}),fchmod:(function(fd,mode){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}FS.chmod(stream.node,mode)}),chown:(function(path,uid,gid,dontFollow){var node;if(typeof path==="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}node.node_ops.setattr(node,{timestamp:Date.now()})}),lchown:(function(path,uid,gid){FS.chown(path,uid,gid,true)}),fchown:(function(fd,uid,gid){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}FS.chown(stream.node,uid,gid)}),truncate:(function(path,len){if(len<0){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}var node;if(typeof path==="string"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(ERRNO_CODES.EISDIR)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}var err=FS.nodePermissions(node,"w");if(err){throw new FS.ErrnoError(err)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})}),ftruncate:(function(fd,len){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}FS.truncate(stream.node,len)}),utime:(function(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})}),open:(function(path,flags,mode,fd_start,fd_end){if(path===""){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)}flags=typeof flags==="string"?FS.modeStringToFlags(flags):flags;mode=typeof mode==="undefined"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path==="object"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(ERRNO_CODES.EEXIST)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(ERRNO_CODES.ENOTDIR)}if(!created){var err=FS.mayOpen(node,flags);if(err){throw new FS.ErrnoError(err)}}if(flags&512){FS.truncate(node,0)}flags&=~(128|512);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false},fd_start,fd_end);if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module["logReadFiles"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1;err("read file: "+path)}}try{if(FS.trackingDelegate["onOpenFile"]){var trackingFlags=0;if((flags&2097155)!==1){trackingFlags|=FS.tracking.openFlags.READ}if((flags&2097155)!==0){trackingFlags|=FS.tracking.openFlags.WRITE}FS.trackingDelegate["onOpenFile"](path,trackingFlags)}}catch(e){console.log("FS.trackingDelegate['onOpenFile']('"+path+"', flags) threw an exception: "+e.message)}return stream}),close:(function(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null}),isClosed:(function(stream){return stream.fd===null}),llseek:(function(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(ERRNO_CODES.ESPIPE)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position}),read:(function(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}if(FS.isClosed(stream)){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(ERRNO_CODES.EISDIR)}if(!stream.stream_ops.read){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}var seeking=typeof position!=="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(ERRNO_CODES.ESPIPE)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead}),write:(function(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}if(FS.isClosed(stream)){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(ERRNO_CODES.EISDIR)}if(!stream.stream_ops.write){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}if(stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!=="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(ERRNO_CODES.ESPIPE)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;try{if(stream.path&&FS.trackingDelegate["onWriteToFile"])FS.trackingDelegate["onWriteToFile"](stream.path)}catch(e){console.log("FS.trackingDelegate['onWriteToFile']('"+path+"') threw an exception: "+e.message)}return bytesWritten}),allocate:(function(stream,offset,length){if(FS.isClosed(stream)){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if(offset<0||length<=0){throw new FS.ErrnoError(ERRNO_CODES.EINVAL)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(ERRNO_CODES.EBADF)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(ERRNO_CODES.ENODEV)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(ERRNO_CODES.EOPNOTSUPP)}stream.stream_ops.allocate(stream,offset,length)}),mmap:(function(stream,buffer,offset,length,position,prot,flags){if((stream.flags&2097155)===1){throw new FS.ErrnoError(ERRNO_CODES.EACCES)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(ERRNO_CODES.ENODEV)}return stream.stream_ops.mmap(stream,buffer,offset,length,position,prot,flags)}),msync:(function(stream,buffer,offset,length,mmapFlags){if(!stream||!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)}),munmap:(function(stream){return 0}),ioctl:(function(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(ERRNO_CODES.ENOTTY)}return stream.stream_ops.ioctl(stream,cmd,arg)}),readFile:(function(path,opts){opts=opts||{};opts.flags=opts.flags||"r";opts.encoding=opts.encoding||"binary";if(opts.encoding!=="utf8"&&opts.encoding!=="binary"){throw new Error('Invalid encoding type "'+opts.encoding+'"')}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding==="utf8"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding==="binary"){ret=buf}FS.close(stream);return ret}),writeFile:(function(path,data,opts){opts=opts||{};opts.flags=opts.flags||"w";var stream=FS.open(path,opts.flags,opts.mode);if(typeof data==="string"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error("Unsupported data type")}FS.close(stream)}),cwd:(function(){return FS.currentPath}),chdir:(function(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(ERRNO_CODES.ENOTDIR)}var err=FS.nodePermissions(lookup.node,"x");if(err){throw new FS.ErrnoError(err)}FS.currentPath=lookup.path}),createDefaultDirectories:(function(){FS.mkdir("/tmp");FS.mkdir("/home");FS.mkdir("/home/<USER>")}),createDefaultDevices:(function(){FS.mkdir("/dev");FS.registerDevice(FS.makedev(1,3),{read:(function(){return 0}),write:(function(stream,buffer,offset,length,pos){return length})});FS.mkdev("/dev/null",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev("/dev/tty",FS.makedev(5,0));FS.mkdev("/dev/tty1",FS.makedev(6,0));var random_device;if(typeof crypto!=="undefined"){var randomBuffer=new Uint8Array(1);random_device=(function(){crypto.getRandomValues(randomBuffer);return randomBuffer[0]})}else if(ENVIRONMENT_IS_NODE){random_device=(function(){return require("crypto")["randomBytes"](1)[0]})}else{random_device=(function(){return Math.random()*256|0})}FS.createDevice("/dev","random",random_device);FS.createDevice("/dev","urandom",random_device);FS.mkdir("/dev/shm");FS.mkdir("/dev/shm/tmp")}),createSpecialDirectories:(function(){FS.mkdir("/proc");FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd");FS.mount({mount:(function(){var node=FS.createNode("/proc/self","fd",16384|511,73);node.node_ops={lookup:(function(parent,name){var fd=+name;var stream=FS.getStream(fd);if(!stream)throw new FS.ErrnoError(ERRNO_CODES.EBADF);var ret={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:(function(){return stream.path})}};ret.parent=ret;return ret})};return node})},{},"/proc/self/fd")}),createStandardStreams:(function(){if(Module["stdin"]){FS.createDevice("/dev","stdin",Module["stdin"])}else{FS.symlink("/dev/tty","/dev/stdin")}if(Module["stdout"]){FS.createDevice("/dev","stdout",null,Module["stdout"])}else{FS.symlink("/dev/tty","/dev/stdout")}if(Module["stderr"]){FS.createDevice("/dev","stderr",null,Module["stderr"])}else{FS.symlink("/dev/tty1","/dev/stderr")}var stdin=FS.open("/dev/stdin","r");assert(stdin.fd===0,"invalid handle for stdin ("+stdin.fd+")");var stdout=FS.open("/dev/stdout","w");assert(stdout.fd===1,"invalid handle for stdout ("+stdout.fd+")");var stderr=FS.open("/dev/stderr","w");assert(stderr.fd===2,"invalid handle for stderr ("+stderr.fd+")")}),ensureErrnoError:(function(){if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.node=node;this.setErrno=(function(errno){this.errno=errno;for(var key in ERRNO_CODES){if(ERRNO_CODES[key]===errno){this.code=key;break}}});this.setErrno(errno);this.message=ERRNO_MESSAGES[errno];if(this.stack)Object.defineProperty(this,"stack",{value:(new Error).stack,writable:true})};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[ERRNO_CODES.ENOENT].forEach((function(code){FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack="<generic error, no stack>"}))}),staticInit:(function(){FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},"/");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={"MEMFS":MEMFS,"IDBFS":IDBFS,"NODEFS":NODEFS,"WORKERFS":WORKERFS}}),init:(function(input,output,error){assert(!FS.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)");FS.init.initialized=true;FS.ensureErrnoError();Module["stdin"]=input||Module["stdin"];Module["stdout"]=output||Module["stdout"];Module["stderr"]=error||Module["stderr"];FS.createStandardStreams()}),quit:(function(){FS.init.initialized=false;var fflush=Module["_fflush"];if(fflush)fflush(0);for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}}),getMode:(function(canRead,canWrite){var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode}),joinPath:(function(parts,forceRelative){var path=PATH.join.apply(null,parts);if(forceRelative&&path[0]=="/")path=path.substr(1);return path}),absolutePath:(function(relative,base){return PATH.resolve(base,relative)}),standardizePath:(function(path){return PATH.normalize(path)}),findObject:(function(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(ret.exists){return ret.object}else{___setErrNo(ret.error);return null}}),analyzePath:(function(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path==="/"}catch(e){ret.error=e.errno}return ret}),createFolder:(function(parent,name,canRead,canWrite){var path=PATH.join2(typeof parent==="string"?parent:FS.getPath(parent),name);var mode=FS.getMode(canRead,canWrite);return FS.mkdir(path,mode)}),createPath:(function(parent,path,canRead,canWrite){parent=typeof parent==="string"?parent:FS.getPath(parent);var parts=path.split("/").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current}),createFile:(function(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent==="string"?parent:FS.getPath(parent),name);var mode=FS.getMode(canRead,canWrite);return FS.create(path,mode)}),createDataFile:(function(parent,name,data,canRead,canWrite,canOwn){var path=name?PATH.join2(typeof parent==="string"?parent:FS.getPath(parent),name):parent;var mode=FS.getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data==="string"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,"w");FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}return node}),createDevice:(function(parent,name,input,output){var path=PATH.join2(typeof parent==="string"?parent:FS.getPath(parent),name);var mode=FS.getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open:(function(stream){stream.seekable=false}),close:(function(stream){if(output&&output.buffer&&output.buffer.length){output(10)}}),read:(function(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EIO)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(ERRNO_CODES.EAGAIN)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead}),write:(function(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EIO)}}if(length){stream.node.timestamp=Date.now()}return i})});return FS.mkdev(path,mode,dev)}),createLink:(function(parent,name,target,canRead,canWrite){var path=PATH.join2(typeof parent==="string"?parent:FS.getPath(parent),name);return FS.symlink(target,path)}),forceLoadFile:(function(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;var success=true;if(typeof XMLHttpRequest!=="undefined"){throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")}else if(Module["read"]){try{obj.contents=intArrayFromString(Module["read"](obj.url),true);obj.usedBytes=obj.contents.length}catch(e){success=false}}else{throw new Error("Cannot load without read() or XMLHttpRequest.")}if(!success)___setErrNo(ERRNO_CODES.EIO);return success}),createLazyFile:(function(parent,name,url,canRead,canWrite){function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open("HEAD",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);var datalength=Number(xhr.getResponseHeader("Content-length"));var header;var hasByteServing=(header=xhr.getResponseHeader("Accept-Ranges"))&&header==="bytes";var usesGzip=(header=xhr.getResponseHeader("Content-Encoding"))&&header==="gzip";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(function(from,to){if(from>to)throw new Error("invalid range ("+from+", "+to+") or no bytes requested!");if(to>datalength-1)throw new Error("only "+datalength+" bytes available! programmer error!");var xhr=new XMLHttpRequest;xhr.open("GET",url,false);if(datalength!==chunkSize)xhr.setRequestHeader("Range","bytes="+from+"-"+to);if(typeof Uint8Array!="undefined")xhr.responseType="arraybuffer";if(xhr.overrideMimeType){xhr.overrideMimeType("text/plain; charset=x-user-defined")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}else{return intArrayFromString(xhr.responseText||"",true)}});var lazyArray=this;lazyArray.setDataGetter((function(chunkNum){var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]==="undefined"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]==="undefined")throw new Error("doXHR failed!");return lazyArray.chunks[chunkNum]}));if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;console.log("LazyFiles on gzip forces download of the whole file when length is accessed")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!=="undefined"){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:(function(){if(!this.lengthKnown){this.cacheLength()}return this._length})},chunkSize:{get:(function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize})}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:(function(){return this.contents.length})}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach((function(key){var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){if(!FS.forceLoadFile(node)){throw new FS.ErrnoError(ERRNO_CODES.EIO)}return fn.apply(null,arguments)}}));stream_ops.read=function stream_ops_read(stream,buffer,offset,length,position){if(!FS.forceLoadFile(node)){throw new FS.ErrnoError(ERRNO_CODES.EIO)}var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);assert(size>=0);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size};node.stream_ops=stream_ops;return node}),createPreloadedFile:(function(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish){Browser.init();var fullname=name?PATH.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency("cp "+fullname);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS.createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}var handled=false;Module["preloadPlugins"].forEach((function(plugin){if(handled)return;if(plugin["canHandle"](fullname)){plugin["handle"](byteArray,fullname,finish,(function(){if(onerror)onerror();removeRunDependency(dep)}));handled=true}}));if(!handled)finish(byteArray)}addRunDependency(dep);if(typeof url=="string"){Browser.asyncLoad(url,(function(byteArray){processData(byteArray)}),onerror)}else{processData(url)}}),indexedDB:(function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB}),DB_NAME:(function(){return"EM_FS_"+window.location.pathname}),DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(function(paths,onload,onerror){onload=onload||(function(){});onerror=onerror||(function(){});var indexedDB=FS.indexedDB();try{var openRequest=indexedDB.open(FS.DB_NAME(),FS.DB_VERSION)}catch(e){return onerror(e)}openRequest.onupgradeneeded=function openRequest_onupgradeneeded(){console.log("creating db");var db=openRequest.result;db.createObjectStore(FS.DB_STORE_NAME)};openRequest.onsuccess=function openRequest_onsuccess(){var db=openRequest.result;var transaction=db.transaction([FS.DB_STORE_NAME],"readwrite");var files=transaction.objectStore(FS.DB_STORE_NAME);var ok=0,fail=0,total=paths.length;function finish(){if(fail==0)onload();else onerror()}paths.forEach((function(path){var putRequest=files.put(FS.analyzePath(path).object.contents,path);putRequest.onsuccess=function putRequest_onsuccess(){ok++;if(ok+fail==total)finish()};putRequest.onerror=function putRequest_onerror(){fail++;if(ok+fail==total)finish()}}));transaction.onerror=onerror};openRequest.onerror=onerror}),loadFilesFromDB:(function(paths,onload,onerror){onload=onload||(function(){});onerror=onerror||(function(){});var indexedDB=FS.indexedDB();try{var openRequest=indexedDB.open(FS.DB_NAME(),FS.DB_VERSION)}catch(e){return onerror(e)}openRequest.onupgradeneeded=onerror;openRequest.onsuccess=function openRequest_onsuccess(){var db=openRequest.result;try{var transaction=db.transaction([FS.DB_STORE_NAME],"readonly")}catch(e){onerror(e);return}var files=transaction.objectStore(FS.DB_STORE_NAME);var ok=0,fail=0,total=paths.length;function finish(){if(fail==0)onload();else onerror()}paths.forEach((function(path){var getRequest=files.get(path);getRequest.onsuccess=function getRequest_onsuccess(){if(FS.analyzePath(path).exists){FS.unlink(path)}FS.createDataFile(PATH.dirname(path),PATH.basename(path),getRequest.result,true,true,true);ok++;if(ok+fail==total)finish()};getRequest.onerror=function getRequest_onerror(){fail++;if(ok+fail==total)finish()}}));transaction.onerror=onerror};openRequest.onerror=onerror})};var SYSCALLS={DEFAULT_POLLMASK:5,mappings:{},umask:511,calculateAt:(function(dirfd,path){if(path[0]!=="/"){var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=FS.getStream(dirfd);if(!dirstream)throw new FS.ErrnoError(ERRNO_CODES.EBADF);dir=dirstream.path}path=PATH.join2(dir,path)}return path}),doStat:(function(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-ERRNO_CODES.ENOTDIR}throw e}HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=0;HEAP32[buf+8>>2]=stat.ino;HEAP32[buf+12>>2]=stat.mode;HEAP32[buf+16>>2]=stat.nlink;HEAP32[buf+20>>2]=stat.uid;HEAP32[buf+24>>2]=stat.gid;HEAP32[buf+28>>2]=stat.rdev;HEAP32[buf+32>>2]=0;HEAP32[buf+36>>2]=stat.size;HEAP32[buf+40>>2]=4096;HEAP32[buf+44>>2]=stat.blocks;HEAP32[buf+48>>2]=stat.atime.getTime()/1e3|0;HEAP32[buf+52>>2]=0;HEAP32[buf+56>>2]=stat.mtime.getTime()/1e3|0;HEAP32[buf+60>>2]=0;HEAP32[buf+64>>2]=stat.ctime.getTime()/1e3|0;HEAP32[buf+68>>2]=0;HEAP32[buf+72>>2]=stat.ino;return 0}),doMsync:(function(addr,stream,len,flags){var buffer=new Uint8Array(HEAPU8.subarray(addr,addr+len));FS.msync(stream,buffer,0,len,flags)}),doMkdir:(function(path,mode){path=PATH.normalize(path);if(path[path.length-1]==="/")path=path.substr(0,path.length-1);FS.mkdir(path,mode,0);return 0}),doMknod:(function(path,mode,dev){switch(mode&61440){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-ERRNO_CODES.EINVAL}FS.mknod(path,mode,dev);return 0}),doReadlink:(function(path,buf,bufsize){if(bufsize<=0)return-ERRNO_CODES.EINVAL;var ret=FS.readlink(path);var len=Math.min(bufsize,lengthBytesUTF8(ret));var endChar=HEAP8[buf+len];stringToUTF8(ret,buf,bufsize+1);HEAP8[buf+len]=endChar;return len}),doAccess:(function(path,amode){if(amode&~7){return-ERRNO_CODES.EINVAL}var node;var lookup=FS.lookupPath(path,{follow:true});node=lookup.node;var perms="";if(amode&4)perms+="r";if(amode&2)perms+="w";if(amode&1)perms+="x";if(perms&&FS.nodePermissions(node,perms)){return-ERRNO_CODES.EACCES}return 0}),doDup:(function(path,flags,suggestFD){var suggest=FS.getStream(suggestFD);if(suggest)FS.close(suggest);return FS.open(path,flags,0,suggestFD,suggestFD).fd}),doReadv:(function(stream,iov,iovcnt,offset){var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov+i*8>>2];var len=HEAP32[iov+(i*8+4)>>2];var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break}return ret}),doWritev:(function(stream,iov,iovcnt,offset){var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov+i*8>>2];var len=HEAP32[iov+(i*8+4)>>2];var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr}return ret}),varargs:0,get:(function(varargs){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret}),getStr:(function(){var ret=Pointer_stringify(SYSCALLS.get());return ret}),getStreamFromFD:(function(){var stream=FS.getStream(SYSCALLS.get());if(!stream)throw new FS.ErrnoError(ERRNO_CODES.EBADF);return stream}),getSocketFromFD:(function(){var socket=SOCKFS.getSocket(SYSCALLS.get());if(!socket)throw new FS.ErrnoError(ERRNO_CODES.EBADF);return socket}),getSocketAddress:(function(allowNull){var addrp=SYSCALLS.get(),addrlen=SYSCALLS.get();if(allowNull&&addrp===0)return null;var info=__read_sockaddr(addrp,addrlen);if(info.errno)throw new FS.ErrnoError(info.errno);info.addr=DNS.lookup_addr(info.addr)||info.addr;return info}),get64:(function(){var low=SYSCALLS.get(),high=SYSCALLS.get();if(low>=0)assert(high===0);else assert(high===-1);return low}),getZero:(function(){assert(SYSCALLS.get()===0)})};function ___syscall140(which,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(),offset_high=SYSCALLS.get(),offset_low=SYSCALLS.get(),result=SYSCALLS.get(),whence=SYSCALLS.get();var offset=offset_low;FS.llseek(stream,offset,whence);HEAP32[result>>2]=stream.position;if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___syscall145(which,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(),iov=SYSCALLS.get(),iovcnt=SYSCALLS.get();return SYSCALLS.doReadv(stream,iov,iovcnt)}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___syscall146(which,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(),iov=SYSCALLS.get(),iovcnt=SYSCALLS.get();return SYSCALLS.doWritev(stream,iov,iovcnt)}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___syscall54(which,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(),op=SYSCALLS.get();switch(op){case 21509:case 21505:{if(!stream.tty)return-ERRNO_CODES.ENOTTY;return 0};case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:{if(!stream.tty)return-ERRNO_CODES.ENOTTY;return 0};case 21519:{if(!stream.tty)return-ERRNO_CODES.ENOTTY;var argp=SYSCALLS.get();HEAP32[argp>>2]=0;return 0};case 21520:{if(!stream.tty)return-ERRNO_CODES.ENOTTY;return-ERRNO_CODES.EINVAL};case 21531:{var argp=SYSCALLS.get();return FS.ioctl(stream,op,argp)};case 21523:{if(!stream.tty)return-ERRNO_CODES.ENOTTY;return 0};case 21524:{if(!stream.tty)return-ERRNO_CODES.ENOTTY;return 0};default:abort("bad ioctl syscall "+op)}}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___syscall6(which,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD();FS.close(stream);return 0}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___unlock(){}var _llvm_fabs_f32=Math_abs;function _emscripten_memcpy_big(dest,src,num){HEAPU8.set(HEAPU8.subarray(src,src+num),dest);return dest}FS.staticInit();__ATINIT__.unshift((function(){if(!Module["noFSInit"]&&!FS.init.initialized)FS.init()}));__ATMAIN__.push((function(){FS.ignorePermissions=false}));__ATEXIT__.push((function(){FS.quit()}));__ATINIT__.unshift((function(){TTY.init()}));__ATEXIT__.push((function(){TTY.shutdown()}));if(ENVIRONMENT_IS_NODE){var fs=require("fs");var NODEJS_PATH=require("path");NODEFS.staticInit()}DYNAMICTOP_PTR=staticAlloc(4);STACK_BASE=STACKTOP=alignMemory(STATICTOP);STACK_MAX=STACK_BASE+TOTAL_STACK;DYNAMIC_BASE=alignMemory(STACK_MAX);HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE;staticSealed=true;var ASSERTIONS=false;function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}function invoke_ii(index,a1){var sp=stackSave();try{return Module["dynCall_ii"](index,a1)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_ii(index,a1){return functionPointers[index](a1)}function invoke_iii(index,a1,a2){var sp=stackSave();try{return Module["dynCall_iii"](index,a1,a2)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_iii(index,a1,a2){return functionPointers[index](a1,a2)}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return Module["dynCall_iiii"](index,a1,a2,a3)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_iiii(index,a1,a2,a3){return functionPointers[index](a1,a2,a3)}function invoke_iiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return Module["dynCall_iiiii"](index,a1,a2,a3,a4)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_iiiii(index,a1,a2,a3,a4){return functionPointers[index](a1,a2,a3,a4)}function invoke_iiiiiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return Module["dynCall_iiiiiii"](index,a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_iiiiiii(index,a1,a2,a3,a4,a5,a6){return functionPointers[index](a1,a2,a3,a4,a5,a6)}function invoke_viii(index,a1,a2,a3){var sp=stackSave();try{Module["dynCall_viii"](index,a1,a2,a3)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_viii(index,a1,a2,a3){functionPointers[index](a1,a2,a3)}function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{Module["dynCall_viiii"](index,a1,a2,a3,a4)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_viiii(index,a1,a2,a3,a4){functionPointers[index](a1,a2,a3,a4)}function invoke_viiiiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{Module["dynCall_viiiiii"](index,a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_viiiiii(index,a1,a2,a3,a4,a5,a6){functionPointers[index](a1,a2,a3,a4,a5,a6)}function invoke_viiiiiii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{Module["dynCall_viiiiiii"](index,a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(typeof e!=="number"&&e!=="longjmp")throw e;Module["setThrew"](1,0)}}function jsCall_viiiiiii(index,a1,a2,a3,a4,a5,a6,a7){functionPointers[index](a1,a2,a3,a4,a5,a6,a7)}Module.asmGlobalArg={"Math":Math,"Int8Array":Int8Array,"Int16Array":Int16Array,"Int32Array":Int32Array,"Uint8Array":Uint8Array,"Uint16Array":Uint16Array,"Uint32Array":Uint32Array,"Float32Array":Float32Array,"Float64Array":Float64Array,"NaN":NaN,"Infinity":Infinity,"byteLength":byteLength};Module.asmLibraryArg={"abort":abort,"assert":assert,"enlargeMemory":enlargeMemory,"getTotalMemory":getTotalMemory,"abortOnCannotGrowMemory":abortOnCannotGrowMemory,"invoke_ii":invoke_ii,"jsCall_ii":jsCall_ii,"invoke_iii":invoke_iii,"jsCall_iii":jsCall_iii,"invoke_iiii":invoke_iiii,"jsCall_iiii":jsCall_iiii,"invoke_iiiii":invoke_iiiii,"jsCall_iiiii":jsCall_iiiii,"invoke_iiiiiii":invoke_iiiiiii,"jsCall_iiiiiii":jsCall_iiiiiii,"invoke_viii":invoke_viii,"jsCall_viii":jsCall_viii,"invoke_viiii":invoke_viiii,"jsCall_viiii":jsCall_viiii,"invoke_viiiiii":invoke_viiiiii,"jsCall_viiiiii":jsCall_viiiiii,"invoke_viiiiiii":invoke_viiiiiii,"jsCall_viiiiiii":jsCall_viiiiiii,"___lock":___lock,"___setErrNo":___setErrNo,"___syscall140":___syscall140,"___syscall145":___syscall145,"___syscall146":___syscall146,"___syscall54":___syscall54,"___syscall6":___syscall6,"___unlock":___unlock,"_emscripten_memcpy_big":_emscripten_memcpy_big,"_llvm_fabs_f32":_llvm_fabs_f32,"DYNAMICTOP_PTR":DYNAMICTOP_PTR,"tempDoublePtr":tempDoublePtr,"ABORT":ABORT,"STACKTOP":STACKTOP,"STACK_MAX":STACK_MAX};// EMSCRIPTEN_START_ASM
var asm=(/** @suppress {uselessCode} */ function(global,env,buffer) {
"almost asm";var a=global.Int8Array;var b=new a(buffer);var c=global.Int16Array;var d=new c(buffer);var e=global.Int32Array;var f=new e(buffer);var g=global.Uint8Array;var h=new g(buffer);var i=global.Uint16Array;var j=new i(buffer);var k=global.Uint32Array;var l=new k(buffer);var m=global.Float32Array;var n=new m(buffer);var o=global.Float64Array;var p=new o(buffer);var q=global.byteLength;var r=env.DYNAMICTOP_PTR|0;var s=env.tempDoublePtr|0;var t=env.ABORT|0;var u=env.STACKTOP|0;var v=env.STACK_MAX|0;var w=0;var x=0;var y=0;var z=0;var A=global.NaN,B=global.Infinity;var C=0,D=0,E=0,F=0,G=0.0;var H=0;var I=global.Math.floor;var J=global.Math.abs;var K=global.Math.sqrt;var L=global.Math.pow;var M=global.Math.cos;var N=global.Math.sin;var O=global.Math.tan;var P=global.Math.acos;var Q=global.Math.asin;var R=global.Math.atan;var S=global.Math.atan2;var T=global.Math.exp;var U=global.Math.log;var V=global.Math.ceil;var W=global.Math.imul;var X=global.Math.min;var Y=global.Math.max;var Z=global.Math.clz32;var _=env.abort;var $=env.assert;var aa=env.enlargeMemory;var ba=env.getTotalMemory;var ca=env.abortOnCannotGrowMemory;var da=env.invoke_ii;var ea=env.jsCall_ii;var fa=env.invoke_iii;var ga=env.jsCall_iii;var ha=env.invoke_iiii;var ia=env.jsCall_iiii;var ja=env.invoke_iiiii;var ka=env.jsCall_iiiii;var la=env.invoke_iiiiiii;var ma=env.jsCall_iiiiiii;var na=env.invoke_viii;var oa=env.jsCall_viii;var pa=env.invoke_viiii;var qa=env.jsCall_viiii;var ra=env.invoke_viiiiii;var sa=env.jsCall_viiiiii;var ta=env.invoke_viiiiiii;var ua=env.jsCall_viiiiiii;var va=env.___lock;var wa=env.___setErrNo;var xa=env.___syscall140;var ya=env.___syscall145;var za=env.___syscall146;var Aa=env.___syscall54;var Ba=env.___syscall6;var Ca=env.___unlock;var Da=env._emscripten_memcpy_big;var Ea=env._llvm_fabs_f32;var Fa=0.0;function Ga(newBuffer){if(q(newBuffer)&16777215||q(newBuffer)<=16777215||q(newBuffer)>2147483648)return false;b=new a(newBuffer);d=new c(newBuffer);f=new e(newBuffer);h=new g(newBuffer);j=new i(newBuffer);l=new k(newBuffer);n=new m(newBuffer);p=new o(newBuffer);buffer=newBuffer;return true}
// EMSCRIPTEN_START_FUNCS
function Qa(a){a=a|0;var b=0;b=u;u=u+a|0;u=u+15&-16;return b|0}function Ra(){return u|0}function Sa(a){a=a|0;u=a}function Ta(a,b){a=a|0;b=b|0;u=a;v=b}function Ua(a,b){a=a|0;b=b|0;if(!w){w=a;x=b}}function Va(a){a=a|0;H=a}function Wa(){return H|0}function Xa(){return Nd(1,40)|0}function Ya(a){a=a|0;var b=0;b=f[a>>2]|0;if(b|0)Md(b);Md(a);return}function Za(a){a=a|0;var b=0;b=f[a>>2]|0;if(b|0)Md(b);f[a+32>>2]=0;f[a+36>>2]=0;f[a>>2]=0;f[a+4>>2]=0;f[a+8>>2]=0;f[a+12>>2]=0;f[a+16>>2]=0;f[a+20>>2]=0;return}function _a(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;d=a+8|0;f[d>>2]=0;f[d+4>>2]=0;f[d+8>>2]=0;f[d+12>>2]=0;f[a+4>>2]=2048;d=Ld(8192)|0;f[a>>2]=d;if(!d){d=0;return d|0}f[a+32>>2]=b;f[a+36>>2]=c;d=1;return d|0}function $a(a){a=a|0;a=a+8|0;f[a>>2]=0;f[a+4>>2]=0;f[a+8>>2]=0;f[a+12>>2]=0;return 1}function ab(a,b){a=a|0;b=b|0;f[a+24>>2]=b&65535;f[a+28>>2]=f[a+20>>2];return}function bb(a){a=a|0;var b=0,c=0,d=0,e=0,g=0,h=0;e=f[a+20>>2]|0;if(e|0?(g=f[(f[a>>2]|0)+(f[a+16>>2]<<2)>>2]|0,h=a+28|0,b=f[h>>2]|0,b>>>0<e>>>0):0){c=a+24|0;d=f[c>>2]|0;do{d=d<<8&65280^f[8+((d>>>8^g>>>(24-b|0)&255)<<2)>>2];f[c>>2]=d;b=b+8|0;f[h>>2]=b}while(b>>>0<e>>>0)}return f[a+24>>2]&65535|0}function cb(a){a=a|0;return (f[a+20>>2]&7|0)==0|0}function db(a){a=a|0;return 8-(f[a+20>>2]&7)|0}function eb(a){a=a|0;return ((f[a+8>>2]|0)-(f[a+16>>2]|0)<<5)+(f[a+12>>2]<<3)-(f[a+20>>2]|0)|0}function fb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;if(!c){f[b>>2]=0;m=1;return m|0}i=a+8|0;d=f[i>>2]|0;l=a+16|0;g=f[l>>2]|0;h=a+12|0;k=a+20|0;e=f[k>>2]|0;a:do if(((d-g<<5)+(f[h>>2]<<3)-e|0)>>>0<c>>>0){while(1){if(!(gb(a)|0)){d=0;break}d=f[i>>2]|0;g=f[l>>2]|0;e=f[k>>2]|0;if(((d-g<<5)+(f[h>>2]<<3)-e|0)>>>0>=c>>>0){h=g;break a}}return d|0}else h=g;while(0);g=(e|0)!=0;if(d>>>0<=h>>>0){d=f[(f[a>>2]|0)+(h<<2)>>2]|0;if(g){f[b>>2]=(d&-1>>>e)>>>(32-c-e|0);f[k>>2]=(f[k>>2]|0)+c;m=1;return m|0}else{f[b>>2]=d>>>(32-c|0);f[k>>2]=(f[k>>2]|0)+c;m=1;return m|0}}if(!g){h=f[(f[a>>2]|0)+(h<<2)>>2]|0;if(c>>>0<32){f[b>>2]=h>>>(32-c|0);f[k>>2]=c;m=1;return m|0}f[b>>2]=h;g=a+24|0;d=f[g>>2]|0;e=a+28|0;k=f[e>>2]|0;switch(k>>>3|k<<29|0){case 0:{d=f[8+((d>>>8^h>>>24)<<2)>>2]^d<<8&65280;m=22;break}case 1:{m=22;break}case 2:{m=23;break}case 3:{m=24;break}default:{}}if((m|0)==22){d=d<<8&65280^f[8+((d>>>8^h>>>16&255)<<2)>>2];m=23}if((m|0)==23){d=d<<8&65280^f[8+((d>>>8^h>>>8&255)<<2)>>2];m=24}if((m|0)==24)f[g>>2]=d<<8&65280^f[8+((d>>>8^h&255)<<2)>>2];f[e>>2]=0;f[l>>2]=(f[l>>2]|0)+1;m=1;return m|0}g=32-e|0;j=f[a>>2]|0;i=f[j+(h<<2)>>2]|0;d=i&-1>>>e;if(g>>>0>c>>>0){f[b>>2]=d>>>(g-c|0);f[k>>2]=(f[k>>2]|0)+c;m=1;return m|0}f[b>>2]=d;g=c-g|0;h=a+24|0;d=f[h>>2]|0;e=a+28|0;a=f[e>>2]|0;switch(a>>>3|a<<29|0){case 0:{d=f[8+((d>>>8^i>>>24)<<2)>>2]^d<<8&65280;m=13;break}case 1:{m=13;break}case 2:{m=14;break}case 3:{m=15;break}default:{}}if((m|0)==13){d=d<<8&65280^f[8+((d>>>8^i>>>16&255)<<2)>>2];m=14}if((m|0)==14){d=d<<8&65280^f[8+((d>>>8^i>>>8&255)<<2)>>2];m=15}if((m|0)==15)f[h>>2]=d<<8&65280^f[8+((d>>>8^i&255)<<2)>>2];f[e>>2]=0;f[l>>2]=(f[l>>2]|0)+1;f[k>>2]=0;if(!g){m=1;return m|0}m=f[b>>2]<<g;f[b>>2]=m;f[b>>2]=(f[j+(f[l>>2]<<2)>>2]|0)>>>(32-g|0)|m;f[k>>2]=g;m=1;return m|0}function gb(a){a=a|0;var b=0,c=0,d=0,e=0,g=0,h=0,i=0,j=0,k=0;k=u;u=u+16|0;h=k;c=a+16|0;d=f[c>>2]|0;i=a+8|0;b=f[i>>2]|0;j=a+12|0;if(d){g=f[a>>2]|0;bf(g|0,g+(d<<2)|0,b-d+((f[j>>2]|0)!=0&1)<<2|0)|0;b=(f[i>>2]|0)-d|0;f[i>>2]=b;f[c>>2]=0}c=f[j>>2]|0;g=((f[a+4>>2]|0)-b<<2)-c|0;f[h>>2]=g;if(!g){j=0;u=k;return j|0}b=(f[a>>2]|0)+(b<<2)|0;if(c|0){g=$e(f[b>>2]|0)|0;f[b>>2]=g}if(!(Ja[f[a+32>>2]&15](b+c|0,h,f[a+36>>2]|0)|0)){j=0;u=k;return j|0}e=f[i>>2]|0;d=e<<2;c=f[j>>2]|0;b=f[h>>2]|0;g=(c+3+d+b|0)>>>2;if(e>>>0<g>>>0){c=f[a>>2]|0;b=e;do{a=c+(b<<2)|0;e=$e(f[a>>2]|0)|0;f[a>>2]=e;b=b+1|0}while((b|0)!=(g|0));d=f[i>>2]<<2;c=f[j>>2]|0;b=f[h>>2]|0}h=d+c+b|0;f[i>>2]=h>>>2;f[j>>2]=h&3;j=1;u=k;return j|0}function hb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0;g=u;u=u+16|0;d=g;e=1<<c+-1;if(!(fb(a,d,c)|0)){e=0;u=g;return e|0}f[b>>2]=(f[d>>2]^e)-e;e=1;u=g;return e|0}function ib(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0;g=u;u=u+16|0;d=g+4|0;e=g;do if(c>>>0>32){if(!(fb(a,d,c+-32|0)|0)){b=0;u=g;return b|0}if(!(fb(a,e,32)|0)){b=0;u=g;return b|0}else{c=f[d>>2]|0;a=b;f[a>>2]=0;f[a+4>>2]=c;a=f[e>>2]|0;break}}else if(!(fb(a,e,c)|0)){b=0;u=g;return b|0}else{a=f[e>>2]|0;c=0;break}while(0);f[b>>2]=a;f[b+4>>2]=c;b=1;u=g;return b|0}function jb(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0;g=u;u=u+16|0;d=g+4|0;e=g;f[e>>2]=0;if(!(fb(a,e,8)|0)){e=0;u=g;return e|0}if(!(fb(a,d,8)|0)){e=0;u=g;return e|0}c=f[e>>2]|f[d>>2]<<8;f[e>>2]=c;if(!(fb(a,d,8)|0)){e=0;u=g;return e|0}c=f[d>>2]<<16|c;f[e>>2]=c;if(!(fb(a,d,8)|0)){e=0;u=g;return e|0}d=f[d>>2]<<24|c;f[e>>2]=d;f[b>>2]=d;e=1;u=g;return e|0}function kb(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0,i=0,j=0;j=u;u=u+16|0;h=j+4|0;i=j;if(!b){i=1;u=j;return i|0}e=a+20|0;c=f[e>>2]&7;if(c){c=8-c|0;c=c>>>0<b>>>0?c:b;if(fb(a,i,c)|0){b=b-c|0;d=5}}else d=5;a:do if((d|0)==5){c=b>>>3;do if(c){while(1){if(!(f[e>>2]|0)){d=10;break}if(!(fb(a,h,8)|0)){d=20;break}c=c+-1|0;if(!c){d=21;break}}b:do if((d|0)==10){if(c>>>0>3){e=a+16|0;g=a+8|0;do{d=f[e>>2]|0;if(d>>>0>=(f[g>>2]|0)>>>0){if(!(gb(a)|0)){d=20;break b}}else{f[e>>2]=d+1;c=c+-4|0}}while(c>>>0>3);if(!c){d=21;break}}while(1){c=c+-1|0;if(!(fb(a,h,8)|0)){d=20;break b}if(!c){d=21;break}}}while(0);if((d|0)==20)break a;else if((d|0)==21){b=b&7;break}}while(0);if(b|0?(fb(a,i,b)|0)==0:0)break;i=1;u=j;return i|0}while(0);i=0;u=j;return i|0}function lb(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0;h=u;u=u+16|0;g=h;a:do if(!b)b=1;else{c=a+20|0;while(1){if(!(f[c>>2]|0))break;if(!(fb(a,g,8)|0)){b=0;break a}b=b+-1|0;if(!b){b=1;break a}}if(b>>>0>3){d=a+16|0;e=a+8|0;do{c=f[d>>2]|0;if(c>>>0>=(f[e>>2]|0)>>>0){if(!(gb(a)|0)){b=0;break a}}else{f[d>>2]=c+1;b=b+-4|0}}while(b>>>0>3);if(!b){b=1;break}}while(1){b=b+-1|0;if(!(fb(a,g,8)|0)){b=0;break a}if(!b){b=1;break}}}while(0);u=h;return b|0}function mb(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;n=u;u=u+16|0;m=n;if(!d){m=1;u=n;return m|0}g=a+20|0;while(1){if(!(f[g>>2]|0)){i=6;break}if(!(fb(a,m,8)|0)){j=0;i=16;break}b[c>>0]=f[m>>2];e=d+-1|0;if(!e){j=1;i=16;break}else{d=e;c=c+1|0}}if((i|0)==6){if(d>>>0>3){g=a+16|0;h=a+8|0;while(1){e=f[g>>2]|0;if(e>>>0>=(f[h>>2]|0)>>>0)if(!(gb(a)|0)){j=0;i=16;break}else{l=d;k=c}else{l=f[a>>2]|0;f[g>>2]=e+1;l=f[l+(e<<2)>>2]|0;b[c>>0]=l>>>24;b[c+1>>0]=l>>>16;b[c+2>>0]=l>>>8;b[c+3>>0]=l;l=d+-4|0;k=c+4|0}if(l>>>0>3){c=k;d=l}else break}if((i|0)==16){u=n;return j|0}if(!l){m=1;u=n;return m|0}else{c=k;d=l}}while(1){if(!(fb(a,m,8)|0)){j=0;i=16;break}b[c>>0]=f[m>>2];d=d+-1|0;if(!d){j=1;i=16;break}else c=c+1|0}if((i|0)==16){u=n;return j|0}}else if((i|0)==16){u=n;return j|0}return 0}function nb(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;f[b>>2]=0;j=a+16|0;h=a+8|0;i=a+12|0;k=a+20|0;l=a+24|0;m=a+28|0;a:while(1){c=f[j>>2]|0;b:do if(c>>>0<(f[h>>2]|0)>>>0){g=f[a>>2]|0;d=f[k>>2]|0;while(1){c=f[g+(c<<2)>>2]<<d;if(c|0){n=6;break a}f[b>>2]=32-d+(f[b>>2]|0);d=f[j>>2]|0;e=f[g+(d<<2)>>2]|0;c=f[l>>2]|0;o=f[m>>2]|0;switch(o>>>3|o<<29|0){case 0:{c=f[8+((c>>>8^e>>>24)<<2)>>2]^c<<8&65280;n=15;break}case 1:{n=15;break}case 2:{n=16;break}case 3:{n=17;break}default:{}}if((n|0)==15){c=c<<8&65280^f[8+((c>>>8^e>>>16&255)<<2)>>2];n=16}if((n|0)==16){c=c<<8&65280^f[8+((c>>>8^e>>>8&255)<<2)>>2];n=17}if((n|0)==17){n=0;f[l>>2]=c<<8&65280^f[8+((c>>>8^e&255)<<2)>>2]}f[m>>2]=0;c=d+1|0;f[j>>2]=c;f[k>>2]=0;if(c>>>0<(f[h>>2]|0)>>>0)d=0;else{e=0;break b}}}else e=f[k>>2]|0;while(0);d=f[i>>2]<<3;if(d>>>0>e>>>0){c=(f[(f[a>>2]|0)+(c<<2)>>2]&-1<<32-d)<<e;if(c|0){n=21;break}f[b>>2]=d-e+(f[b>>2]|0);f[k>>2]=d}if(!(gb(a)|0)){c=0;n=24;break}}if((n|0)==6){o=Z(c|0)|0;f[b>>2]=(f[b>>2]|0)+o;o=o+1+(f[k>>2]|0)|0;f[k>>2]=o;if(o>>>0<=31){o=1;return o|0}e=f[j>>2]|0;d=f[g+(e<<2)>>2]|0;c=f[l>>2]|0;o=f[m>>2]|0;switch(o>>>3|o<<29|0){case 0:{c=f[8+((c>>>8^d>>>24)<<2)>>2]^c<<8&65280;n=9;break}case 1:{n=9;break}case 2:{n=10;break}case 3:{n=11;break}default:{}}if((n|0)==9){c=c<<8&65280^f[8+((c>>>8^d>>>16&255)<<2)>>2];n=10}if((n|0)==10){c=c<<8&65280^f[8+((c>>>8^d>>>8&255)<<2)>>2];n=11}if((n|0)==11)f[l>>2]=c<<8&65280^f[8+((c>>>8^d&255)<<2)>>2];f[m>>2]=0;f[j>>2]=e+1;f[k>>2]=0;o=1;return o|0}else if((n|0)==21){o=Z(c|0)|0;f[b>>2]=(f[b>>2]|0)+o;f[k>>2]=o+1+(f[k>>2]|0);o=1;return o|0}else if((n|0)==24)return c|0;return 0}function ob(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0;Y=u;u=u+16|0;P=Y+4|0;Q=Y;R=b+(c<<2)|0;if(!d){if((c|0)<=0){a=1;u=Y;return a|0}c=b;while(1){if(!(nb(a,Q)|0)){t=0;s=44;break}X=f[Q>>2]|0;f[c>>2]=X>>>1^0-(X&1);c=c+4|0;if(c>>>0>=R>>>0){t=1;s=44;break}}if((s|0)==44){u=Y;return t|0}}X=a+16|0;c=f[X>>2]|0;p=a+8|0;h=f[p>>2]|0;if(c>>>0<h>>>0){T=f[a>>2]|0;z=h;A=c;D=b;M=T+(c<<2)|0;s=8}else{I=b;K=0;s=30}a:while(1){do if((s|0)==8){s=0;c=f[a+20>>2]|0;h=32-c|0;if(D>>>0>=R>>>0){g=h;e=A;s=36;break a}m=a+24|0;n=a+28|0;o=32-d|0;b=f[M>>2]<<c;k=A;l=D;b:while(1){i=Z(b|0)|0;c:do if(!b){y=h;c=k;i=f[n>>2]|0;j=f[m>>2]|0;b=f[T+(k<<2)>>2]|0;while(1){v=c+1|0;switch(i>>>3|i<<29|0){case 0:{w=f[8+((j>>>8^b>>>24)<<2)>>2]^j<<8&65280;s=14;break}case 1:{w=j;s=14;break}case 2:{B=j;s=15;break}case 3:{F=j;s=16;break}default:O=j}if((s|0)==14){B=w<<8&65280^f[8+((w>>>8^b>>>16&255)<<2)>>2];s=15}if((s|0)==15){F=B<<8&65280^f[8+((B>>>8^b>>>8&255)<<2)>>2];s=16}if((s|0)==16){s=0;O=F<<8&65280^f[8+((F>>>8^b&255)<<2)>>2];f[m>>2]=O}f[n>>2]=0;if(v>>>0>=z>>>0){s=29;break b}b=f[T+(v<<2)>>2]|0;c=Z(b|0)|0;i=c+y|0;if(!b){y=i;c=v;i=0;j=O;b=0}else{E=i;i=v;break c}}}else{c=i;E=i;i=k}while(0);c=b<<c<<1;q=h+31-E&31;f[Q>>2]=E;r=c>>>o;if(q>>>0<d>>>0){S=i+1|0;c=f[T+(i<<2)>>2]|0;h=f[m>>2]|0;k=f[n>>2]|0;switch(k>>>3|k<<29|0){case 0:{x=f[8+((h>>>8^c>>>24)<<2)>>2]^h<<8&65280;s=23;break}case 1:{x=h;s=23;break}case 2:{C=h;s=24;break}case 3:{G=h;s=25;break}default:{}}if((s|0)==23){C=x<<8&65280^f[8+((x>>>8^c>>>16&255)<<2)>>2];s=24}if((s|0)==24){G=C<<8&65280^f[8+((C>>>8^c>>>8&255)<<2)>>2];s=25}if((s|0)==25){s=0;f[m>>2]=G<<8&65280^f[8+((G>>>8^c&255)<<2)>>2]}f[n>>2]=0;if(S>>>0>=z>>>0){s=32;break}i=f[T+(S<<2)>>2]|0;c=q+o|0;h=c;b=i<<32-c;c=i>>>c|r;i=S}else{h=q-d|0;b=c<<d;c=r}f[P>>2]=c;k=c|E<<d;c=l+4|0;f[l>>2]=k>>>1^0-(k&1);if(c>>>0<R>>>0){k=i;l=c}else{g=h;e=i;s=36;break a}}if((s|0)==29){f[a+20>>2]=0;f[X>>2]=v;I=l;K=y;s=30;continue a}else if((s|0)==32){f[a+20>>2]=0;f[X>>2]=S;H=q;J=l;L=r;N=E;break}}else if((s|0)==30){if(!(nb(a,Q)|0)){t=0;s=44;break a}N=(f[Q>>2]|0)+K|0;f[Q>>2]=N;H=0;J=I;L=0}while(0);if(!(fb(a,P,d-H|0)|0)){t=0;s=44;break}h=f[P>>2]|L;f[P>>2]=h;h=N<<d|h;c=J+4|0;f[J>>2]=h>>>1^0-(h&1);h=f[X>>2]|0;b=f[p>>2]|0;if(c>>>0<R>>>0&h>>>0>=b>>>0){I=c;K=0;s=30;continue}T=f[a>>2]|0;z=b;A=h;D=c;M=T+(h<<2)|0;s=8}if((s|0)==36){if(e>>>0<z>>>0&(g|0)==0){b=e+1|0;e=f[T+(e<<2)>>2]|0;g=a+24|0;c=f[g>>2]|0;h=a+28|0;T=f[h>>2]|0;switch(T>>>3|T<<29|0){case 0:{U=f[8+((c>>>8^e>>>24)<<2)>>2]^c<<8&65280;s=39;break}case 1:{U=c;s=39;break}case 2:{V=c;s=40;break}case 3:{W=c;s=41;break}default:{}}if((s|0)==39){V=U<<8&65280^f[8+((U>>>8^e>>>16&255)<<2)>>2];s=40}if((s|0)==40){W=V<<8&65280^f[8+((V>>>8^e>>>8&255)<<2)>>2];s=41}if((s|0)==41)f[g>>2]=W<<8&65280^f[8+((W>>>8^e&255)<<2)>>2];f[h>>2]=0;g=32;e=b}f[a+20>>2]=32-g;f[X>>2]=e;a=1;u=Y;return a|0}else if((s|0)==44){u=Y;return t|0}return 0}function pb(a,c,d,e){a=a|0;c=c|0;d=d|0;e=e|0;var g=0,h=0,i=0,j=0,k=0,l=0;l=u;u=u+16|0;k=l;if(!(fb(a,k,8)|0)){c=0;u=l;return c|0}j=(d|0)!=0;g=f[k>>2]|0;if(j){i=f[e>>2]|0;f[e>>2]=i+1;b[d+i>>0]=g}a:do if(g&128){if(!((g&192|0)!=0&(g&32|0)==0))if(!((g&224|0)!=0&(g&16|0)==0))if(!((g&240|0)!=0&(g&8|0)==0))if(!((g&248|0)!=0&(g&4|0)==0))if((g&252|0)!=0&(g&2|0)==0){i=5;h=1}else{f[c>>2]=-1;c=1;u=l;return c|0}else{i=4;h=3}else{i=3;h=7}else{i=2;h=15}else{i=1;h=31}g=g&h;b:do if(j){while(1){if(!(fb(a,k,8)|0)){g=0;break}h=f[k>>2]|0;j=f[e>>2]|0;f[e>>2]=j+1;b[d+j>>0]=h;if((h&192|0)!=128)break b;g=h&63|g<<6;i=i+-1|0;if(!i)break a}u=l;return g|0}else{while(1){if(!(fb(a,k,8)|0)){g=0;break}h=f[k>>2]|0;if((h&192|0)!=128)break b;g=h&63|g<<6;i=i+-1|0;if(!i)break a}u=l;return g|0}while(0);f[c>>2]=-1;c=1;u=l;return c|0}while(0);f[c>>2]=g;c=1;u=l;return c|0}function qb(a,c,d,e){a=a|0;c=c|0;d=d|0;e=e|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0;l=u;u=u+16|0;k=l;if(!(fb(a,k,8)|0)){c=0;u=l;return c|0}i=(d|0)!=0;h=f[k>>2]|0;if(i){j=f[e>>2]|0;f[e>>2]=j+1;b[d+j>>0]=h}a:do if(!(h&128))g=0;else{do if(!((h&192|0)!=0&(h&32|0)==0)){if((h&224|0)!=0&(h&16|0)==0){g=2;h=h&15;break}if((h&240|0)!=0&(h&8|0)==0){g=3;h=h&7;break}if((h&248|0)!=0&(h&4|0)==0){g=4;h=h&3;break}if((h&252|0)!=0&(h&2|0)==0){g=5;h=h&1;break}if((h&254|0)!=0&(h&1|0)==0){g=6;h=0}else{f[c>>2]=-1;f[c+4>>2]=-1;c=1;u=l;return c|0}}else{g=1;h=h&31}while(0);b:do if(i){j=g;g=0;while(1){if(!(fb(a,k,8)|0)){g=0;break}i=f[k>>2]|0;m=f[e>>2]|0;f[e>>2]=m+1;b[d+m>>0]=i;if((i&192|0)!=128)break b;h=_e(h|0,g|0,6)|0;g=H;h=h|i&63;j=j+-1|0;if(!j)break a}u=l;return g|0}else{j=g;g=0;while(1){if(!(fb(a,k,8)|0)){g=0;break}i=f[k>>2]|0;if((i&192|0)!=128)break b;h=_e(h|0,g|0,6)|0;g=H;h=h|i&63;j=j+-1|0;if(!j)break a}u=l;return g|0}while(0);m=c;f[m>>2]=-1;f[m+4>>2]=-1;m=1;u=l;return m|0}while(0);m=c;f[m>>2]=h;f[m+4>>2]=g;m=1;u=l;return m|0}function rb(){return Nd(1,20)|0}function sb(a){a=a|0;var b=0;b=f[a>>2]|0;if(b|0)Md(b);Md(a);return}function tb(a){a=a|0;var b=0;b=f[a>>2]|0;if(b|0)Md(b);f[a>>2]=0;f[a+8>>2]=0;f[a+16>>2]=0;f[a+12>>2]=0;return}function ub(a){a=a|0;var b=0;f[a+16>>2]=0;f[a+12>>2]=0;f[a+8>>2]=8192;b=Ld(32768)|0;f[a>>2]=b;return (b|0)!=0|0}function vb(a){a=a|0;f[a+16>>2]=0;f[a+12>>2]=0;return}function wb(a,b){a=a|0;b=b|0;var c=0,e=0,g=0;g=u;u=u+16|0;c=g+4|0;e=g;if(!(xb(a,c,e)|0)){e=0;u=g;return e|0}e=(Ob(f[c>>2]|0,f[e>>2]|0)|0)&65535;d[b>>1]=e;e=1;u=g;return e|0}function xb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0,h=0,i=0,j=0,k=0;k=a+16|0;e=f[k>>2]|0;if(e&7|0){c=0;return c|0}if(!e){g=a+12|0;e=f[a>>2]|0;d=0}else{g=a+12|0;d=f[g>>2]|0;j=a+8|0;do if((d|0)==(f[j>>2]|0)?(h=(e+63|0)>>>5,i=d+h|0,i>>>0>d>>>0):0){h=h&1023;h=((h|0)==0?0:1024-h|0)+i|0;e=f[a>>2]|0;if(h){if(h>>>0>1073741823){c=0;return c|0}i=h<<2;d=Od(e,i)|0;if((i|0)!=0&(d|0)==0){Md(e);c=0;return c|0}}else d=Od(e,0)|0;if(!d){c=0;return c|0}else{f[a>>2]=d;f[j>>2]=h;e=f[k>>2]|0;d=f[g>>2]|0;break}}while(0);j=$e(f[a+4>>2]<<32-e|0)|0;e=f[a>>2]|0;f[e+(d<<2)>>2]=j;d=(f[k>>2]|0)>>>3}f[b>>2]=e;f[c>>2]=d+(f[g>>2]<<2);c=1;return c|0}function yb(a){a=a|0;return}function zb(a,c){a=a|0;c=c|0;var d=0,e=0,g=0;g=u;u=u+16|0;d=g+4|0;e=g;if(!(xb(a,d,e)|0)){e=0;u=g;return e|0}e=Nb(f[d>>2]|0,f[e>>2]|0)|0;b[c>>0]=e;e=1;u=g;return e|0}function Ab(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0,i=0,j=0;if(!b){a=1;return a|0}g=a+8|0;d=f[g>>2]|0;h=a+12|0;e=f[h>>2]|0;i=a+16|0;do if(d>>>0<=(e+b|0)>>>0?(c=((b+31+(f[i>>2]|0)|0)>>>5)+e|0,c>>>0>d>>>0):0){e=c-d&1023;e=((e|0)==0?0:1024-e|0)+c|0;d=f[a>>2]|0;if(e){if(e>>>0>1073741823){a=0;return a|0}j=e<<2;c=Od(d,j)|0;if((j|0)!=0&(c|0)==0){Md(d);j=0;return j|0}}else c=Od(d,0)|0;if(!c){j=0;return j|0}else{f[a>>2]=c;f[g>>2]=e;break}}while(0);d=f[i>>2]|0;do if(d){c=32-d|0;c=c>>>0<b>>>0?c:b;j=a+4|0;e=f[j>>2]<<c;f[j>>2]=e;j=c+d|0;f[i>>2]=j;if((j|0)==32){e=$e(e|0)|0;g=f[a>>2]|0;j=f[h>>2]|0;f[h>>2]=j+1;f[g+(j<<2)>>2]=e;f[i>>2]=0;b=b-c|0;break}else{j=1;return j|0}}while(0);if(b>>>0>31){d=f[a>>2]|0;c=b;do{j=f[h>>2]|0;f[h>>2]=j+1;f[d+(j<<2)>>2]=0;c=c+-32|0}while(c>>>0>31);b=b&31}if(!b){j=1;return j|0}f[a+4>>2]=0;f[i>>2]=b;j=1;return j|0}function Bb(a,b,c){a=a|0;b=b|0;c=c|0;if(!(c>>>0>31|(b>>>c|0)==0)){c=0;return c|0}c=Cb(a,b,c)|0;return c|0}function Cb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0,h=0,i=0,j=0,k=0;if(!a){c=0;return c|0}h=f[a>>2]|0;if(c>>>0>32|(h|0)==0){c=0;return c|0}if(!c){c=1;return c|0}i=a+8|0;g=f[i>>2]|0;k=a+12|0;d=f[k>>2]|0;j=a+16|0;do if(g>>>0<=(d+c|0)>>>0?(e=((c+31+(f[j>>2]|0)|0)>>>5)+d|0,e>>>0>g>>>0):0){g=e-g&1023;e=((g|0)==0?0:1024-g|0)+e|0;if(e){if(e>>>0>1073741823){c=0;return c|0}g=e<<2;d=Od(h,g)|0;if((g|0)!=0&(d|0)==0){Md(h);c=0;return c|0}}else d=Od(h,0)|0;if(!d){c=0;return c|0}else{f[a>>2]=d;f[i>>2]=e;break}}else d=h;while(0);e=f[j>>2]|0;g=32-e|0;if(g>>>0>c>>>0){k=a+4|0;f[k>>2]=f[k>>2]<<c|b;f[j>>2]=e+c;c=1;return c|0}if(!e){b=$e(b|0)|0;c=f[k>>2]|0;f[k>>2]=c+1;f[d+(c<<2)>>2]=b;c=1;return c|0}else{a=a+4|0;i=f[a>>2]<<g;c=c-g|0;f[j>>2]=c;j=i|b>>>c;c=f[k>>2]|0;f[k>>2]=c+1;f[d+(c<<2)>>2]=j<<24|j>>>24|j>>>8&65280|j<<8&16711680;f[a>>2]=b;c=1;return c|0}return 0}function Db(a,b,c){a=a|0;b=b|0;c=c|0;return Cb(a,(c>>>0<32?~(-1<<c):-1)&b,c)|0}function Eb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(d>>>0<=32){if(!((d|0)==32|(b>>>d|0)==0)){c=0;return c|0}c=Cb(a,b,d)|0;return c|0}d=d+-32|0;if(!(d>>>0>31|(c>>>d|0)==0)){c=0;return c|0}if(!(Cb(a,c,d)|0)){c=0;return c|0}c=(Cb(a,b,32)|0)!=0&1;return c|0}function Fb(a,b){a=a|0;b=b|0;if(!(Cb(a,b&255,8)|0)){b=0;return b|0}if(!(Cb(a,b>>>8&255,8)|0)){b=0;return b|0}if(!(Cb(a,b>>>16&255,8)|0)){b=0;return b|0}b=(Cb(a,b>>>24,8)|0)!=0&1;return b|0}function Gb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;if(!c){c=1;return c|0}d=0;while(1){if(!(Cb(a,h[b+d>>0]|0,8)|0)){d=0;a=5;break}d=d+1|0;if(d>>>0>=c>>>0){d=1;a=5;break}}if((a|0)==5)return d|0;return 0}function Hb(a,b){a=a|0;b=b|0;if(b>>>0<32){b=Cb(a,1,b+1|0)|0;return b|0}if(!(Ab(a,b)|0)){b=0;return b|0}b=(Cb(a,1,1)|0)!=0&1;return b|0}function Ib(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;q=-1<<d;r=-1>>>(31-d|0);s=d+1|0;if(!c){u=1;return u|0}t=a+16|0;l=a+8|0;m=a+12|0;n=a+4|0;a:while(1){k=f[b>>2]|0;k=k<<1^k>>31;e=k>>>d;g=e+s|0;h=f[t>>2]|0;if((h|0)!=0?(o=g+h|0,o>>>0<32):0){f[t>>2]=o;f[n>>2]=f[n>>2]<<g|(k|q)&r}else u=6;do if((u|0)==6){u=0;i=f[l>>2]|0;j=f[m>>2]|0;if(i>>>0<=(h+1+e+j|0)>>>0?(p=j+((g+h+31|0)>>>5)|0,p>>>0>i>>>0):0){i=p-i&1023;i=((i|0)==0?0:1024-i|0)+p|0;h=f[a>>2]|0;if(i){if(i>>>0>1073741823){b=0;u=30;break a}j=i<<2;g=Od(h,j)|0;if((j|0)!=0&(g|0)==0){u=12;break a}}else g=Od(h,0)|0;if(!g){b=0;u=30;break a}f[a>>2]=g;f[l>>2]=i}b:do if(e|0){g=f[t>>2]|0;do if(g){h=32-g|0;i=f[n>>2]|0;if(e>>>0<h>>>0){f[n>>2]=i<<e;f[t>>2]=g+e;break b}else{g=i<<h;f[n>>2]=g;g=$e(g|0)|0;i=f[a>>2]|0;j=f[m>>2]|0;f[m>>2]=j+1;f[i+(j<<2)>>2]=g;f[t>>2]=0;e=e-h|0;break}}while(0);if(e>>>0>31){h=f[a>>2]|0;g=e;do{j=f[m>>2]|0;f[m>>2]=j+1;f[h+(j<<2)>>2]=0;g=g+-32|0}while(g>>>0>31);e=e&31}if(e|0){f[n>>2]=0;f[t>>2]=e}}while(0);e=(k|q)&r;g=f[t>>2]|0;h=32-g|0;i=f[n>>2]|0;if(s>>>0<h>>>0){f[n>>2]=i<<s|e;f[t>>2]=g+s;break}else{j=s-h|0;f[t>>2]=j;i=e>>>j|i<<h;j=f[a>>2]|0;k=f[m>>2]|0;f[m>>2]=k+1;f[j+(k<<2)>>2]=i<<24|i>>>24|i>>>8&65280|i<<8&16711680;f[n>>2]=e;break}}while(0);c=c+-1|0;if(!c){b=1;u=30;break}else b=b+4|0}if((u|0)==12){Md(h);u=0;return u|0}else if((u|0)==30)return b|0;return 0}function Jb(a,b){a=a|0;b=b|0;var c=0;if((b|0)<0){b=0;return b|0}if(b>>>0<128){b=Cb(a,b,8)|0;return b|0}if(b>>>0<2048){c=(Cb(a,b>>>6|192,8)|0)&1;b=c&(Cb(a,b&63|128,8)|0);return b|0}if(b>>>0<65536){c=(Cb(a,b>>>12|224,8)|0)&1;c=c&(Cb(a,b>>>6&63|128,8)|0);c=c&(Cb(a,b&63|128,8)|0);return c|0}if(b>>>0<2097152){c=(Cb(a,b>>>18|240,8)|0)&1;c=c&(Cb(a,b>>>12&63|128,8)|0);c=c&(Cb(a,b>>>6&63|128,8)|0);c=c&(Cb(a,b&63|128,8)|0);return c|0}if(b>>>0<67108864){c=(Cb(a,b>>>24|248,8)|0)&1;c=c&(Cb(a,b>>>18&63|128,8)|0);c=c&(Cb(a,b>>>12&63|128,8)|0);c=c&(Cb(a,b>>>6&63|128,8)|0);c=c&(Cb(a,b&63|128,8)|0);return c|0}else{c=(Cb(a,b>>>30|252,8)|0)&1;c=c&(Cb(a,b>>>24&63|128,8)|0);c=c&(Cb(a,b>>>18&63|128,8)|0);c=c&(Cb(a,b>>>12&63|128,8)|0);c=c&(Cb(a,b>>>6&63|128,8)|0);c=c&(Cb(a,b&63|128,8)|0);return c|0}return 0}function Kb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;if(c>>>0>15|(c|0)==15&b>>>0>4294967295){c=0;return c|0}if(c>>>0<0|(c|0)==0&b>>>0<128){c=Cb(a,b,8)|0;return c|0}if(c>>>0<0|(c|0)==0&b>>>0<2048){c=Ze(b|0,c|0,6)|0;c=(Cb(a,c|192,8)|0)&1;c=c&(Cb(a,b&63|128,8)|0);return c|0}if(c>>>0<0|(c|0)==0&b>>>0<65536){d=Ze(b|0,c|0,12)|0;d=(Cb(a,d|224,8)|0)&1;c=Ze(b|0,c|0,6)|0;c=d&(Cb(a,c&63|128,8)|0);c=c&(Cb(a,b&63|128,8)|0);return c|0}if(c>>>0<0|(c|0)==0&b>>>0<2097152){d=Ze(b|0,c|0,18)|0;d=(Cb(a,d|240,8)|0)&1;e=Ze(b|0,c|0,12)|0;e=d&(Cb(a,e&63|128,8)|0);d=Ze(b|0,c|0,6)|0;d=e&(Cb(a,d&63|128,8)|0);d=d&(Cb(a,b&63|128,8)|0);return d|0}if(c>>>0<0|(c|0)==0&b>>>0<67108864){d=Ze(b|0,c|0,24)|0;d=(Cb(a,d|248,8)|0)&1;e=Ze(b|0,c|0,18)|0;e=d&(Cb(a,e&63|128,8)|0);d=Ze(b|0,c|0,12)|0;d=e&(Cb(a,d&63|128,8)|0);e=Ze(b|0,c|0,6)|0;e=d&(Cb(a,e&63|128,8)|0);e=e&(Cb(a,b&63|128,8)|0);return e|0}if(c>>>0<0|(c|0)==0&b>>>0<2147483648){e=Ze(b|0,c|0,30)|0;e=(Cb(a,e|252,8)|0)&1;d=Ze(b|0,c|0,24)|0;d=e&(Cb(a,d&63|128,8)|0);e=Ze(b|0,c|0,18)|0;e=d&(Cb(a,e&63|128,8)|0);d=Ze(b|0,c|0,12)|0;d=e&(Cb(a,d&63|128,8)|0);e=Ze(b|0,c|0,6)|0;e=d&(Cb(a,e&63|128,8)|0);e=e&(Cb(a,b&63|128,8)|0);return e|0}else{d=(Cb(a,254,8)|0)&1;e=Ze(b|0,c|0,30)|0;e=d&(Cb(a,e&63|128,8)|0);d=Ze(b|0,c|0,24)|0;d=e&(Cb(a,d&63|128,8)|0);e=Ze(b|0,c|0,18)|0;e=d&(Cb(a,e&63|128,8)|0);d=Ze(b|0,c|0,12)|0;d=e&(Cb(a,d&63|128,8)|0);e=Ze(b|0,c|0,6)|0;e=d&(Cb(a,e&63|128,8)|0);e=e&(Cb(a,b&63|128,8)|0);return e|0}return 0}function Lb(a){a=a|0;var b=0;b=f[a+16>>2]&7;if(!b){b=1;return b|0}b=Ab(a,8-b|0)|0;return b|0}function Mb(a){a=a|0;var b=0,c=0;b=a+8|0;c=b+80|0;do{f[b>>2]=0;b=b+4|0}while((b|0)<(c|0));f[a+4>>2]=2;f[a>>2]=0;return}function Nb(a,c){a=a|0;c=c|0;var d=0;if(!c){d=0;return d|0}d=c;c=0;while(1){d=d+-1|0;c=b[2064+((b[a>>0]^c)&255)>>0]|0;if(!d)break;else a=a+1|0}return c|0}function Ob(a,b){a=a|0;b=b|0;var c=0;if(!b){c=0;return c|0}c=b;b=0;while(1){c=c+-1|0;b=(f[8+((b>>>8^(h[a>>0]|0))<<2)>>2]^b<<8)&65535;if(!c)break;else a=a+1|0}return b|0}function Pb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0.0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,o=0,p=0,q=0,r=0;k=f[a+-4>>2]|0;m=f[a+-8>>2]|0;l=k-m|0;o=f[a+-12>>2]|0;p=o-m+l|0;if(!b){k=0;j=0;i=0;g=0;e=0}else{q=0;h=0;j=0;i=0;g=0;e=0;m=(o<<1)-m-(f[a+-16>>2]|0)+p|0;do{r=k;k=f[a+(q<<2)>>2]|0;e=((k|0)<0?0-k|0:k)+e|0;o=l;l=k-r|0;g=((l|0)<0?0-l|0:l)+g|0;r=p;p=l-o|0;i=((p|0)<0?0-p|0:p)+i|0;o=m;m=p-r|0;j=((m|0)<0?0-m|0:m)+j|0;o=m-o|0;h=((o|0)<0?0-o|0:o)+h|0;q=q+1|0}while((q|0)!=(b|0));k=h}r=g>>>0<i>>>0?g:i;r=r>>>0<j>>>0?r:j;if(e>>>0<(r>>>0<k>>>0?r:k)>>>0)h=0;else{r=i>>>0<j>>>0?i:j;h=j>>>0<k>>>0;h=g>>>0<(r>>>0<k>>>0?r:k)>>>0?1:i>>>0<(h?j:k)>>>0?2:h?3:4}if(!e)d=0.0;else d=+U(+(+(e>>>0)*.6931471805599453/+(b>>>0)))/.6931471805599453;n[c>>2]=d;if(!g)d=0.0;else d=+U(+(+(g>>>0)*.6931471805599453/+(b>>>0)))/.6931471805599453;n[c+4>>2]=d;if(!i)d=0.0;else d=+U(+(+(i>>>0)*.6931471805599453/+(b>>>0)))/.6931471805599453;n[c+8>>2]=d;if(!j)d=0.0;else d=+U(+(+(j>>>0)*.6931471805599453/+(b>>>0)))/.6931471805599453;n[c+12>>2]=d;if(!k){d=0.0;r=c+16|0;n[r>>2]=d;return h|0}d=+U(+(+(k>>>0)*.6931471805599453/+(b>>>0)))/.6931471805599453;r=c+16|0;n[r>>2]=d;return h|0}function Qb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0.0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;e=f[a+-4>>2]|0;h=f[a+-8>>2]|0;g=e-h|0;i=f[a+-12>>2]|0;j=i-h+g|0;if(!b){p=0;q=0;l=0;m=0;o=0;j=0;k=0;i=0;g=0;e=0}else{u=0;t=(i<<1)-h-(f[a+-16>>2]|0)+j|0;r=0;h=0;l=0;p=0;m=0;q=0;s=0;o=0;i=0;k=0;do{w=e;e=f[a+(u<<2)>>2]|0;r=Se(r|0,h|0,((e|0)<0?0-e|0:e)|0,0)|0;h=H;v=g;g=e-w|0;l=Se(l|0,p|0,((g|0)<0?0-g|0:g)|0,0)|0;p=H;w=j;j=g-v|0;m=Se(m|0,q|0,((j|0)<0?0-j|0:j)|0,0)|0;q=H;v=t;t=j-w|0;s=Se(s|0,o|0,((t|0)<0?0-t|0:t)|0,0)|0;o=H;v=t-v|0;i=Se(i|0,k|0,((v|0)<0?0-v|0:v)|0,0)|0;k=H;u=u+1|0}while((u|0)!=(b|0));j=s;g=h;e=r}a=p>>>0<q>>>0|(p|0)==(q|0)&l>>>0<m>>>0;w=a?l:m;a=a?p:q;v=a>>>0<o>>>0|(a|0)==(o|0)&w>>>0<j>>>0;w=v?w:j;a=v?a:o;v=a>>>0<k>>>0|(a|0)==(k|0)&w>>>0<i>>>0;a=v?a:k;if(g>>>0<a>>>0|(g|0)==(a|0)&e>>>0<(v?w:i)>>>0)h=0;else{u=q>>>0<o>>>0|(q|0)==(o|0)&m>>>0<j>>>0;v=u?m:j;u=u?q:o;a=u>>>0<k>>>0|(u|0)==(k|0)&v>>>0<i>>>0;u=a?u:k;h=o>>>0<k>>>0|(o|0)==(k|0)&j>>>0<i>>>0;w=h?o:k;h=p>>>0<u>>>0|(p|0)==(u|0)&l>>>0<(a?v:i)>>>0?1:q>>>0<w>>>0|(q|0)==(w|0)&m>>>0<(h?j:i)>>>0?2:h?3:4}if((e|0)==0&(g|0)==0)d=0.0;else d=+U(+((+(e>>>0)+4294967296.0*+(g>>>0))*.6931471805599453/+(b>>>0)))/.6931471805599453;n[c>>2]=d;if((l|0)==0&(p|0)==0)d=0.0;else d=+U(+((+(l>>>0)+4294967296.0*+(p>>>0))*.6931471805599453/+(b>>>0)))/.6931471805599453;n[c+4>>2]=d;if((m|0)==0&(q|0)==0)d=0.0;else d=+U(+((+(m>>>0)+4294967296.0*+(q>>>0))*.6931471805599453/+(b>>>0)))/.6931471805599453;n[c+8>>2]=d;if((j|0)==0&(o|0)==0)d=0.0;else d=+U(+((+(j>>>0)+4294967296.0*+(o>>>0))*.6931471805599453/+(b>>>0)))/.6931471805599453;n[c+12>>2]=d;if((i|0)==0&(k|0)==0){d=0.0;w=c+16|0;n[w>>2]=d;return h|0}d=+U(+((+(i>>>0)+4294967296.0*+(k>>>0))*.6931471805599453/+(b>>>0)))/.6931471805599453;w=c+16|0;n[w>>2]=d;return h|0}function Rb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;switch(c|0){case 0:{af(d|0,a|0,b<<2|0)|0;return}case 1:{if((b|0)<=0)return;c=0;do{f[d+(c<<2)>>2]=(f[a+(c<<2)>>2]|0)-(f[a+(c+-1<<2)>>2]|0);c=c+1|0}while((c|0)!=(b|0));return}case 2:{if((b|0)<=0)return;c=0;do{f[d+(c<<2)>>2]=(f[a+(c<<2)>>2]|0)-(f[a+(c+-1<<2)>>2]<<1)+(f[a+(c+-2<<2)>>2]|0);c=c+1|0}while((c|0)!=(b|0));return}case 3:{if((b|0)<=0)return;c=0;do{f[d+(c<<2)>>2]=(f[a+(c<<2)>>2]|0)-(f[a+(c+-3<<2)>>2]|0)+(((f[a+(c+-2<<2)>>2]|0)-(f[a+(c+-1<<2)>>2]|0)|0)*3|0);c=c+1|0}while((c|0)!=(b|0));return}case 4:{if((b|0)<=0)return;c=0;do{f[d+(c<<2)>>2]=(f[a+(c<<2)>>2]|0)-(f[a+(c+-1<<2)>>2]<<2)+((f[a+(c+-2<<2)>>2]|0)*6|0)-(f[a+(c+-3<<2)>>2]<<2)+(f[a+(c+-4<<2)>>2]|0);c=c+1|0}while((c|0)!=(b|0));return}default:return}}function Sb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,g=0,h=0,i=0;switch(c|0){case 0:{af(d|0,a|0,b<<2|0)|0;return}case 1:{if((b|0)<=0)return;c=0;e=f[d+-4>>2]|0;do{e=e+(f[a+(c<<2)>>2]|0)|0;f[d+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}case 2:{if((b|0)<=0)return;c=0;e=f[d+-4>>2]|0;do{e=(e<<1)+(f[a+(c<<2)>>2]|0)-(f[d+(c+-2<<2)>>2]|0)|0;f[d+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}case 3:{if((b|0)<=0)return;c=0;e=f[d+-4>>2]|0;g=f[d+-12>>2]|0;do{h=g;g=f[d+(c+-2<<2)>>2]|0;e=h+(f[a+(c<<2)>>2]|0)+((e-g|0)*3|0)|0;f[d+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}case 4:{if((b|0)<=0)return;c=0;e=f[d+-12>>2]|0;g=f[d+-4>>2]|0;do{h=e;e=f[d+(c+-2<<2)>>2]|0;i=W(e,-6)|0;g=i+(f[a+(c<<2)>>2]|0)-(f[d+(c+-4<<2)>>2]|0)+(h+g<<2)|0;f[d+(c<<2)>>2]=g;c=c+1|0}while((c|0)!=(b|0));return}default:return}}function Tb(a){a=a|0;return (a+-1|0)>>>0<655350|0}function Ub(a,b){a=a|0;b=b|0;return a>>>0<16385&(a>>>0<4609|b>>>0>48e3)&1|0}function Vb(a){a=a|0;if((a+-1|0)>>>0>655349)return 0;else return (((a>>>0)%10|0|0)==0|(a>>>0<65536|((a>>>0)%1e3|0|0)==0))&1|0;return 0}function Wb(a){a=a|0;var b=0,c=0,d=0,e=0,g=0,h=0,i=0,j=0;g=f[a>>2]|0;if(!g){g=1;return g|0}c=f[a+4>>2]|0;a=0;b=1;d=0;e=0;while(1){j=c+(a*24|0)|0;h=e;e=f[j>>2]|0;i=d;d=f[j+4>>2]|0;if(!b?!((e|0)==-1&(d|0)==-1|(d>>>0>i>>>0|(d|0)==(i|0)&e>>>0>h>>>0)):0){a=0;b=6;break}a=a+1|0;if(a>>>0>=g>>>0){a=1;b=6;break}else b=0}if((b|0)==6)return a|0;return 0}function Xb(a){a=a|0;var b=0,c=0,d=0,e=0,g=0,h=0,i=0,j=0;b=f[a>>2]|0;if(!b){i=0;return i|0}i=a+4|0;ke(f[i>>2]|0,b,24,6);if(!(f[a>>2]|0)){i=0;return i|0}e=f[a>>2]|0;if(e>>>0>1){b=1;h=1;do{g=f[i>>2]|0;d=g+(h*24|0)|0;c=f[d>>2]|0;d=f[d+4>>2]|0;if(!(!((c|0)==-1&(d|0)==-1)?(j=g+((b+-1|0)*24|0)|0,(c|0)==(f[j>>2]|0)?(d|0)==(f[j+4>>2]|0):0):0)){e=g+(b*24|0)|0;j=g+(h*24|0)|0;f[e>>2]=f[j>>2];f[e+4>>2]=f[j+4>>2];f[e+8>>2]=f[j+8>>2];f[e+12>>2]=f[j+12>>2];f[e+16>>2]=f[j+16>>2];f[e+20>>2]=f[j+20>>2];b=b+1|0;e=f[a>>2]|0}h=h+1|0}while(h>>>0<e>>>0)}else b=1;if(b>>>0>=e>>>0){j=b;return j|0}d=f[i>>2]|0;c=b;do{j=d+(c*24|0)|0;f[j>>2]=-1;f[j+4>>2]=-1;j=d+(c*24|0)+8|0;f[j>>2]=0;f[j+4>>2]=0;f[d+(c*24|0)+16>>2]=0;c=c+1|0}while((c|0)!=(e|0));return b|0}function Yb(a,b){a=a|0;b=b|0;var c=0,d=0;d=a;a=f[d>>2]|0;d=f[d+4>>2]|0;c=b;b=f[c>>2]|0;c=f[c+4>>2]|0;return ((a|0)==(b|0)&(d|0)==(c|0)?0:d>>>0<c>>>0|(d|0)==(c|0)&a>>>0<b>>>0?-1:1)|0}function Zb(a){a=a|0;var c=0,d=0,e=0,f=0,g=0,i=0,j=0;j=b[a>>0]|0;e=j&255;if(!(e&128)){j=1;return j|0}if((e&224|0)==192?(b[a+1>>0]&-64)<<24>>24==-128:0){j=(e&254|0)==192?0:2;return j|0}if(((j&-16)<<24>>24==-32?(c=b[a+1>>0]|0,d=c&255,(d&192|0)==128):0)?(f=b[a+2>>0]|0,(f&-64)<<24>>24==-128):0){if(j<<24>>24==-32&(d&224|0)==128){j=0;return j|0}switch(j<<24>>24){case -19:{if((c&-32)<<24>>24==-96){j=0;return j|0}break}case -17:{if(c<<24>>24==-65&(f&-2)<<24>>24==-66){j=0;return j|0}break}default:{}}j=3;return j|0}if((((j&-8)<<24>>24==-16?(g=h[a+1>>0]|0,(g&192|0)==128):0)?(b[a+2>>0]&-64)<<24>>24==-128:0)?(b[a+3>>0]&-64)<<24>>24==-128:0)return (j<<24>>24==-16&(g&240|0)==128?0:4)|0;if(((((j&-4)<<24>>24==-8?(i=h[a+1>>0]|0,(i&192|0)==128):0)?(b[a+2>>0]&-64)<<24>>24==-128:0)?(b[a+3>>0]&-64)<<24>>24==-128:0)?(b[a+4>>0]&-64)<<24>>24==-128:0){j=j<<24>>24==-8&(i&248|0)==128?0:5;return j|0}if((j&-2)<<24>>24!=-4){j=0;return j|0}c=h[a+1>>0]|0;if((c&192|0)!=128){j=0;return j|0}if((b[a+2>>0]&-64)<<24>>24!=-128){j=0;return j|0}if((b[a+3>>0]&-64)<<24>>24!=-128){j=0;return j|0}if((b[a+4>>0]&-64)<<24>>24==-128)return ((b[a+5>>0]&-64)<<24>>24==-128?(j<<24>>24==-4&(c&252|0)==128?0:6):0)|0;else{j=0;return j|0}return 0}function _b(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,g=0,i=0,j=0,k=0,l=0,m=0;g=(c|0)!=0;if(g){c=a+136|0;e=f[c>>2]|0;c=f[c+4>>2]|0;if(c>>>0<0|(c|0)==0&e>>>0<88200){if(!d){l=0;return l|0}f[d>>2]=2357;l=0;return l|0}k=Xe(e|0,c|0,588,0)|0;if(!((k|0)==0&(H|0)==0)){if(!d){l=0;return l|0}f[d>>2]=2422;l=0;return l|0}}k=f[a+148>>2]|0;if(!k){if(!d){l=0;return l|0}f[d>>2]=2493;l=0;return l|0}i=f[a+152>>2]|0;j=k+-1|0;a:do if(g){if((b[i+(j<<5)+8>>0]|0)!=-86){if(!d){l=0;return l|0}f[d>>2]=2547;l=0;return l|0}i=f[a+152>>2]|0;j=k+-1|0;c=0;b:while(1){e=b[i+(c<<5)+8>>0]|0;if(!(e<<24>>24)){l=31;break a}if(!((e&255)<100|e<<24>>24==-86)){l=33;break}a=i+(c<<5)|0;a=Xe(f[a>>2]|0,f[a+4>>2]|0,588,0)|0;if(!((a|0)==0&(H|0)==0)){l=35;break}g=b[i+(c<<5)+23>>0]|0;e=g<<24>>24==0;if(c>>>0<j>>>0){if(e){l=41;break a}if((h[(f[i+(c<<5)+24>>2]|0)+8>>0]|0)>1){l=44;break a}else l=25}else if(!e)l=25;if((l|0)==25){l=0;a=f[i+(c<<5)+24>>2]|0;g=g&255;e=0;do{m=a+(e<<4)|0;m=Xe(f[m>>2]|0,f[m+4>>2]|0,588,0)|0;if(!((m|0)==0&(H|0)==0)){l=49;break b}if(e|0?((h[a+(e+-1<<4)+8>>0]|0)+1|0)!=(h[a+(e<<4)+8>>0]|0):0){l=52;break a}e=e+1|0}while(e>>>0<g>>>0)}c=c+1|0;if(c>>>0>=k>>>0){c=1;l=56;break}}if((l|0)==33){if(!d){m=0;return m|0}f[d>>2]=2648;m=0;return m|0}else if((l|0)==35){if(!d){m=0;return m|0}if((c|0)==(j|0)){f[d>>2]=2697;m=0;return m|0}else{f[d>>2]=2769;m=0;return m|0}}else if((l|0)==49){if(!d){m=0;return m|0}f[d>>2]=2941;m=0;return m|0}else if((l|0)==56)return c|0}else{a=0;while(1){if(!(b[i+(a<<5)+8>>0]|0)){l=31;break a}e=b[i+(a<<5)+23>>0]|0;c=e<<24>>24==0;if(a>>>0<j>>>0){if(c){l=41;break a}if((h[(f[i+(a<<5)+24>>2]|0)+8>>0]|0)>1){l=44;break a}else l=47}else if(!c)l=47;if((l|0)==47){l=0;g=i+(a<<5)+24|0;e=e&255;c=0;do{if(c|0?(m=f[g>>2]|0,((h[m+(c+-1<<4)+8>>0]|0)+1|0)!=(h[m+(c<<4)+8>>0]|0)):0){l=52;break a}c=c+1|0}while(c>>>0<e>>>0)}a=a+1|0;if(a>>>0>=k>>>0){c=1;break}}return c|0}while(0);if((l|0)==31){if(!d){m=0;return m|0}f[d>>2]=2608;m=0;return m|0}else if((l|0)==41){if(!d){m=0;return m|0}f[d>>2]=2838;m=0;return m|0}else if((l|0)==44){if(!d){m=0;return m|0}f[d>>2]=2889;m=0;return m|0}else if((l|0)==52){if(!d){m=0;return m|0}f[d>>2]=3016;m=0;return m|0}return 0}function $b(a,c){a=a|0;c=c|0;var d=0,e=0,g=0;e=f[a+4>>2]|0;d=b[e>>0]|0;a:do if(d<<24>>24){while(1){e=e+1|0;if(d<<24>>24<32|d<<24>>24==127)break;d=b[e>>0]|0;if(!(d<<24>>24))break a}if(!c){g=0;return g|0}f[c>>2]=3065;g=0;return g|0}while(0);d=f[a+8>>2]|0;if(!(b[d>>0]|0)){g=1;return g|0}while(1){e=Zb(d)|0;if(!e)break;d=d+e|0;if(!(b[d>>0]|0)){d=1;g=13;break}}if((g|0)==13)return d|0;if(!c){g=0;return g|0}f[c>>2]=3139;g=0;return g|0}function ac(a,b,c){a=a|0;b=b|0;c=c|0;while(1)if((a|0)!=0&b>>>a>>>0<=c>>>0)a=a+-1|0;else break;return a|0}function bc(a){a=a|0;var b=0;if(!(a&1)){b=a;a=0;while(1){a=a+1|0;if(b&2|0)break;else b=b>>>1}if(a>>>0>=15){b=15;return b|0}}else a=0;b=a;return b|0}function cc(a){a=a|0;f[a>>2]=0;f[a+4>>2]=0;f[a+8>>2]=0;return}function dc(a){a=a|0;var b=0,c=0;b=f[a>>2]|0;if(b|0)Md(b);b=a+4|0;c=f[b>>2]|0;if(c|0)Md(c);f[a>>2]=0;f[b>>2]=0;f[a+8>>2]=0;return}function ec(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0,i=0;g=a+8|0;if((f[g>>2]|0)>>>0>=b>>>0){i=1;return i|0}c=f[a>>2]|0;h=4<<b;d=Od(c,h)|0;i=b>>>0<30;e=(d|0)==0;if(i&e)Md(c);f[a>>2]=d;if(e){i=0;return i|0}a=a+4|0;c=f[a>>2]|0;d=Od(c,h)|0;e=(d|0)==0;if(i&e)Md(c);f[a>>2]=d;if(e){i=0;return i|0}cf(d|0,0,h|0)|0;f[g>>2]=b;i=1;return i|0}function fc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;if(!d)return;e=0;do{n[c+(e<<2)>>2]=+n[b+(e<<2)>>2]*+(f[a+(e<<2)>>2]|0);e=e+1|0}while((e|0)!=(d|0));return}function gc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0.0,g=0,h=0,i=0;h=b-c|0;if(!c){e=0;do e=e+1|0;while(e>>>0<=h>>>0)}else{cf(d|0,0,c<<2|0)|0;e=0;do{f=+n[a+(e<<2)>>2];g=0;do{i=d+(g<<2)|0;n[i>>2]=+n[i>>2]+f*+n[a+(g+e<<2)>>2];g=g+1|0}while((g|0)!=(c|0));e=e+1|0}while(e>>>0<=h>>>0)}if(e>>>0>=b>>>0)return;c=e;g=b-e|0;while(1){f=+n[a+(c<<2)>>2];if((c|0)!=(b|0)){e=0;do{i=d+(e<<2)|0;n[i>>2]=+n[i>>2]+f*+n[a+(e+c<<2)>>2];e=e+1|0}while((e|0)!=(g|0))}c=c+1|0;if((c|0)==(b|0))break;else g=g+-1|0}return}function hc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0.0,g=0,h=0,i=0,j=0.0,k=0,l=0,m=0,o=0,q=0,r=0,s=0,t=0,v=0.0,w=0;s=u;u=u+256|0;m=s;o=f[b>>2]|0;if(!o){u=s;return}j=+n[a>>2];k=0;q=1;while(1){g=k>>>1;l=k;k=k+1|0;e=-+n[a+(k<<2)>>2];if(l){h=0;do{e=e-+p[m+(h<<3)>>3]*+n[a+(l-h<<2)>>2];h=h+1|0}while((h|0)!=(l|0));e=e/j;p[m+(l<<3)>>3]=e;if(!g)g=0;else{i=l+-1|0;h=0;do{w=m+(h<<3)|0;v=+p[w>>3];t=m+(i-h<<3)|0;p[w>>3]=v+e*+p[t>>3];p[t>>3]=e*v+ +p[t>>3];h=h+1|0}while((h|0)!=(g|0))}g=m+(g<<3)|0;if(l&1){v=+p[g>>3];p[g>>3]=v+e*v}}else{e=e/j;p[m+(l<<3)>>3]=e}e=1.0-e*e;g=0;do{n[c+(l<<7)+(g<<2)>>2]=-+p[m+(g<<3)>>3];g=g+1|0}while((g|0)!=(q|0));j=j*e;p[d+(l<<3)>>3]=j;if(j==0.0)break;if(k>>>0>=o>>>0){r=17;break}else q=q+1|0}if((r|0)==17){u=s;return}f[b>>2]=k;u=s;return}function ic(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var g=0.0,h=0,i=0.0,j=0,k=0,l=0,m=0,o=0,p=0,q=0;q=u;u=u+16|0;k=q;h=c+-1|0;m=1<<h;o=0-m|0;p=m+-1|0;l=(b|0)==0;if(l){e=2;u=q;return e|0}c=0;g=0.0;do{i=+J(+(+n[a+(c<<2)>>2]));g=g<i?i:g;c=c+1|0}while((c|0)!=(b|0));if(g<=0.0){e=2;u=q;return e|0}j=1<<(f[275]|0)+-1;+ie(g,k);c=(f[k>>2]|0)+-1|0;f[k>>2]=c;h=h-c|0;c=h+-1|0;f[e>>2]=c;if((h|0)<=(j|0)){if((h|0)<(1-j|0)){e=1;u=q;return e|0}}else{c=j+-1|0;f[e>>2]=c}if((c|0)<=-1){if(!l){i=+(1<<0-c|0);g=0.0;h=0;while(1){g=g+ +n[a+(h<<2)>>2]/i;c=Fe(g)|0;c=(c|0)<(m|0)?((c|0)<(o|0)?o:c):p;f[d+(h<<2)>>2]=c;h=h+1|0;if((h|0)==(b|0))break;else g=g-+(c|0)}}f[e>>2]=0;e=0;u=q;return e|0}if(l){e=0;u=q;return e|0}g=0.0;h=0;while(1){g=g+ +n[a+(h<<2)>>2]*+(1<<c|0);c=Fe(g)|0;c=(c|0)<(m|0)?((c|0)<(o|0)?o:c):p;f[d+(h<<2)>>2]=c;h=h+1|0;if((h|0)==(b|0)){c=0;break}g=g-+(c|0);c=f[e>>2]|0}u=q;return c|0}function jc(a,b,c,d,e,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;if(d>>>0>=13){if((b|0)<=0)return;j=c+124|0;k=c+120|0;l=c+116|0;m=c+112|0;n=c+108|0;o=c+104|0;p=c+100|0;q=c+96|0;r=c+92|0;s=c+88|0;t=c+84|0;u=c+80|0;v=c+76|0;w=c+72|0;x=c+68|0;y=c+64|0;z=c+60|0;A=c+56|0;B=c+52|0;C=c+48|0;D=c+44|0;E=c+40|0;F=c+36|0;G=c+32|0;H=c+28|0;I=c+24|0;J=c+20|0;K=c+16|0;L=c+12|0;M=c+8|0;N=c+4|0;i=0;do{switch(d|0){case 32:{h=W(f[a+(i+-32<<2)>>2]|0,f[j>>2]|0)|0;O=53;break}case 31:{h=0;O=53;break}case 30:{h=0;O=54;break}case 29:{h=0;O=55;break}case 28:{h=0;O=56;break}case 27:{h=0;O=57;break}case 26:{h=0;O=58;break}case 25:{h=0;O=59;break}case 24:{h=0;O=60;break}case 23:{h=0;O=61;break}case 22:{h=0;O=62;break}case 21:{h=0;O=63;break}case 20:{h=0;O=64;break}case 19:{h=0;O=65;break}case 18:{h=0;O=66;break}case 17:{h=0;O=67;break}case 16:{h=0;O=68;break}case 15:{h=0;O=69;break}case 14:{h=0;O=70;break}case 13:{h=0;O=71;break}default:h=0}if((O|0)==53){h=(W(f[a+(i+-31<<2)>>2]|0,f[k>>2]|0)|0)+h|0;O=54}if((O|0)==54){h=(W(f[a+(i+-30<<2)>>2]|0,f[l>>2]|0)|0)+h|0;O=55}if((O|0)==55){h=(W(f[a+(i+-29<<2)>>2]|0,f[m>>2]|0)|0)+h|0;O=56}if((O|0)==56){h=(W(f[a+(i+-28<<2)>>2]|0,f[n>>2]|0)|0)+h|0;O=57}if((O|0)==57){h=(W(f[a+(i+-27<<2)>>2]|0,f[o>>2]|0)|0)+h|0;O=58}if((O|0)==58){h=(W(f[a+(i+-26<<2)>>2]|0,f[p>>2]|0)|0)+h|0;O=59}if((O|0)==59){h=(W(f[a+(i+-25<<2)>>2]|0,f[q>>2]|0)|0)+h|0;O=60}if((O|0)==60){h=(W(f[a+(i+-24<<2)>>2]|0,f[r>>2]|0)|0)+h|0;O=61}if((O|0)==61){h=(W(f[a+(i+-23<<2)>>2]|0,f[s>>2]|0)|0)+h|0;O=62}if((O|0)==62){h=(W(f[a+(i+-22<<2)>>2]|0,f[t>>2]|0)|0)+h|0;O=63}if((O|0)==63){h=(W(f[a+(i+-21<<2)>>2]|0,f[u>>2]|0)|0)+h|0;O=64}if((O|0)==64){h=(W(f[a+(i+-20<<2)>>2]|0,f[v>>2]|0)|0)+h|0;O=65}if((O|0)==65){h=(W(f[a+(i+-19<<2)>>2]|0,f[w>>2]|0)|0)+h|0;O=66}if((O|0)==66){h=(W(f[a+(i+-18<<2)>>2]|0,f[x>>2]|0)|0)+h|0;O=67}if((O|0)==67){h=(W(f[a+(i+-17<<2)>>2]|0,f[y>>2]|0)|0)+h|0;O=68}if((O|0)==68){h=(W(f[a+(i+-16<<2)>>2]|0,f[z>>2]|0)|0)+h|0;O=69}if((O|0)==69){h=(W(f[a+(i+-15<<2)>>2]|0,f[A>>2]|0)|0)+h|0;O=70}if((O|0)==70){h=(W(f[a+(i+-14<<2)>>2]|0,f[B>>2]|0)|0)+h|0;O=71}if((O|0)==71){O=0;h=(W(f[a+(i+-13<<2)>>2]|0,f[C>>2]|0)|0)+h|0;h=h+(W(f[a+(i+-12<<2)>>2]|0,f[D>>2]|0)|0)|0;h=h+(W(f[a+(i+-11<<2)>>2]|0,f[E>>2]|0)|0)|0;h=h+(W(f[a+(i+-10<<2)>>2]|0,f[F>>2]|0)|0)|0;h=h+(W(f[a+(i+-9<<2)>>2]|0,f[G>>2]|0)|0)|0;h=h+(W(f[a+(i+-8<<2)>>2]|0,f[H>>2]|0)|0)|0;h=h+(W(f[a+(i+-7<<2)>>2]|0,f[I>>2]|0)|0)|0;h=h+(W(f[a+(i+-6<<2)>>2]|0,f[J>>2]|0)|0)|0;h=h+(W(f[a+(i+-5<<2)>>2]|0,f[K>>2]|0)|0)|0;h=h+(W(f[a+(i+-4<<2)>>2]|0,f[L>>2]|0)|0)|0;h=h+(W(f[a+(i+-3<<2)>>2]|0,f[M>>2]|0)|0)|0;h=h+(W(f[a+(i+-2<<2)>>2]|0,f[N>>2]|0)|0)|0;h=h+(W(f[a+(i+-1<<2)>>2]|0,f[c>>2]|0)|0)|0}f[g+(i<<2)>>2]=(f[a+(i<<2)>>2]|0)-(h>>e);i=i+1|0}while((i|0)!=(b|0));return}if(d>>>0>8)if(d>>>0>10){h=(b|0)>0;if((d|0)==12){if(!h)return;v=f[c+44>>2]|0;w=f[c+40>>2]|0;x=f[c+36>>2]|0;y=f[c+32>>2]|0;z=f[c+28>>2]|0;A=f[c+24>>2]|0;B=f[c+20>>2]|0;C=f[c+16>>2]|0;D=f[c+12>>2]|0;E=f[c+8>>2]|0;F=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-48>>2]|0;k=f[a+-44>>2]|0;l=f[a+-40>>2]|0;m=f[a+-36>>2]|0;n=f[a+-32>>2]|0;o=f[a+-28>>2]|0;p=f[a+-24>>2]|0;q=f[a+-20>>2]|0;r=f[a+-16>>2]|0;s=f[a+-12>>2]|0;t=f[a+-8>>2]|0;u=f[a+-4>>2]|0;while(1){c=(W(k,w)|0)+(W(h,v)|0)+(W(l,x)|0)+(W(m,y)|0)+(W(n,z)|0)+(W(o,A)|0)+(W(p,B)|0)+(W(q,C)|0)+(W(r,D)|0)+(W(s,E)|0)+(W(t,F)|0)+(W(u,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{G=u;H=t;I=s;J=r;K=q;L=p;M=o;N=n;O=m;d=l;c=k;u=h;t=G;s=H;r=I;q=J;p=K;o=L;n=M;m=N;l=O;k=d;h=c}}return}else{if(!h)return;u=f[c+40>>2]|0;v=f[c+36>>2]|0;w=f[c+32>>2]|0;x=f[c+28>>2]|0;y=f[c+24>>2]|0;z=f[c+20>>2]|0;A=f[c+16>>2]|0;B=f[c+12>>2]|0;C=f[c+8>>2]|0;D=f[c+4>>2]|0;s=f[c>>2]|0;i=0;j=f[a+-36>>2]|0;k=f[a+-32>>2]|0;l=f[a+-28>>2]|0;m=f[a+-24>>2]|0;n=f[a+-20>>2]|0;o=f[a+-16>>2]|0;p=f[a+-12>>2]|0;q=f[a+-8>>2]|0;r=f[a+-4>>2]|0;h=f[a+-44>>2]|0;t=f[a+-40>>2]|0;while(1){c=(W(t,v)|0)+(W(h,u)|0)+(W(j,w)|0)+(W(k,x)|0)+(W(l,y)|0)+(W(m,z)|0)+(W(n,A)|0)+(W(o,B)|0)+(W(p,C)|0)+(W(q,D)|0)+(W(r,s)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{I=t;J=r;K=q;L=p;M=o;N=n;O=m;d=l;c=k;r=h;t=j;h=I;q=J;p=K;o=L;n=M;m=N;l=O;k=d;j=c}}return}}else{h=(b|0)>0;if((d|0)==10){if(!h)return;t=f[c+36>>2]|0;u=f[c+32>>2]|0;v=f[c+28>>2]|0;w=f[c+24>>2]|0;x=f[c+20>>2]|0;y=f[c+16>>2]|0;z=f[c+12>>2]|0;A=f[c+8>>2]|0;B=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-40>>2]|0;k=f[a+-36>>2]|0;l=f[a+-32>>2]|0;m=f[a+-28>>2]|0;n=f[a+-24>>2]|0;o=f[a+-20>>2]|0;p=f[a+-16>>2]|0;q=f[a+-12>>2]|0;r=f[a+-8>>2]|0;s=f[a+-4>>2]|0;while(1){c=(W(k,u)|0)+(W(h,t)|0)+(W(l,v)|0)+(W(m,w)|0)+(W(n,x)|0)+(W(o,y)|0)+(W(p,z)|0)+(W(q,A)|0)+(W(r,B)|0)+(W(s,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{I=s;J=r;K=q;L=p;M=o;N=n;O=m;d=l;c=k;s=h;r=I;q=J;p=K;o=L;n=M;m=N;l=O;k=d;h=c}}return}else{if(!h)return;s=f[c+32>>2]|0;t=f[c+28>>2]|0;u=f[c+24>>2]|0;v=f[c+20>>2]|0;w=f[c+16>>2]|0;x=f[c+12>>2]|0;y=f[c+8>>2]|0;z=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-36>>2]|0;k=f[a+-32>>2]|0;l=f[a+-28>>2]|0;m=f[a+-24>>2]|0;n=f[a+-20>>2]|0;o=f[a+-16>>2]|0;p=f[a+-12>>2]|0;q=f[a+-8>>2]|0;r=f[a+-4>>2]|0;while(1){c=(W(k,t)|0)+(W(h,s)|0)+(W(l,u)|0)+(W(m,v)|0)+(W(n,w)|0)+(W(o,x)|0)+(W(p,y)|0)+(W(q,z)|0)+(W(r,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{J=r;K=q;L=p;M=o;N=n;O=m;d=l;c=k;r=h;q=J;p=K;o=L;n=M;m=N;l=O;k=d;h=c}}return}}if(d>>>0>4)if(d>>>0>6){h=(b|0)>0;if((d|0)==8){if(!h)return;r=f[c+28>>2]|0;s=f[c+24>>2]|0;t=f[c+20>>2]|0;u=f[c+16>>2]|0;v=f[c+12>>2]|0;w=f[c+8>>2]|0;x=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-32>>2]|0;k=f[a+-28>>2]|0;l=f[a+-24>>2]|0;m=f[a+-20>>2]|0;n=f[a+-16>>2]|0;o=f[a+-12>>2]|0;p=f[a+-8>>2]|0;q=f[a+-4>>2]|0;while(1){c=(W(k,s)|0)+(W(h,r)|0)+(W(l,t)|0)+(W(m,u)|0)+(W(n,v)|0)+(W(o,w)|0)+(W(p,x)|0)+(W(q,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{K=q;L=p;M=o;N=n;O=m;d=l;c=k;q=h;p=K;o=L;n=M;m=N;l=O;k=d;h=c}}return}else{if(!h)return;q=f[c+24>>2]|0;r=f[c+20>>2]|0;s=f[c+16>>2]|0;t=f[c+12>>2]|0;u=f[c+8>>2]|0;v=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-28>>2]|0;k=f[a+-24>>2]|0;l=f[a+-20>>2]|0;m=f[a+-16>>2]|0;n=f[a+-12>>2]|0;o=f[a+-8>>2]|0;p=f[a+-4>>2]|0;while(1){c=(W(k,r)|0)+(W(h,q)|0)+(W(l,s)|0)+(W(m,t)|0)+(W(n,u)|0)+(W(o,v)|0)+(W(p,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{L=p;M=o;N=n;O=m;d=l;c=k;p=h;o=L;n=M;m=N;l=O;k=d;h=c}}return}}else{h=(b|0)>0;if((d|0)==6){if(!h)return;p=f[c+20>>2]|0;q=f[c+16>>2]|0;r=f[c+12>>2]|0;s=f[c+8>>2]|0;t=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-24>>2]|0;k=f[a+-20>>2]|0;l=f[a+-16>>2]|0;m=f[a+-12>>2]|0;n=f[a+-8>>2]|0;o=f[a+-4>>2]|0;while(1){c=(W(k,q)|0)+(W(h,p)|0)+(W(l,r)|0)+(W(m,s)|0)+(W(n,t)|0)+(W(o,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{M=o;N=n;O=m;d=l;c=k;o=h;n=M;m=N;l=O;k=d;h=c}}return}else{if(!h)return;o=f[c+16>>2]|0;p=f[c+12>>2]|0;q=f[c+8>>2]|0;r=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-20>>2]|0;k=f[a+-16>>2]|0;l=f[a+-12>>2]|0;m=f[a+-8>>2]|0;n=f[a+-4>>2]|0;while(1){c=(W(k,p)|0)+(W(h,o)|0)+(W(l,q)|0)+(W(m,r)|0)+(W(n,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{N=n;O=m;d=l;c=k;n=h;m=N;l=O;k=d;h=c}}return}}else if(d>>>0>2){h=(b|0)>0;if((d|0)==4){if(!h)return;n=f[c+12>>2]|0;o=f[c+8>>2]|0;p=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-16>>2]|0;k=f[a+-12>>2]|0;l=f[a+-8>>2]|0;m=f[a+-4>>2]|0;while(1){c=(W(k,o)|0)+(W(h,n)|0)+(W(l,p)|0)+(W(m,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{O=m;d=l;c=k;m=h;l=O;k=d;h=c}}return}else{if(!h)return;m=f[c+8>>2]|0;n=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-12>>2]|0;k=f[a+-8>>2]|0;l=f[a+-4>>2]|0;while(1){c=(W(k,n)|0)+(W(h,m)|0)+(W(l,j)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{d=l;c=k;l=h;k=d;h=c}}return}}else{h=(b|0)>0;if((d|0)==2){if(!h)return;l=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[a+-8>>2]|0;k=f[a+-4>>2]|0;while(1){c=(W(k,j)|0)+(W(h,l)|0)|0;h=f[a+(i<<2)>>2]|0;f[g+(i<<2)>>2]=h-(c>>e);i=i+1|0;if((i|0)==(b|0))break;else{c=k;k=h;h=c}}return}else{if(!h)return;i=f[c>>2]|0;h=0;j=f[a+-4>>2]|0;do{c=j;j=f[a+(h<<2)>>2]|0;c=j-((W(c,i)|0)>>e)|0;f[g+(h<<2)>>2]=c;h=h+1|0}while((h|0)!=(b|0));return}}}function kc(a,b,c,d,e,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0;if(d>>>0>=13){if((b|0)<=0)return;k=c+124|0;l=c+120|0;m=c+116|0;n=c+112|0;o=c+108|0;p=c+104|0;q=c+100|0;r=c+96|0;s=c+92|0;t=c+88|0;u=c+84|0;v=c+80|0;w=c+76|0;x=c+72|0;y=c+68|0;z=c+64|0;A=c+60|0;B=c+56|0;C=c+52|0;D=c+48|0;E=c+44|0;F=c+40|0;G=c+36|0;I=c+32|0;J=c+28|0;K=c+24|0;L=c+20|0;M=c+16|0;N=c+12|0;O=c+8|0;P=c+4|0;j=0;do{switch(d|0){case 32:{h=f[k>>2]|0;i=f[a+(j+-32<<2)>>2]|0;h=Re(i|0,((i|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;i=H;Q=53;break}case 31:{h=0;i=0;Q=53;break}case 30:{h=0;i=0;Q=54;break}case 29:{h=0;i=0;Q=55;break}case 28:{h=0;i=0;Q=56;break}case 27:{h=0;i=0;Q=57;break}case 26:{h=0;i=0;Q=58;break}case 25:{h=0;i=0;Q=59;break}case 24:{h=0;i=0;Q=60;break}case 23:{h=0;i=0;Q=61;break}case 22:{h=0;i=0;Q=62;break}case 21:{h=0;i=0;Q=63;break}case 20:{h=0;i=0;Q=64;break}case 19:{h=0;i=0;Q=65;break}case 18:{h=0;i=0;Q=66;break}case 17:{h=0;i=0;Q=67;break}case 16:{h=0;i=0;Q=68;break}case 15:{h=0;i=0;Q=69;break}case 14:{h=0;i=0;Q=70;break}case 13:{h=0;i=0;Q=71;break}default:{h=0;i=0}}if((Q|0)==53){R=f[l>>2]|0;S=f[a+(j+-31<<2)>>2]|0;R=Re(S|0,((S|0)<0)<<31>>31|0,R|0,((R|0)<0)<<31>>31|0)|0;h=Se(R|0,H|0,h|0,i|0)|0;i=H;Q=54}if((Q|0)==54){S=f[m>>2]|0;R=f[a+(j+-30<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=55}if((Q|0)==55){S=f[n>>2]|0;R=f[a+(j+-29<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=56}if((Q|0)==56){S=f[o>>2]|0;R=f[a+(j+-28<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=57}if((Q|0)==57){S=f[p>>2]|0;R=f[a+(j+-27<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=58}if((Q|0)==58){S=f[q>>2]|0;R=f[a+(j+-26<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=59}if((Q|0)==59){S=f[r>>2]|0;R=f[a+(j+-25<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=60}if((Q|0)==60){S=f[s>>2]|0;R=f[a+(j+-24<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=61}if((Q|0)==61){S=f[t>>2]|0;R=f[a+(j+-23<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=62}if((Q|0)==62){S=f[u>>2]|0;R=f[a+(j+-22<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=63}if((Q|0)==63){S=f[v>>2]|0;R=f[a+(j+-21<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=64}if((Q|0)==64){S=f[w>>2]|0;R=f[a+(j+-20<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=65}if((Q|0)==65){S=f[x>>2]|0;R=f[a+(j+-19<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=66}if((Q|0)==66){S=f[y>>2]|0;R=f[a+(j+-18<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=67}if((Q|0)==67){S=f[z>>2]|0;R=f[a+(j+-17<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=68}if((Q|0)==68){S=f[A>>2]|0;R=f[a+(j+-16<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=69}if((Q|0)==69){S=f[B>>2]|0;R=f[a+(j+-15<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=70}if((Q|0)==70){S=f[C>>2]|0;R=f[a+(j+-14<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=71}if((Q|0)==71){Q=0;S=f[D>>2]|0;R=f[a+(j+-13<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;S=f[E>>2]|0;R=f[a+(j+-12<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[F>>2]|0;R=f[a+(j+-11<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[G>>2]|0;R=f[a+(j+-10<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[I>>2]|0;R=f[a+(j+-9<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[J>>2]|0;R=f[a+(j+-8<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[K>>2]|0;R=f[a+(j+-7<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[L>>2]|0;R=f[a+(j+-6<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[M>>2]|0;R=f[a+(j+-5<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[N>>2]|0;R=f[a+(j+-4<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[O>>2]|0;R=f[a+(j+-3<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[P>>2]|0;R=f[a+(j+-2<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[c>>2]|0;R=f[a+(j+-1<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H}R=f[a+(j<<2)>>2]|0;S=Ye(h|0,i|0,e|0)|0;f[g+(j<<2)>>2]=R-S;j=j+1|0}while((j|0)!=(b|0));return}if(d>>>0>8)if(d>>>0>10){h=(b|0)>0;if((d|0)==12){if(!h)return;w=f[c+44>>2]|0;x=((w|0)<0)<<31>>31;y=f[c+40>>2]|0;z=((y|0)<0)<<31>>31;A=f[c+36>>2]|0;B=((A|0)<0)<<31>>31;C=f[c+32>>2]|0;D=((C|0)<0)<<31>>31;E=f[c+28>>2]|0;F=((E|0)<0)<<31>>31;G=f[c+24>>2]|0;I=((G|0)<0)<<31>>31;J=f[c+20>>2]|0;K=((J|0)<0)<<31>>31;L=f[c+16>>2]|0;M=((L|0)<0)<<31>>31;N=f[c+12>>2]|0;O=((N|0)<0)<<31>>31;P=f[c+8>>2]|0;Q=((P|0)<0)<<31>>31;d=f[c+4>>2]|0;R=((d|0)<0)<<31>>31;o=f[c>>2]|0;p=((o|0)<0)<<31>>31;i=0;j=f[a+-20>>2]|0;k=f[a+-16>>2]|0;l=f[a+-12>>2]|0;m=f[a+-8>>2]|0;n=f[a+-4>>2]|0;h=f[a+-48>>2]|0;q=f[a+-44>>2]|0;r=f[a+-40>>2]|0;s=f[a+-36>>2]|0;t=f[a+-32>>2]|0;u=f[a+-28>>2]|0;v=f[a+-24>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,w|0,x|0)|0;S=H;c=Re(q|0,((q|0)<0)<<31>>31|0,y|0,z|0)|0;S=Se(c|0,H|0,h|0,S|0)|0;h=H;c=Re(r|0,((r|0)<0)<<31>>31|0,A|0,B|0)|0;c=Se(S|0,h|0,c|0,H|0)|0;h=H;S=Re(s|0,((s|0)<0)<<31>>31|0,C|0,D|0)|0;S=Se(c|0,h|0,S|0,H|0)|0;h=H;c=Re(t|0,((t|0)<0)<<31>>31|0,E|0,F|0)|0;c=Se(S|0,h|0,c|0,H|0)|0;h=H;S=Re(u|0,((u|0)<0)<<31>>31|0,G|0,I|0)|0;S=Se(c|0,h|0,S|0,H|0)|0;h=H;c=Re(v|0,((v|0)<0)<<31>>31|0,J|0,K|0)|0;c=Se(S|0,h|0,c|0,H|0)|0;h=H;S=Re(j|0,((j|0)<0)<<31>>31|0,L|0,M|0)|0;S=Se(c|0,h|0,S|0,H|0)|0;h=H;c=Re(k|0,((k|0)<0)<<31>>31|0,N|0,O|0)|0;c=Se(S|0,h|0,c|0,H|0)|0;h=H;S=Re(l|0,((l|0)<0)<<31>>31|0,P|0,Q|0)|0;S=Se(c|0,h|0,S|0,H|0)|0;h=H;c=Re(m|0,((m|0)<0)<<31>>31|0,d|0,R|0)|0;c=Se(S|0,h|0,c|0,H|0)|0;h=H;S=Re(n|0,((n|0)<0)<<31>>31|0,o|0,p|0)|0;S=Se(c|0,h|0,S|0,H|0)|0;h=f[a+(i<<2)>>2]|0;S=Ye(S|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-S;i=i+1|0;if((i|0)==(b|0))break;else{_=v;Z=u;Y=t;X=s;W=r;V=q;U=n;T=m;c=l;S=k;n=h;v=j;u=_;t=Z;s=Y;r=X;q=W;h=V;m=U;l=T;k=c;j=S}}return}else{if(!h)return;v=f[c+40>>2]|0;w=((v|0)<0)<<31>>31;x=f[c+36>>2]|0;y=((x|0)<0)<<31>>31;z=f[c+32>>2]|0;A=((z|0)<0)<<31>>31;B=f[c+28>>2]|0;C=((B|0)<0)<<31>>31;D=f[c+24>>2]|0;E=((D|0)<0)<<31>>31;F=f[c+20>>2]|0;G=((F|0)<0)<<31>>31;I=f[c+16>>2]|0;J=((I|0)<0)<<31>>31;K=f[c+12>>2]|0;L=((K|0)<0)<<31>>31;M=f[c+8>>2]|0;N=((M|0)<0)<<31>>31;O=f[c+4>>2]|0;P=((O|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-44>>2]|0;l=f[a+-40>>2]|0;m=f[a+-36>>2]|0;n=f[a+-32>>2]|0;o=f[a+-28>>2]|0;p=f[a+-24>>2]|0;q=f[a+-20>>2]|0;r=f[a+-16>>2]|0;s=f[a+-12>>2]|0;t=f[a+-8>>2]|0;u=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,v|0,w|0)|0;Z=H;_=Re(l|0,((l|0)<0)<<31>>31|0,x|0,y|0)|0;Z=Se(_|0,H|0,h|0,Z|0)|0;h=H;_=Re(m|0,((m|0)<0)<<31>>31|0,z|0,A|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(n|0,((n|0)<0)<<31>>31|0,B|0,C|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(o|0,((o|0)<0)<<31>>31|0,D|0,E|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(p|0,((p|0)<0)<<31>>31|0,F|0,G|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(q|0,((q|0)<0)<<31>>31|0,I|0,J|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(r|0,((r|0)<0)<<31>>31|0,K|0,L|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(s|0,((s|0)<0)<<31>>31|0,M|0,N|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(t|0,((t|0)<0)<<31>>31|0,O|0,P|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(u|0,((u|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{c=u;S=t;T=s;U=r;V=q;W=p;X=o;Y=n;Z=m;_=l;u=h;t=c;s=S;r=T;q=U;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}}else{h=(b|0)>0;if((d|0)==10){if(!h)return;u=f[c+36>>2]|0;v=((u|0)<0)<<31>>31;w=f[c+32>>2]|0;x=((w|0)<0)<<31>>31;y=f[c+28>>2]|0;z=((y|0)<0)<<31>>31;A=f[c+24>>2]|0;B=((A|0)<0)<<31>>31;C=f[c+20>>2]|0;D=((C|0)<0)<<31>>31;E=f[c+16>>2]|0;F=((E|0)<0)<<31>>31;G=f[c+12>>2]|0;I=((G|0)<0)<<31>>31;J=f[c+8>>2]|0;K=((J|0)<0)<<31>>31;L=f[c+4>>2]|0;M=((L|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-40>>2]|0;l=f[a+-36>>2]|0;m=f[a+-32>>2]|0;n=f[a+-28>>2]|0;o=f[a+-24>>2]|0;p=f[a+-20>>2]|0;q=f[a+-16>>2]|0;r=f[a+-12>>2]|0;s=f[a+-8>>2]|0;t=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,u|0,v|0)|0;_=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,w|0,x|0)|0;_=Se(Z|0,H|0,h|0,_|0)|0;h=H;Z=Re(m|0,((m|0)<0)<<31>>31|0,y|0,z|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(n|0,((n|0)<0)<<31>>31|0,A|0,B|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(o|0,((o|0)<0)<<31>>31|0,C|0,D|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(p|0,((p|0)<0)<<31>>31|0,E|0,F|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(q|0,((q|0)<0)<<31>>31|0,G|0,I|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(r|0,((r|0)<0)<<31>>31|0,J|0,K|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(s|0,((s|0)<0)<<31>>31|0,L|0,M|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(t|0,((t|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{S=t;T=s;U=r;V=q;W=p;X=o;Y=n;Z=m;_=l;t=h;s=S;r=T;q=U;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}else{if(!h)return;t=f[c+32>>2]|0;u=((t|0)<0)<<31>>31;v=f[c+28>>2]|0;w=((v|0)<0)<<31>>31;x=f[c+24>>2]|0;y=((x|0)<0)<<31>>31;z=f[c+20>>2]|0;A=((z|0)<0)<<31>>31;B=f[c+16>>2]|0;C=((B|0)<0)<<31>>31;D=f[c+12>>2]|0;E=((D|0)<0)<<31>>31;F=f[c+8>>2]|0;G=((F|0)<0)<<31>>31;I=f[c+4>>2]|0;J=((I|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-36>>2]|0;l=f[a+-32>>2]|0;m=f[a+-28>>2]|0;n=f[a+-24>>2]|0;o=f[a+-20>>2]|0;p=f[a+-16>>2]|0;q=f[a+-12>>2]|0;r=f[a+-8>>2]|0;s=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,t|0,u|0)|0;Z=H;_=Re(l|0,((l|0)<0)<<31>>31|0,v|0,w|0)|0;Z=Se(_|0,H|0,h|0,Z|0)|0;h=H;_=Re(m|0,((m|0)<0)<<31>>31|0,x|0,y|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(n|0,((n|0)<0)<<31>>31|0,z|0,A|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(o|0,((o|0)<0)<<31>>31|0,B|0,C|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(p|0,((p|0)<0)<<31>>31|0,D|0,E|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(q|0,((q|0)<0)<<31>>31|0,F|0,G|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(r|0,((r|0)<0)<<31>>31|0,I|0,J|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(s|0,((s|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{T=s;U=r;V=q;W=p;X=o;Y=n;Z=m;_=l;s=h;r=T;q=U;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}}if(d>>>0>4)if(d>>>0>6){h=(b|0)>0;if((d|0)==8){if(!h)return;s=f[c+28>>2]|0;t=((s|0)<0)<<31>>31;u=f[c+24>>2]|0;v=((u|0)<0)<<31>>31;w=f[c+20>>2]|0;x=((w|0)<0)<<31>>31;y=f[c+16>>2]|0;z=((y|0)<0)<<31>>31;A=f[c+12>>2]|0;B=((A|0)<0)<<31>>31;C=f[c+8>>2]|0;D=((C|0)<0)<<31>>31;E=f[c+4>>2]|0;F=((E|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-32>>2]|0;l=f[a+-28>>2]|0;m=f[a+-24>>2]|0;n=f[a+-20>>2]|0;o=f[a+-16>>2]|0;p=f[a+-12>>2]|0;q=f[a+-8>>2]|0;r=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,s|0,t|0)|0;_=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,u|0,v|0)|0;_=Se(Z|0,H|0,h|0,_|0)|0;h=H;Z=Re(m|0,((m|0)<0)<<31>>31|0,w|0,x|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(n|0,((n|0)<0)<<31>>31|0,y|0,z|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(o|0,((o|0)<0)<<31>>31|0,A|0,B|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(p|0,((p|0)<0)<<31>>31|0,C|0,D|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(q|0,((q|0)<0)<<31>>31|0,E|0,F|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(r|0,((r|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{U=r;V=q;W=p;X=o;Y=n;Z=m;_=l;r=h;q=U;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}else{if(!h)return;r=f[c+24>>2]|0;s=((r|0)<0)<<31>>31;t=f[c+20>>2]|0;u=((t|0)<0)<<31>>31;v=f[c+16>>2]|0;w=((v|0)<0)<<31>>31;x=f[c+12>>2]|0;y=((x|0)<0)<<31>>31;z=f[c+8>>2]|0;A=((z|0)<0)<<31>>31;B=f[c+4>>2]|0;C=((B|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-28>>2]|0;l=f[a+-24>>2]|0;m=f[a+-20>>2]|0;n=f[a+-16>>2]|0;o=f[a+-12>>2]|0;p=f[a+-8>>2]|0;q=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,r|0,s|0)|0;Z=H;_=Re(l|0,((l|0)<0)<<31>>31|0,t|0,u|0)|0;Z=Se(_|0,H|0,h|0,Z|0)|0;h=H;_=Re(m|0,((m|0)<0)<<31>>31|0,v|0,w|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(n|0,((n|0)<0)<<31>>31|0,x|0,y|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(o|0,((o|0)<0)<<31>>31|0,z|0,A|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(p|0,((p|0)<0)<<31>>31|0,B|0,C|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(q|0,((q|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{V=q;W=p;X=o;Y=n;Z=m;_=l;q=h;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}}else{h=(b|0)>0;if((d|0)==6){if(!h)return;q=f[c+20>>2]|0;r=((q|0)<0)<<31>>31;s=f[c+16>>2]|0;t=((s|0)<0)<<31>>31;u=f[c+12>>2]|0;v=((u|0)<0)<<31>>31;w=f[c+8>>2]|0;x=((w|0)<0)<<31>>31;y=f[c+4>>2]|0;z=((y|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-24>>2]|0;l=f[a+-20>>2]|0;m=f[a+-16>>2]|0;n=f[a+-12>>2]|0;o=f[a+-8>>2]|0;p=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,q|0,r|0)|0;_=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,s|0,t|0)|0;_=Se(Z|0,H|0,h|0,_|0)|0;h=H;Z=Re(m|0,((m|0)<0)<<31>>31|0,u|0,v|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(n|0,((n|0)<0)<<31>>31|0,w|0,x|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(o|0,((o|0)<0)<<31>>31|0,y|0,z|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(p|0,((p|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{W=p;X=o;Y=n;Z=m;_=l;p=h;o=W;n=X;m=Y;l=Z;h=_}}return}else{if(!h)return;p=f[c+16>>2]|0;q=((p|0)<0)<<31>>31;r=f[c+12>>2]|0;s=((r|0)<0)<<31>>31;t=f[c+8>>2]|0;u=((t|0)<0)<<31>>31;v=f[c+4>>2]|0;w=((v|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-20>>2]|0;l=f[a+-16>>2]|0;m=f[a+-12>>2]|0;n=f[a+-8>>2]|0;o=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,p|0,q|0)|0;Z=H;_=Re(l|0,((l|0)<0)<<31>>31|0,r|0,s|0)|0;Z=Se(_|0,H|0,h|0,Z|0)|0;h=H;_=Re(m|0,((m|0)<0)<<31>>31|0,t|0,u|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=H;Z=Re(n|0,((n|0)<0)<<31>>31|0,v|0,w|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(o|0,((o|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{X=o;Y=n;Z=m;_=l;o=h;n=X;m=Y;l=Z;h=_}}return}}else if(d>>>0>2){h=(b|0)>0;if((d|0)==4){if(!h)return;o=f[c+12>>2]|0;p=((o|0)<0)<<31>>31;q=f[c+8>>2]|0;r=((q|0)<0)<<31>>31;s=f[c+4>>2]|0;t=((s|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-16>>2]|0;l=f[a+-12>>2]|0;m=f[a+-8>>2]|0;n=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,o|0,p|0)|0;_=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,q|0,r|0)|0;_=Se(Z|0,H|0,h|0,_|0)|0;h=H;Z=Re(m|0,((m|0)<0)<<31>>31|0,s|0,t|0)|0;Z=Se(_|0,h|0,Z|0,H|0)|0;h=H;_=Re(n|0,((n|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{Y=n;Z=m;_=l;n=h;m=Y;l=Z;h=_}}return}else{if(!h)return;n=f[c+8>>2]|0;o=((n|0)<0)<<31>>31;p=f[c+4>>2]|0;q=((p|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-12>>2]|0;l=f[a+-8>>2]|0;m=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,n|0,o|0)|0;Z=H;_=Re(l|0,((l|0)<0)<<31>>31|0,p|0,q|0)|0;Z=Se(_|0,H|0,h|0,Z|0)|0;h=H;_=Re(m|0,((m|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,h|0,_|0,H|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{Z=m;_=l;m=h;l=Z;h=_}}return}}else{h=(b|0)>0;if((d|0)==2){if(!h)return;m=f[c+4>>2]|0;n=((m|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[a+-8>>2]|0;l=f[a+-4>>2]|0;while(1){h=Re(h|0,((h|0)<0)<<31>>31|0,m|0,n|0)|0;_=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,j|0,k|0)|0;_=Se(Z|0,H|0,h|0,_|0)|0;h=f[a+(i<<2)>>2]|0;_=Ye(_|0,H|0,e|0)|0;f[g+(i<<2)>>2]=h-_;i=i+1|0;if((i|0)==(b|0))break;else{_=l;l=h;h=_}}return}else{if(!h)return;i=f[c>>2]|0;j=((i|0)<0)<<31>>31;h=0;k=f[a+-4>>2]|0;do{_=k;k=f[a+(h<<2)>>2]|0;_=Re(_|0,((_|0)<0)<<31>>31|0,i|0,j|0)|0;_=Ye(_|0,H|0,e|0)|0;f[g+(h<<2)>>2]=k-_;h=h+1|0}while((h|0)!=(b|0));return}}}function lc(a,b,c,d,e,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;if(d>>>0>=13){if((b|0)<=0)return;j=c+124|0;k=c+120|0;l=c+116|0;m=c+112|0;n=c+108|0;o=c+104|0;p=c+100|0;q=c+96|0;r=c+92|0;s=c+88|0;t=c+84|0;u=c+80|0;v=c+76|0;w=c+72|0;x=c+68|0;y=c+64|0;z=c+60|0;A=c+56|0;B=c+52|0;C=c+48|0;D=c+44|0;E=c+40|0;F=c+36|0;G=c+32|0;H=c+28|0;I=c+24|0;J=c+20|0;K=c+16|0;L=c+12|0;M=c+8|0;N=c+4|0;i=0;do{switch(d|0){case 32:{h=W(f[g+(i+-32<<2)>>2]|0,f[j>>2]|0)|0;O=53;break}case 31:{h=0;O=53;break}case 30:{h=0;O=54;break}case 29:{h=0;O=55;break}case 28:{h=0;O=56;break}case 27:{h=0;O=57;break}case 26:{h=0;O=58;break}case 25:{h=0;O=59;break}case 24:{h=0;O=60;break}case 23:{h=0;O=61;break}case 22:{h=0;O=62;break}case 21:{h=0;O=63;break}case 20:{h=0;O=64;break}case 19:{h=0;O=65;break}case 18:{h=0;O=66;break}case 17:{h=0;O=67;break}case 16:{h=0;O=68;break}case 15:{h=0;O=69;break}case 14:{h=0;O=70;break}case 13:{h=0;O=71;break}default:h=0}if((O|0)==53){h=(W(f[g+(i+-31<<2)>>2]|0,f[k>>2]|0)|0)+h|0;O=54}if((O|0)==54){h=(W(f[g+(i+-30<<2)>>2]|0,f[l>>2]|0)|0)+h|0;O=55}if((O|0)==55){h=(W(f[g+(i+-29<<2)>>2]|0,f[m>>2]|0)|0)+h|0;O=56}if((O|0)==56){h=(W(f[g+(i+-28<<2)>>2]|0,f[n>>2]|0)|0)+h|0;O=57}if((O|0)==57){h=(W(f[g+(i+-27<<2)>>2]|0,f[o>>2]|0)|0)+h|0;O=58}if((O|0)==58){h=(W(f[g+(i+-26<<2)>>2]|0,f[p>>2]|0)|0)+h|0;O=59}if((O|0)==59){h=(W(f[g+(i+-25<<2)>>2]|0,f[q>>2]|0)|0)+h|0;O=60}if((O|0)==60){h=(W(f[g+(i+-24<<2)>>2]|0,f[r>>2]|0)|0)+h|0;O=61}if((O|0)==61){h=(W(f[g+(i+-23<<2)>>2]|0,f[s>>2]|0)|0)+h|0;O=62}if((O|0)==62){h=(W(f[g+(i+-22<<2)>>2]|0,f[t>>2]|0)|0)+h|0;O=63}if((O|0)==63){h=(W(f[g+(i+-21<<2)>>2]|0,f[u>>2]|0)|0)+h|0;O=64}if((O|0)==64){h=(W(f[g+(i+-20<<2)>>2]|0,f[v>>2]|0)|0)+h|0;O=65}if((O|0)==65){h=(W(f[g+(i+-19<<2)>>2]|0,f[w>>2]|0)|0)+h|0;O=66}if((O|0)==66){h=(W(f[g+(i+-18<<2)>>2]|0,f[x>>2]|0)|0)+h|0;O=67}if((O|0)==67){h=(W(f[g+(i+-17<<2)>>2]|0,f[y>>2]|0)|0)+h|0;O=68}if((O|0)==68){h=(W(f[g+(i+-16<<2)>>2]|0,f[z>>2]|0)|0)+h|0;O=69}if((O|0)==69){h=(W(f[g+(i+-15<<2)>>2]|0,f[A>>2]|0)|0)+h|0;O=70}if((O|0)==70){h=(W(f[g+(i+-14<<2)>>2]|0,f[B>>2]|0)|0)+h|0;O=71}if((O|0)==71){O=0;h=(W(f[g+(i+-13<<2)>>2]|0,f[C>>2]|0)|0)+h|0;h=h+(W(f[g+(i+-12<<2)>>2]|0,f[D>>2]|0)|0)|0;h=h+(W(f[g+(i+-11<<2)>>2]|0,f[E>>2]|0)|0)|0;h=h+(W(f[g+(i+-10<<2)>>2]|0,f[F>>2]|0)|0)|0;h=h+(W(f[g+(i+-9<<2)>>2]|0,f[G>>2]|0)|0)|0;h=h+(W(f[g+(i+-8<<2)>>2]|0,f[H>>2]|0)|0)|0;h=h+(W(f[g+(i+-7<<2)>>2]|0,f[I>>2]|0)|0)|0;h=h+(W(f[g+(i+-6<<2)>>2]|0,f[J>>2]|0)|0)|0;h=h+(W(f[g+(i+-5<<2)>>2]|0,f[K>>2]|0)|0)|0;h=h+(W(f[g+(i+-4<<2)>>2]|0,f[L>>2]|0)|0)|0;h=h+(W(f[g+(i+-3<<2)>>2]|0,f[M>>2]|0)|0)|0;h=h+(W(f[g+(i+-2<<2)>>2]|0,f[N>>2]|0)|0)|0;h=h+(W(f[g+(i+-1<<2)>>2]|0,f[c>>2]|0)|0)|0}f[g+(i<<2)>>2]=(f[a+(i<<2)>>2]|0)+(h>>e);i=i+1|0}while((i|0)!=(b|0));return}if(d>>>0>8)if(d>>>0>10){h=(b|0)>0;if((d|0)==12){if(!h)return;v=f[c+44>>2]|0;w=f[c+40>>2]|0;x=f[c+36>>2]|0;y=f[c+32>>2]|0;z=f[c+28>>2]|0;A=f[c+24>>2]|0;B=f[c+20>>2]|0;C=f[c+16>>2]|0;D=f[c+12>>2]|0;E=f[c+8>>2]|0;F=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-48>>2]|0;k=f[g+-44>>2]|0;l=f[g+-40>>2]|0;m=f[g+-36>>2]|0;n=f[g+-32>>2]|0;o=f[g+-28>>2]|0;p=f[g+-24>>2]|0;q=f[g+-20>>2]|0;r=f[g+-16>>2]|0;s=f[g+-12>>2]|0;t=f[g+-8>>2]|0;u=f[g+-4>>2]|0;while(1){h=(W(k,w)|0)+(W(h,v)|0)+(W(l,x)|0)+(W(m,y)|0)+(W(n,z)|0)+(W(o,A)|0)+(W(p,B)|0)+(W(q,C)|0)+(W(r,D)|0)+(W(s,E)|0)+(W(t,F)|0)+(W(u,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{G=u;H=t;I=s;J=r;K=q;L=p;M=o;N=n;O=m;d=l;c=k;u=h;t=G;s=H;r=I;q=J;p=K;o=L;n=M;m=N;l=O;k=d;h=c}}return}else{if(!h)return;u=f[c+40>>2]|0;v=f[c+36>>2]|0;w=f[c+32>>2]|0;x=f[c+28>>2]|0;y=f[c+24>>2]|0;z=f[c+20>>2]|0;A=f[c+16>>2]|0;B=f[c+12>>2]|0;C=f[c+8>>2]|0;D=f[c+4>>2]|0;s=f[c>>2]|0;i=0;j=f[g+-36>>2]|0;k=f[g+-32>>2]|0;l=f[g+-28>>2]|0;m=f[g+-24>>2]|0;n=f[g+-20>>2]|0;o=f[g+-16>>2]|0;p=f[g+-12>>2]|0;q=f[g+-8>>2]|0;r=f[g+-4>>2]|0;h=f[g+-44>>2]|0;t=f[g+-40>>2]|0;while(1){h=(W(t,v)|0)+(W(h,u)|0)+(W(j,w)|0)+(W(k,x)|0)+(W(l,y)|0)+(W(m,z)|0)+(W(n,A)|0)+(W(o,B)|0)+(W(p,C)|0)+(W(q,D)|0)+(W(r,s)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{I=t;J=r;K=q;L=p;M=o;N=n;O=m;d=l;c=k;r=h;t=j;h=I;q=J;p=K;o=L;n=M;m=N;l=O;k=d;j=c}}return}}else{h=(b|0)>0;if((d|0)==10){if(!h)return;t=f[c+36>>2]|0;u=f[c+32>>2]|0;v=f[c+28>>2]|0;w=f[c+24>>2]|0;x=f[c+20>>2]|0;y=f[c+16>>2]|0;z=f[c+12>>2]|0;A=f[c+8>>2]|0;B=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-40>>2]|0;k=f[g+-36>>2]|0;l=f[g+-32>>2]|0;m=f[g+-28>>2]|0;n=f[g+-24>>2]|0;o=f[g+-20>>2]|0;p=f[g+-16>>2]|0;q=f[g+-12>>2]|0;r=f[g+-8>>2]|0;s=f[g+-4>>2]|0;while(1){h=(W(k,u)|0)+(W(h,t)|0)+(W(l,v)|0)+(W(m,w)|0)+(W(n,x)|0)+(W(o,y)|0)+(W(p,z)|0)+(W(q,A)|0)+(W(r,B)|0)+(W(s,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{I=s;J=r;K=q;L=p;M=o;N=n;O=m;d=l;c=k;s=h;r=I;q=J;p=K;o=L;n=M;m=N;l=O;k=d;h=c}}return}else{if(!h)return;s=f[c+32>>2]|0;t=f[c+28>>2]|0;u=f[c+24>>2]|0;v=f[c+20>>2]|0;w=f[c+16>>2]|0;x=f[c+12>>2]|0;y=f[c+8>>2]|0;z=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-36>>2]|0;k=f[g+-32>>2]|0;l=f[g+-28>>2]|0;m=f[g+-24>>2]|0;n=f[g+-20>>2]|0;o=f[g+-16>>2]|0;p=f[g+-12>>2]|0;q=f[g+-8>>2]|0;r=f[g+-4>>2]|0;while(1){h=(W(k,t)|0)+(W(h,s)|0)+(W(l,u)|0)+(W(m,v)|0)+(W(n,w)|0)+(W(o,x)|0)+(W(p,y)|0)+(W(q,z)|0)+(W(r,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{J=r;K=q;L=p;M=o;N=n;O=m;d=l;c=k;r=h;q=J;p=K;o=L;n=M;m=N;l=O;k=d;h=c}}return}}if(d>>>0>4)if(d>>>0>6){h=(b|0)>0;if((d|0)==8){if(!h)return;r=f[c+28>>2]|0;s=f[c+24>>2]|0;t=f[c+20>>2]|0;u=f[c+16>>2]|0;v=f[c+12>>2]|0;w=f[c+8>>2]|0;x=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-32>>2]|0;k=f[g+-28>>2]|0;l=f[g+-24>>2]|0;m=f[g+-20>>2]|0;n=f[g+-16>>2]|0;o=f[g+-12>>2]|0;p=f[g+-8>>2]|0;q=f[g+-4>>2]|0;while(1){h=(W(k,s)|0)+(W(h,r)|0)+(W(l,t)|0)+(W(m,u)|0)+(W(n,v)|0)+(W(o,w)|0)+(W(p,x)|0)+(W(q,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{K=q;L=p;M=o;N=n;O=m;d=l;c=k;q=h;p=K;o=L;n=M;m=N;l=O;k=d;h=c}}return}else{if(!h)return;q=f[c+24>>2]|0;r=f[c+20>>2]|0;s=f[c+16>>2]|0;t=f[c+12>>2]|0;u=f[c+8>>2]|0;v=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-28>>2]|0;k=f[g+-24>>2]|0;l=f[g+-20>>2]|0;m=f[g+-16>>2]|0;n=f[g+-12>>2]|0;o=f[g+-8>>2]|0;p=f[g+-4>>2]|0;while(1){h=(W(k,r)|0)+(W(h,q)|0)+(W(l,s)|0)+(W(m,t)|0)+(W(n,u)|0)+(W(o,v)|0)+(W(p,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{L=p;M=o;N=n;O=m;d=l;c=k;p=h;o=L;n=M;m=N;l=O;k=d;h=c}}return}}else{h=(b|0)>0;if((d|0)==6){if(!h)return;p=f[c+20>>2]|0;q=f[c+16>>2]|0;r=f[c+12>>2]|0;s=f[c+8>>2]|0;t=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-24>>2]|0;k=f[g+-20>>2]|0;l=f[g+-16>>2]|0;m=f[g+-12>>2]|0;n=f[g+-8>>2]|0;o=f[g+-4>>2]|0;while(1){h=(W(k,q)|0)+(W(h,p)|0)+(W(l,r)|0)+(W(m,s)|0)+(W(n,t)|0)+(W(o,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{M=o;N=n;O=m;d=l;c=k;o=h;n=M;m=N;l=O;k=d;h=c}}return}else{if(!h)return;o=f[c+16>>2]|0;p=f[c+12>>2]|0;q=f[c+8>>2]|0;r=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-20>>2]|0;k=f[g+-16>>2]|0;l=f[g+-12>>2]|0;m=f[g+-8>>2]|0;n=f[g+-4>>2]|0;while(1){h=(W(k,p)|0)+(W(h,o)|0)+(W(l,q)|0)+(W(m,r)|0)+(W(n,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{N=n;O=m;d=l;c=k;n=h;m=N;l=O;k=d;h=c}}return}}else if(d>>>0>2){h=(b|0)>0;if((d|0)==4){if(!h)return;n=f[c+12>>2]|0;o=f[c+8>>2]|0;p=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-16>>2]|0;k=f[g+-12>>2]|0;l=f[g+-8>>2]|0;m=f[g+-4>>2]|0;while(1){h=(W(k,o)|0)+(W(h,n)|0)+(W(l,p)|0)+(W(m,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{O=m;d=l;c=k;m=h;l=O;k=d;h=c}}return}else{if(!h)return;m=f[c+8>>2]|0;n=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-12>>2]|0;k=f[g+-8>>2]|0;l=f[g+-4>>2]|0;while(1){h=(W(k,n)|0)+(W(h,m)|0)+(W(l,j)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{d=l;c=k;l=h;k=d;h=c}}return}}else{h=(b|0)>0;if((d|0)==2){if(!h)return;l=f[c+4>>2]|0;j=f[c>>2]|0;i=0;h=f[g+-8>>2]|0;k=f[g+-4>>2]|0;while(1){h=(W(k,j)|0)+(W(h,l)|0)|0;h=(h>>e)+(f[a+(i<<2)>>2]|0)|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{c=k;k=h;h=c}}return}else{if(!h)return;i=f[c>>2]|0;h=0;j=f[g+-4>>2]|0;do{j=((W(j,i)|0)>>e)+(f[a+(h<<2)>>2]|0)|0;f[g+(h<<2)>>2]=j;h=h+1|0}while((h|0)!=(b|0));return}}}function mc(a,b,c,d,e,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0;if(d>>>0>=13){if((b|0)<=0)return;k=c+124|0;l=c+120|0;m=c+116|0;n=c+112|0;o=c+108|0;p=c+104|0;q=c+100|0;r=c+96|0;s=c+92|0;t=c+88|0;u=c+84|0;v=c+80|0;w=c+76|0;x=c+72|0;y=c+68|0;z=c+64|0;A=c+60|0;B=c+56|0;C=c+52|0;D=c+48|0;E=c+44|0;F=c+40|0;G=c+36|0;I=c+32|0;J=c+28|0;K=c+24|0;L=c+20|0;M=c+16|0;N=c+12|0;O=c+8|0;P=c+4|0;j=0;do{switch(d|0){case 32:{h=f[k>>2]|0;i=f[g+(j+-32<<2)>>2]|0;h=Re(i|0,((i|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;i=H;Q=53;break}case 31:{h=0;i=0;Q=53;break}case 30:{h=0;i=0;Q=54;break}case 29:{h=0;i=0;Q=55;break}case 28:{h=0;i=0;Q=56;break}case 27:{h=0;i=0;Q=57;break}case 26:{h=0;i=0;Q=58;break}case 25:{h=0;i=0;Q=59;break}case 24:{h=0;i=0;Q=60;break}case 23:{h=0;i=0;Q=61;break}case 22:{h=0;i=0;Q=62;break}case 21:{h=0;i=0;Q=63;break}case 20:{h=0;i=0;Q=64;break}case 19:{h=0;i=0;Q=65;break}case 18:{h=0;i=0;Q=66;break}case 17:{h=0;i=0;Q=67;break}case 16:{h=0;i=0;Q=68;break}case 15:{h=0;i=0;Q=69;break}case 14:{h=0;i=0;Q=70;break}case 13:{h=0;i=0;Q=71;break}default:{h=0;i=0}}if((Q|0)==53){R=f[l>>2]|0;S=f[g+(j+-31<<2)>>2]|0;R=Re(S|0,((S|0)<0)<<31>>31|0,R|0,((R|0)<0)<<31>>31|0)|0;h=Se(R|0,H|0,h|0,i|0)|0;i=H;Q=54}if((Q|0)==54){S=f[m>>2]|0;R=f[g+(j+-30<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=55}if((Q|0)==55){S=f[n>>2]|0;R=f[g+(j+-29<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=56}if((Q|0)==56){S=f[o>>2]|0;R=f[g+(j+-28<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=57}if((Q|0)==57){S=f[p>>2]|0;R=f[g+(j+-27<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=58}if((Q|0)==58){S=f[q>>2]|0;R=f[g+(j+-26<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=59}if((Q|0)==59){S=f[r>>2]|0;R=f[g+(j+-25<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=60}if((Q|0)==60){S=f[s>>2]|0;R=f[g+(j+-24<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=61}if((Q|0)==61){S=f[t>>2]|0;R=f[g+(j+-23<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=62}if((Q|0)==62){S=f[u>>2]|0;R=f[g+(j+-22<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=63}if((Q|0)==63){S=f[v>>2]|0;R=f[g+(j+-21<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=64}if((Q|0)==64){S=f[w>>2]|0;R=f[g+(j+-20<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=65}if((Q|0)==65){S=f[x>>2]|0;R=f[g+(j+-19<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=66}if((Q|0)==66){S=f[y>>2]|0;R=f[g+(j+-18<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=67}if((Q|0)==67){S=f[z>>2]|0;R=f[g+(j+-17<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=68}if((Q|0)==68){S=f[A>>2]|0;R=f[g+(j+-16<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=69}if((Q|0)==69){S=f[B>>2]|0;R=f[g+(j+-15<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=70}if((Q|0)==70){S=f[C>>2]|0;R=f[g+(j+-14<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;Q=71}if((Q|0)==71){Q=0;S=f[D>>2]|0;R=f[g+(j+-13<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;h=Se(S|0,H|0,h|0,i|0)|0;i=H;S=f[E>>2]|0;R=f[g+(j+-12<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[F>>2]|0;R=f[g+(j+-11<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[G>>2]|0;R=f[g+(j+-10<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[I>>2]|0;R=f[g+(j+-9<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[J>>2]|0;R=f[g+(j+-8<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[K>>2]|0;R=f[g+(j+-7<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[L>>2]|0;R=f[g+(j+-6<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[M>>2]|0;R=f[g+(j+-5<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[N>>2]|0;R=f[g+(j+-4<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[O>>2]|0;R=f[g+(j+-3<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H;S=f[P>>2]|0;R=f[g+(j+-2<<2)>>2]|0;S=Re(R|0,((R|0)<0)<<31>>31|0,S|0,((S|0)<0)<<31>>31|0)|0;S=Se(h|0,i|0,S|0,H|0)|0;i=H;h=f[c>>2]|0;R=f[g+(j+-1<<2)>>2]|0;h=Re(R|0,((R|0)<0)<<31>>31|0,h|0,((h|0)<0)<<31>>31|0)|0;h=Se(S|0,i|0,h|0,H|0)|0;i=H}R=f[a+(j<<2)>>2]|0;S=Ye(h|0,i|0,e|0)|0;f[g+(j<<2)>>2]=R+S;j=j+1|0}while((j|0)!=(b|0));return}if(d>>>0>8)if(d>>>0>10){h=(b|0)>0;if((d|0)==12){if(!h)return;w=f[c+44>>2]|0;x=((w|0)<0)<<31>>31;y=f[c+40>>2]|0;z=((y|0)<0)<<31>>31;A=f[c+36>>2]|0;B=((A|0)<0)<<31>>31;C=f[c+32>>2]|0;D=((C|0)<0)<<31>>31;E=f[c+28>>2]|0;F=((E|0)<0)<<31>>31;G=f[c+24>>2]|0;I=((G|0)<0)<<31>>31;J=f[c+20>>2]|0;K=((J|0)<0)<<31>>31;L=f[c+16>>2]|0;M=((L|0)<0)<<31>>31;N=f[c+12>>2]|0;O=((N|0)<0)<<31>>31;P=f[c+8>>2]|0;Q=((P|0)<0)<<31>>31;d=f[c+4>>2]|0;R=((d|0)<0)<<31>>31;o=f[c>>2]|0;p=((o|0)<0)<<31>>31;i=0;j=f[g+-20>>2]|0;k=f[g+-16>>2]|0;l=f[g+-12>>2]|0;m=f[g+-8>>2]|0;n=f[g+-4>>2]|0;h=f[g+-48>>2]|0;q=f[g+-44>>2]|0;r=f[g+-40>>2]|0;s=f[g+-36>>2]|0;t=f[g+-32>>2]|0;u=f[g+-28>>2]|0;v=f[g+-24>>2]|0;while(1){S=Re(h|0,((h|0)<0)<<31>>31|0,w|0,x|0)|0;h=H;c=Re(q|0,((q|0)<0)<<31>>31|0,y|0,z|0)|0;h=Se(c|0,H|0,S|0,h|0)|0;S=H;c=Re(r|0,((r|0)<0)<<31>>31|0,A|0,B|0)|0;c=Se(h|0,S|0,c|0,H|0)|0;S=H;h=Re(s|0,((s|0)<0)<<31>>31|0,C|0,D|0)|0;h=Se(c|0,S|0,h|0,H|0)|0;S=H;c=Re(t|0,((t|0)<0)<<31>>31|0,E|0,F|0)|0;c=Se(h|0,S|0,c|0,H|0)|0;S=H;h=Re(u|0,((u|0)<0)<<31>>31|0,G|0,I|0)|0;h=Se(c|0,S|0,h|0,H|0)|0;S=H;c=Re(v|0,((v|0)<0)<<31>>31|0,J|0,K|0)|0;c=Se(h|0,S|0,c|0,H|0)|0;S=H;h=Re(j|0,((j|0)<0)<<31>>31|0,L|0,M|0)|0;h=Se(c|0,S|0,h|0,H|0)|0;S=H;c=Re(k|0,((k|0)<0)<<31>>31|0,N|0,O|0)|0;c=Se(h|0,S|0,c|0,H|0)|0;S=H;h=Re(l|0,((l|0)<0)<<31>>31|0,P|0,Q|0)|0;h=Se(c|0,S|0,h|0,H|0)|0;S=H;c=Re(m|0,((m|0)<0)<<31>>31|0,d|0,R|0)|0;c=Se(h|0,S|0,c|0,H|0)|0;S=H;h=Re(n|0,((n|0)<0)<<31>>31|0,o|0,p|0)|0;h=Se(c|0,S|0,h|0,H|0)|0;S=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=S+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{_=v;Z=u;Y=t;X=s;W=r;V=q;U=n;T=m;c=l;S=k;n=h;v=j;u=_;t=Z;s=Y;r=X;q=W;h=V;m=U;l=T;k=c;j=S}}return}else{if(!h)return;v=f[c+40>>2]|0;w=((v|0)<0)<<31>>31;x=f[c+36>>2]|0;y=((x|0)<0)<<31>>31;z=f[c+32>>2]|0;A=((z|0)<0)<<31>>31;B=f[c+28>>2]|0;C=((B|0)<0)<<31>>31;D=f[c+24>>2]|0;E=((D|0)<0)<<31>>31;F=f[c+20>>2]|0;G=((F|0)<0)<<31>>31;I=f[c+16>>2]|0;J=((I|0)<0)<<31>>31;K=f[c+12>>2]|0;L=((K|0)<0)<<31>>31;M=f[c+8>>2]|0;N=((M|0)<0)<<31>>31;O=f[c+4>>2]|0;P=((O|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-44>>2]|0;l=f[g+-40>>2]|0;m=f[g+-36>>2]|0;n=f[g+-32>>2]|0;o=f[g+-28>>2]|0;p=f[g+-24>>2]|0;q=f[g+-20>>2]|0;r=f[g+-16>>2]|0;s=f[g+-12>>2]|0;t=f[g+-8>>2]|0;u=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,v|0,w|0)|0;Z=H;h=Re(l|0,((l|0)<0)<<31>>31|0,x|0,y|0)|0;Z=Se(h|0,H|0,_|0,Z|0)|0;_=H;h=Re(m|0,((m|0)<0)<<31>>31|0,z|0,A|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(n|0,((n|0)<0)<<31>>31|0,B|0,C|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(o|0,((o|0)<0)<<31>>31|0,D|0,E|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(p|0,((p|0)<0)<<31>>31|0,F|0,G|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(q|0,((q|0)<0)<<31>>31|0,I|0,J|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(r|0,((r|0)<0)<<31>>31|0,K|0,L|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(s|0,((s|0)<0)<<31>>31|0,M|0,N|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(t|0,((t|0)<0)<<31>>31|0,O|0,P|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(u|0,((u|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{c=u;S=t;T=s;U=r;V=q;W=p;X=o;Y=n;Z=m;_=l;u=h;t=c;s=S;r=T;q=U;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}}else{h=(b|0)>0;if((d|0)==10){if(!h)return;u=f[c+36>>2]|0;v=((u|0)<0)<<31>>31;w=f[c+32>>2]|0;x=((w|0)<0)<<31>>31;y=f[c+28>>2]|0;z=((y|0)<0)<<31>>31;A=f[c+24>>2]|0;B=((A|0)<0)<<31>>31;C=f[c+20>>2]|0;D=((C|0)<0)<<31>>31;E=f[c+16>>2]|0;F=((E|0)<0)<<31>>31;G=f[c+12>>2]|0;I=((G|0)<0)<<31>>31;J=f[c+8>>2]|0;K=((J|0)<0)<<31>>31;L=f[c+4>>2]|0;M=((L|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-40>>2]|0;l=f[g+-36>>2]|0;m=f[g+-32>>2]|0;n=f[g+-28>>2]|0;o=f[g+-24>>2]|0;p=f[g+-20>>2]|0;q=f[g+-16>>2]|0;r=f[g+-12>>2]|0;s=f[g+-8>>2]|0;t=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,u|0,v|0)|0;h=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,w|0,x|0)|0;h=Se(Z|0,H|0,_|0,h|0)|0;_=H;Z=Re(m|0,((m|0)<0)<<31>>31|0,y|0,z|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(n|0,((n|0)<0)<<31>>31|0,A|0,B|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(o|0,((o|0)<0)<<31>>31|0,C|0,D|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(p|0,((p|0)<0)<<31>>31|0,E|0,F|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(q|0,((q|0)<0)<<31>>31|0,G|0,I|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(r|0,((r|0)<0)<<31>>31|0,J|0,K|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(s|0,((s|0)<0)<<31>>31|0,L|0,M|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(t|0,((t|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{S=t;T=s;U=r;V=q;W=p;X=o;Y=n;Z=m;_=l;t=h;s=S;r=T;q=U;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}else{if(!h)return;t=f[c+32>>2]|0;u=((t|0)<0)<<31>>31;v=f[c+28>>2]|0;w=((v|0)<0)<<31>>31;x=f[c+24>>2]|0;y=((x|0)<0)<<31>>31;z=f[c+20>>2]|0;A=((z|0)<0)<<31>>31;B=f[c+16>>2]|0;C=((B|0)<0)<<31>>31;D=f[c+12>>2]|0;E=((D|0)<0)<<31>>31;F=f[c+8>>2]|0;G=((F|0)<0)<<31>>31;I=f[c+4>>2]|0;J=((I|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-36>>2]|0;l=f[g+-32>>2]|0;m=f[g+-28>>2]|0;n=f[g+-24>>2]|0;o=f[g+-20>>2]|0;p=f[g+-16>>2]|0;q=f[g+-12>>2]|0;r=f[g+-8>>2]|0;s=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,t|0,u|0)|0;Z=H;h=Re(l|0,((l|0)<0)<<31>>31|0,v|0,w|0)|0;Z=Se(h|0,H|0,_|0,Z|0)|0;_=H;h=Re(m|0,((m|0)<0)<<31>>31|0,x|0,y|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(n|0,((n|0)<0)<<31>>31|0,z|0,A|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(o|0,((o|0)<0)<<31>>31|0,B|0,C|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(p|0,((p|0)<0)<<31>>31|0,D|0,E|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(q|0,((q|0)<0)<<31>>31|0,F|0,G|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(r|0,((r|0)<0)<<31>>31|0,I|0,J|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(s|0,((s|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{T=s;U=r;V=q;W=p;X=o;Y=n;Z=m;_=l;s=h;r=T;q=U;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}}if(d>>>0>4)if(d>>>0>6){h=(b|0)>0;if((d|0)==8){if(!h)return;s=f[c+28>>2]|0;t=((s|0)<0)<<31>>31;u=f[c+24>>2]|0;v=((u|0)<0)<<31>>31;w=f[c+20>>2]|0;x=((w|0)<0)<<31>>31;y=f[c+16>>2]|0;z=((y|0)<0)<<31>>31;A=f[c+12>>2]|0;B=((A|0)<0)<<31>>31;C=f[c+8>>2]|0;D=((C|0)<0)<<31>>31;E=f[c+4>>2]|0;F=((E|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-32>>2]|0;l=f[g+-28>>2]|0;m=f[g+-24>>2]|0;n=f[g+-20>>2]|0;o=f[g+-16>>2]|0;p=f[g+-12>>2]|0;q=f[g+-8>>2]|0;r=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,s|0,t|0)|0;h=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,u|0,v|0)|0;h=Se(Z|0,H|0,_|0,h|0)|0;_=H;Z=Re(m|0,((m|0)<0)<<31>>31|0,w|0,x|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(n|0,((n|0)<0)<<31>>31|0,y|0,z|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(o|0,((o|0)<0)<<31>>31|0,A|0,B|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(p|0,((p|0)<0)<<31>>31|0,C|0,D|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(q|0,((q|0)<0)<<31>>31|0,E|0,F|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(r|0,((r|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{U=r;V=q;W=p;X=o;Y=n;Z=m;_=l;r=h;q=U;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}else{if(!h)return;r=f[c+24>>2]|0;s=((r|0)<0)<<31>>31;t=f[c+20>>2]|0;u=((t|0)<0)<<31>>31;v=f[c+16>>2]|0;w=((v|0)<0)<<31>>31;x=f[c+12>>2]|0;y=((x|0)<0)<<31>>31;z=f[c+8>>2]|0;A=((z|0)<0)<<31>>31;B=f[c+4>>2]|0;C=((B|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-28>>2]|0;l=f[g+-24>>2]|0;m=f[g+-20>>2]|0;n=f[g+-16>>2]|0;o=f[g+-12>>2]|0;p=f[g+-8>>2]|0;q=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,r|0,s|0)|0;Z=H;h=Re(l|0,((l|0)<0)<<31>>31|0,t|0,u|0)|0;Z=Se(h|0,H|0,_|0,Z|0)|0;_=H;h=Re(m|0,((m|0)<0)<<31>>31|0,v|0,w|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(n|0,((n|0)<0)<<31>>31|0,x|0,y|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(o|0,((o|0)<0)<<31>>31|0,z|0,A|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(p|0,((p|0)<0)<<31>>31|0,B|0,C|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(q|0,((q|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{V=q;W=p;X=o;Y=n;Z=m;_=l;q=h;p=V;o=W;n=X;m=Y;l=Z;h=_}}return}}else{h=(b|0)>0;if((d|0)==6){if(!h)return;q=f[c+20>>2]|0;r=((q|0)<0)<<31>>31;s=f[c+16>>2]|0;t=((s|0)<0)<<31>>31;u=f[c+12>>2]|0;v=((u|0)<0)<<31>>31;w=f[c+8>>2]|0;x=((w|0)<0)<<31>>31;y=f[c+4>>2]|0;z=((y|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-24>>2]|0;l=f[g+-20>>2]|0;m=f[g+-16>>2]|0;n=f[g+-12>>2]|0;o=f[g+-8>>2]|0;p=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,q|0,r|0)|0;h=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,s|0,t|0)|0;h=Se(Z|0,H|0,_|0,h|0)|0;_=H;Z=Re(m|0,((m|0)<0)<<31>>31|0,u|0,v|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(n|0,((n|0)<0)<<31>>31|0,w|0,x|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(o|0,((o|0)<0)<<31>>31|0,y|0,z|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(p|0,((p|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{W=p;X=o;Y=n;Z=m;_=l;p=h;o=W;n=X;m=Y;l=Z;h=_}}return}else{if(!h)return;p=f[c+16>>2]|0;q=((p|0)<0)<<31>>31;r=f[c+12>>2]|0;s=((r|0)<0)<<31>>31;t=f[c+8>>2]|0;u=((t|0)<0)<<31>>31;v=f[c+4>>2]|0;w=((v|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-20>>2]|0;l=f[g+-16>>2]|0;m=f[g+-12>>2]|0;n=f[g+-8>>2]|0;o=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,p|0,q|0)|0;Z=H;h=Re(l|0,((l|0)<0)<<31>>31|0,r|0,s|0)|0;Z=Se(h|0,H|0,_|0,Z|0)|0;_=H;h=Re(m|0,((m|0)<0)<<31>>31|0,t|0,u|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=H;Z=Re(n|0,((n|0)<0)<<31>>31|0,v|0,w|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(o|0,((o|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{X=o;Y=n;Z=m;_=l;o=h;n=X;m=Y;l=Z;h=_}}return}}else if(d>>>0>2){h=(b|0)>0;if((d|0)==4){if(!h)return;o=f[c+12>>2]|0;p=((o|0)<0)<<31>>31;q=f[c+8>>2]|0;r=((q|0)<0)<<31>>31;s=f[c+4>>2]|0;t=((s|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-16>>2]|0;l=f[g+-12>>2]|0;m=f[g+-8>>2]|0;n=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,o|0,p|0)|0;h=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,q|0,r|0)|0;h=Se(Z|0,H|0,_|0,h|0)|0;_=H;Z=Re(m|0,((m|0)<0)<<31>>31|0,s|0,t|0)|0;Z=Se(h|0,_|0,Z|0,H|0)|0;_=H;h=Re(n|0,((n|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{Y=n;Z=m;_=l;n=h;m=Y;l=Z;h=_}}return}else{if(!h)return;n=f[c+8>>2]|0;o=((n|0)<0)<<31>>31;p=f[c+4>>2]|0;q=((p|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-12>>2]|0;l=f[g+-8>>2]|0;m=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,n|0,o|0)|0;Z=H;h=Re(l|0,((l|0)<0)<<31>>31|0,p|0,q|0)|0;Z=Se(h|0,H|0,_|0,Z|0)|0;_=H;h=Re(m|0,((m|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,_|0,h|0,H|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{Z=m;_=l;m=h;l=Z;h=_}}return}}else{h=(b|0)>0;if((d|0)==2){if(!h)return;m=f[c+4>>2]|0;n=((m|0)<0)<<31>>31;j=f[c>>2]|0;k=((j|0)<0)<<31>>31;i=0;h=f[g+-8>>2]|0;l=f[g+-4>>2]|0;while(1){_=Re(h|0,((h|0)<0)<<31>>31|0,m|0,n|0)|0;h=H;Z=Re(l|0,((l|0)<0)<<31>>31|0,j|0,k|0)|0;h=Se(Z|0,H|0,_|0,h|0)|0;_=f[a+(i<<2)>>2]|0;h=Ye(h|0,H|0,e|0)|0;h=_+h|0;f[g+(i<<2)>>2]=h;i=i+1|0;if((i|0)==(b|0))break;else{_=l;l=h;h=_}}return}else{if(!h)return;i=f[c>>2]|0;j=((i|0)<0)<<31>>31;h=0;k=f[g+-4>>2]|0;do{Z=f[a+(h<<2)>>2]|0;_=Re(k|0,((k|0)<0)<<31>>31|0,i|0,j|0)|0;_=Ye(_|0,H|0,e|0)|0;k=Z+_|0;f[g+(h<<2)>>2]=k;h=h+1|0}while((h|0)!=(b|0));return}}}function nc(a,b){a=+a;b=b|0;if(a>0.0){a=+U(+(.5/+(b>>>0)*a))*.5/.6931471805599453;a=a>=0.0?a:0.0;return +a}else{a=a<0.0?1.e+32:0.0;return +a}return 0.0}function oc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0.0,f=0,g=0.0,h=0,i=0,j=0,k=0.0;k=.5/+(c>>>0);if(!b){d=1;return d|0}g=4294967295.0;h=0;i=0;j=1;while(1){e=+p[a+(h<<3)>>3];if(e>0.0){e=+U(+(k*e))*.5/.6931471805599453;e=e>=0.0?e:0.0}else e=e<0.0?1.e+32:0.0;e=e*+((c-j|0)>>>0)+ +((W(j,d)|0)>>>0);f=e<g;i=f?h:i;h=h+1|0;if((h|0)==(b|0))break;else{g=f?e:g;j=j+1|0}}d=i+1|0;return d|0}function pc(a){a=a|0;f[a+64>>2]=1732584193;f[a+68>>2]=-271733879;f[a+72>>2]=-1732584194;f[a+76>>2]=271733878;a=a+80|0;f[a>>2]=0;f[a+4>>2]=0;f[a+8>>2]=0;f[a+12>>2]=0;return}function qc(a,c){a=a|0;c=c|0;var d=0,e=0,g=0,h=0;h=c+80|0;e=f[h>>2]&63;d=c+e|0;g=d+1|0;b[d>>0]=-128;e=55-e|0;d=c+64|0;if((e|0)<0){cf(g|0,0,e+8|0)|0;rc(d,c);g=c;e=56}cf(g|0,0,e|0)|0;e=f[h>>2]|0;f[c+56>>2]=e<<3;f[c+60>>2]=f[c+84>>2]<<3|e>>>29;rc(d,c);e=a+16|0;do{b[a>>0]=b[d>>0]|0;a=a+1|0;d=d+1|0}while((a|0)<(e|0));d=c+88|0;e=f[d>>2]|0;if(!e){a=c;e=a+96|0;do{f[a>>2]=0;a=a+4|0}while((a|0)<(e|0));return}Md(e);f[d>>2]=0;f[c+92>>2]=0;a=c;e=a+96|0;do{f[a>>2]=0;a=a+4|0}while((a|0)<(e|0));return}function rc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0;l=f[a>>2]|0;i=a+4|0;k=f[i>>2]|0;e=a+8|0;g=f[e>>2]|0;c=a+12|0;d=f[c>>2]|0;C=f[b>>2]|0;D=l+-680876936+C+((d^g)&k^d)|0;D=(D<<7|D>>>25)+k|0;v=f[b+4>>2]|0;m=d+-389564586+v+(D&(g^k)^g)|0;m=(m<<12|m>>>20)+D|0;o=f[b+8>>2]|0;h=g+606105819+o+(m&(D^k)^k)|0;h=(h<<17|h>>>15)+m|0;x=f[b+12>>2]|0;j=k+-1044525330+x+(h&(m^D)^D)|0;j=(j<<22|j>>>10)+h|0;q=f[b+16>>2]|0;D=q+-176418897+D+(j&(h^m)^m)|0;D=(D<<7|D>>>25)+j|0;z=f[b+20>>2]|0;m=z+1200080426+m+(D&(j^h)^h)|0;m=(m<<12|m>>>20)+D|0;s=f[b+24>>2]|0;h=s+-1473231341+h+(m&(D^j)^j)|0;h=(h<<17|h>>>15)+m|0;B=f[b+28>>2]|0;j=B+-45705983+j+(h&(m^D)^D)|0;j=(j<<22|j>>>10)+h|0;u=f[b+32>>2]|0;D=u+1770035416+D+(j&(h^m)^m)|0;D=(D<<7|D>>>25)+j|0;n=f[b+36>>2]|0;m=n+-1958414417+m+(D&(j^h)^h)|0;m=(m<<12|m>>>20)+D|0;w=f[b+40>>2]|0;h=w+-42063+h+(m&(D^j)^j)|0;h=(h<<17|h>>>15)+m|0;p=f[b+44>>2]|0;j=p+-1990404162+j+(h&(m^D)^D)|0;j=(j<<22|j>>>10)+h|0;y=f[b+48>>2]|0;D=y+1804603682+D+(j&(h^m)^m)|0;D=(D<<7|D>>>25)+j|0;r=f[b+52>>2]|0;m=r+-40341101+m+(D&(j^h)^h)|0;m=(m<<12|m>>>20)+D|0;A=f[b+56>>2]|0;h=A+-1502002290+h+(m&(D^j)^j)|0;h=(h<<17|h>>>15)+m|0;t=f[b+60>>2]|0;j=t+1236535329+j+(h&(m^D)^D)|0;j=(j<<22|j>>>10)+h|0;D=v+-165796510+D+((j^h)&m^h)|0;D=(D<<5|D>>>27)+j|0;b=s+-1069501632+m+((D^j)&h^j)|0;b=(b<<9|b>>>23)+D|0;h=p+643717713+h+((b^D)&j^D)|0;h=(h<<14|h>>>18)+b|0;j=C+-373897302+j+((h^b)&D^b)|0;j=(j<<20|j>>>12)+h|0;D=z+-701558691+D+((j^h)&b^h)|0;D=(D<<5|D>>>27)+j|0;b=w+38016083+b+((D^j)&h^j)|0;b=(b<<9|b>>>23)+D|0;h=t+-660478335+h+((b^D)&j^D)|0;h=(h<<14|h>>>18)+b|0;j=q+-405537848+j+((h^b)&D^b)|0;j=(j<<20|j>>>12)+h|0;D=n+568446438+D+((j^h)&b^h)|0;D=(D<<5|D>>>27)+j|0;b=A+-1019803690+b+((D^j)&h^j)|0;b=(b<<9|b>>>23)+D|0;h=x+-187363961+h+((b^D)&j^D)|0;h=(h<<14|h>>>18)+b|0;j=u+1163531501+j+((h^b)&D^b)|0;j=(j<<20|j>>>12)+h|0;D=r+-1444681467+D+((j^h)&b^h)|0;D=(D<<5|D>>>27)+j|0;b=o+-51403784+b+((D^j)&h^j)|0;b=(b<<9|b>>>23)+D|0;h=B+1735328473+h+((b^D)&j^D)|0;h=(h<<14|h>>>18)+b|0;m=h^b;j=y+-1926607734+j+(m&D^b)|0;j=(j<<20|j>>>12)+h|0;m=z+-378558+D+(m^j)|0;m=(m<<4|m>>>28)+j|0;b=u+-2022574463+b+(j^h^m)|0;b=(b<<11|b>>>21)+m|0;h=p+1839030562+h+(m^j^b)|0;h=(h<<16|h>>>16)+b|0;j=A+-35309556+j+(b^m^h)|0;j=(j<<23|j>>>9)+h|0;m=v+-1530992060+m+(h^b^j)|0;m=(m<<4|m>>>28)+j|0;b=q+1272893353+b+(j^h^m)|0;b=(b<<11|b>>>21)+m|0;h=B+-155497632+h+(m^j^b)|0;h=(h<<16|h>>>16)+b|0;j=w+-1094730640+j+(b^m^h)|0;j=(j<<23|j>>>9)+h|0;m=r+681279174+m+(h^b^j)|0;m=(m<<4|m>>>28)+j|0;b=C+-358537222+b+(j^h^m)|0;b=(b<<11|b>>>21)+m|0;h=x+-722521979+h+(m^j^b)|0;h=(h<<16|h>>>16)+b|0;j=s+76029189+j+(b^m^h)|0;j=(j<<23|j>>>9)+h|0;m=n+-640364487+m+(h^b^j)|0;m=(m<<4|m>>>28)+j|0;b=y+-421815835+b+(j^h^m)|0;b=(b<<11|b>>>21)+m|0;h=t+530742520+h+(m^j^b)|0;h=(h<<16|h>>>16)+b|0;j=o+-995338651+j+(b^m^h)|0;j=(j<<23|j>>>9)+h|0;m=C+-198630844+m+((j|~b)^h)|0;m=(m<<6|m>>>26)+j|0;b=B+1126891415+b+((m|~h)^j)|0;b=(b<<10|b>>>22)+m|0;h=A+-1416354905+h+((b|~j)^m)|0;h=(h<<15|h>>>17)+b|0;j=z+-57434055+j+((h|~m)^b)|0;j=(j<<21|j>>>11)+h|0;m=y+1700485571+m+((j|~b)^h)|0;m=(m<<6|m>>>26)+j|0;b=x+-1894986606+b+((m|~h)^j)|0;b=(b<<10|b>>>22)+m|0;h=w+-1051523+h+((b|~j)^m)|0;h=(h<<15|h>>>17)+b|0;j=v+-2054922799+j+((h|~m)^b)|0;j=(j<<21|j>>>11)+h|0;m=u+1873313359+m+((j|~b)^h)|0;m=(m<<6|m>>>26)+j|0;b=t+-30611744+b+((m|~h)^j)|0;b=(b<<10|b>>>22)+m|0;h=s+-1560198380+h+((b|~j)^m)|0;h=(h<<15|h>>>17)+b|0;j=r+1309151649+j+((h|~m)^b)|0;j=(j<<21|j>>>11)+h|0;m=q+-145523070+m+((j|~b)^h)|0;m=(m<<6|m>>>26)+j|0;b=p+-1120210379+b+((m|~h)^j)|0;b=(b<<10|b>>>22)+m|0;h=o+718787259+h+((b|~j)^m)|0;h=(h<<15|h>>>17)+b|0;j=n+-343485551+j+((h|~m)^b)|0;f[a>>2]=m+l;f[i>>2]=h+k+(j<<21|j>>>11);f[e>>2]=h+g;f[c>>2]=b+d;return}function sc(a,c,e,g,h){a=a|0;c=c|0;e=e|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;i=W(h,e)|0;r=W(i,g)|0;if((4294967295/(h>>>0)|0)>>>0<e>>>0){a=0;return a|0}if(i>>>0>(4294967295/(g>>>0)|0)>>>0){a=0;return a|0}l=a+92|0;j=a+88|0;k=f[j>>2]|0;if((f[l>>2]|0)>>>0<r>>>0){i=Od(k,r)|0;if(!i){Md(k);i=Ld(r)|0;f[j>>2]=i;if(!i){f[l>>2]=0;a=0;return a|0}}else f[j>>2]=i;f[l>>2]=r;q=a+88|0}else{q=j;i=k}a:do switch((h*100|0)+e|0){case 101:{if(g|0){j=0;while(1){b[i>>0]=f[(f[c>>2]|0)+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+1|0}}break}case 102:{if(g|0){k=c+4|0;j=0;while(1){b[i>>0]=f[(f[c>>2]|0)+(j<<2)>>2];b[i+1>>0]=f[(f[k>>2]|0)+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+2|0}}break}case 104:{if(g|0){k=c+4|0;l=c+8|0;h=c+12|0;j=0;while(1){b[i>>0]=f[(f[c>>2]|0)+(j<<2)>>2];b[i+1>>0]=f[(f[k>>2]|0)+(j<<2)>>2];b[i+2>>0]=f[(f[l>>2]|0)+(j<<2)>>2];b[i+3>>0]=f[(f[h>>2]|0)+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+4|0}}break}case 106:{if(g|0){k=c+4|0;l=c+8|0;h=c+12|0;e=c+16|0;m=c+20|0;j=0;while(1){b[i>>0]=f[(f[c>>2]|0)+(j<<2)>>2];b[i+1>>0]=f[(f[k>>2]|0)+(j<<2)>>2];b[i+2>>0]=f[(f[l>>2]|0)+(j<<2)>>2];b[i+3>>0]=f[(f[h>>2]|0)+(j<<2)>>2];b[i+4>>0]=f[(f[e>>2]|0)+(j<<2)>>2];b[i+5>>0]=f[(f[m>>2]|0)+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+6|0}}break}case 108:{if(g|0){k=c+4|0;l=c+8|0;h=c+12|0;e=c+16|0;m=c+20|0;n=c+24|0;o=c+28|0;j=0;while(1){b[i>>0]=f[(f[c>>2]|0)+(j<<2)>>2];b[i+1>>0]=f[(f[k>>2]|0)+(j<<2)>>2];b[i+2>>0]=f[(f[l>>2]|0)+(j<<2)>>2];b[i+3>>0]=f[(f[h>>2]|0)+(j<<2)>>2];b[i+4>>0]=f[(f[e>>2]|0)+(j<<2)>>2];b[i+5>>0]=f[(f[m>>2]|0)+(j<<2)>>2];b[i+6>>0]=f[(f[n>>2]|0)+(j<<2)>>2];b[i+7>>0]=f[(f[o>>2]|0)+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+8|0}}break}case 201:{if(g|0){k=f[c>>2]|0;j=0;while(1){d[i>>1]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+2|0}}break}case 202:{if(g|0){l=f[c>>2]|0;k=f[c+4>>2]|0;j=0;while(1){d[i>>1]=f[l+(j<<2)>>2];d[i+2>>1]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+4|0}}break}case 204:{if(g|0){l=f[c>>2]|0;h=f[c+4>>2]|0;e=f[c+8>>2]|0;k=f[c+12>>2]|0;j=0;while(1){d[i>>1]=f[l+(j<<2)>>2];d[i+2>>1]=f[h+(j<<2)>>2];d[i+4>>1]=f[e+(j<<2)>>2];d[i+6>>1]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+8|0}}break}case 206:{if(g|0){l=f[c>>2]|0;h=f[c+4>>2]|0;e=f[c+8>>2]|0;m=f[c+12>>2]|0;n=f[c+16>>2]|0;k=f[c+20>>2]|0;j=0;while(1){d[i>>1]=f[l+(j<<2)>>2];d[i+2>>1]=f[h+(j<<2)>>2];d[i+4>>1]=f[e+(j<<2)>>2];d[i+6>>1]=f[m+(j<<2)>>2];d[i+8>>1]=f[n+(j<<2)>>2];d[i+10>>1]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+12|0}}break}case 208:{if(g|0){l=f[c>>2]|0;h=f[c+4>>2]|0;e=f[c+8>>2]|0;m=f[c+12>>2]|0;n=f[c+16>>2]|0;o=f[c+20>>2]|0;p=f[c+24>>2]|0;k=f[c+28>>2]|0;j=0;while(1){d[i>>1]=f[l+(j<<2)>>2];d[i+2>>1]=f[h+(j<<2)>>2];d[i+4>>1]=f[e+(j<<2)>>2];d[i+6>>1]=f[m+(j<<2)>>2];d[i+8>>1]=f[n+(j<<2)>>2];d[i+10>>1]=f[o+(j<<2)>>2];d[i+12>>1]=f[p+(j<<2)>>2];d[i+14>>1]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+16|0}}break}case 301:{if(g|0){j=0;while(1){p=f[(f[c>>2]|0)+(j<<2)>>2]|0;b[i>>0]=p;b[i+1>>0]=p>>>8;b[i+2>>0]=p>>>16;j=j+1|0;if((j|0)==(g|0))break;else i=i+3|0}}break}case 302:{if(g|0){k=c+4|0;j=0;while(1){p=f[(f[c>>2]|0)+(j<<2)>>2]|0;b[i>>0]=p;b[i+1>>0]=p>>>8;b[i+2>>0]=p>>>16;p=f[(f[k>>2]|0)+(j<<2)>>2]|0;b[i+3>>0]=p;b[i+4>>0]=p>>>8;b[i+5>>0]=p>>>16;j=j+1|0;if((j|0)==(g|0))break;else i=i+6|0}}break}case 401:{if(g|0){k=f[c>>2]|0;j=0;while(1){f[i>>2]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+4|0}}break}case 402:{if(g|0){l=f[c>>2]|0;k=f[c+4>>2]|0;j=0;while(1){f[i>>2]=f[l+(j<<2)>>2];f[i+4>>2]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+8|0}}break}case 404:{if(g|0){l=f[c>>2]|0;h=f[c+4>>2]|0;e=f[c+8>>2]|0;k=f[c+12>>2]|0;j=0;while(1){f[i>>2]=f[l+(j<<2)>>2];f[i+4>>2]=f[h+(j<<2)>>2];f[i+8>>2]=f[e+(j<<2)>>2];f[i+12>>2]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+16|0}}break}case 406:{if(g|0){l=f[c>>2]|0;h=f[c+4>>2]|0;e=f[c+8>>2]|0;m=f[c+12>>2]|0;n=f[c+16>>2]|0;k=f[c+20>>2]|0;j=0;while(1){f[i>>2]=f[l+(j<<2)>>2];f[i+4>>2]=f[h+(j<<2)>>2];f[i+8>>2]=f[e+(j<<2)>>2];f[i+12>>2]=f[m+(j<<2)>>2];f[i+16>>2]=f[n+(j<<2)>>2];f[i+20>>2]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+24|0}}break}case 408:{if(g|0){l=f[c>>2]|0;h=f[c+4>>2]|0;e=f[c+8>>2]|0;m=f[c+12>>2]|0;n=f[c+16>>2]|0;o=f[c+20>>2]|0;p=f[c+24>>2]|0;k=f[c+28>>2]|0;j=0;while(1){f[i>>2]=f[l+(j<<2)>>2];f[i+4>>2]=f[h+(j<<2)>>2];f[i+8>>2]=f[e+(j<<2)>>2];f[i+12>>2]=f[m+(j<<2)>>2];f[i+16>>2]=f[n+(j<<2)>>2];f[i+20>>2]=f[o+(j<<2)>>2];f[i+24>>2]=f[p+(j<<2)>>2];f[i+28>>2]=f[k+(j<<2)>>2];j=j+1|0;if((j|0)==(g|0))break;else i=i+32|0}}break}default:switch(h|0){case 1:{if((e|0)==0|(g|0)==0)break a;l=0;while(1){j=0;k=i;while(1){b[k>>0]=f[(f[c+(j<<2)>>2]|0)+(l<<2)>>2];j=j+1|0;if((j|0)==(e|0))break;else k=k+1|0}l=l+1|0;if((l|0)==(g|0))break;else i=i+e|0}break}case 2:{if((e|0)==0|(g|0)==0)break a;l=0;while(1){j=0;k=i;while(1){d[k>>1]=f[(f[c+(j<<2)>>2]|0)+(l<<2)>>2];j=j+1|0;if((j|0)==(e|0))break;else k=k+2|0}l=l+1|0;if((l|0)==(g|0))break;else i=i+(e<<1)|0}break}case 3:{if((e|0)==0|(g|0)==0)break a;h=e*3|0;l=0;while(1){j=i;k=0;while(1){p=f[(f[c+(k<<2)>>2]|0)+(l<<2)>>2]|0;b[j>>0]=p;b[j+1>>0]=p>>>8;b[j+2>>0]=p>>>16;k=k+1|0;if((k|0)==(e|0))break;else j=j+3|0}l=l+1|0;if((l|0)==(g|0))break;else i=i+h|0}break}case 4:{if((e|0)==0|(g|0)==0)break a;l=0;while(1){j=0;k=i;while(1){f[k>>2]=f[(f[c+(j<<2)>>2]|0)+(l<<2)>>2];j=j+1|0;if((j|0)==(e|0))break;else k=k+4|0}l=l+1|0;if((l|0)==(g|0))break;else i=i+(e<<2)|0}break}default:break a}}while(0);o=f[q>>2]|0;q=a+80|0;i=f[q>>2]|0;g=i+r|0;f[q>>2]=g;if(g>>>0<i>>>0){g=a+84|0;f[g>>2]=(f[g>>2]|0)+1}k=i&63;j=64-k|0;i=a+64+(0-j)|0;if(r>>>0<j>>>0){af(i|0,o|0,r|0)|0;a=1;return a|0}af(i|0,o|0,j|0)|0;n=a+64|0;rc(n,a);i=o+j|0;j=r-j|0;if(j>>>0>63){e=r+-128+k|0;m=128-k|0;while(1){k=a;l=i;h=k+64|0;do{b[k>>0]=b[l>>0]|0;k=k+1|0;l=l+1|0}while((k|0)<(h|0));rc(n,a);j=j+-64|0;if(j>>>0<=63)break;else i=i+64|0}i=e&-64;j=e-i|0;i=o+(m+i)|0}af(a|0,i|0,j|0)|0;a=1;return a|0}function tc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;if(a>>>0>1073741823){c=0;return c|0}d=a<<2;d=Ld(d|(d|0)==0)|0;if(!d){c=0;return c|0}a=f[b>>2]|0;if(a|0)Md(a);f[b>>2]=d;f[c>>2]=d;c=1;return c|0}function uc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;if(a>>>0>536870911){c=0;return c|0}d=a<<3;d=Ld(d|(d|0)==0)|0;if(!d){c=0;return c|0}a=f[b>>2]|0;if(a|0)Md(a);f[b>>2]=d;f[c>>2]=d;c=1;return c|0}function vc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;if(a>>>0>1073741823){c=0;return c|0}d=a<<2;d=Ld(d|(d|0)==0)|0;if(!d){c=0;return c|0}a=f[b>>2]|0;if(a|0)Md(a);f[b>>2]=d;f[c>>2]=d;c=1;return c|0}function wc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;if(a>>>0>1073741823){c=0;return c|0}d=a<<2;d=Ld(d|(d|0)==0)|0;if(!d){c=0;return c|0}a=f[b>>2]|0;if(a|0)Md(a);f[b>>2]=d;f[c>>2]=d;c=1;return c|0}function xc(a,b){a=a|0;b=b|0;do if((a|0)!=0&(b|0)!=0)if((4294967295/(b>>>0)|0)>>>0<a>>>0){b=0;return b|0}else{a=W(b,a)|0;break}else a=1;while(0);b=Ld(a)|0;return b|0}function yc(){var a=0,b=0,c=0,d=0,e=0,g=0;d=Nd(1,8)|0;if(!d){g=0;return g|0}e=Nd(1,32)|0;f[d>>2]=e;if(!e){Md(d);g=0;return g|0}g=Nd(1,6184)|0;f[d+4>>2]=g;if(!g){Md(e);Md(d);g=0;return g|0}a=Xa()|0;f[g+56>>2]=a;if(!a){Md(g);Md(e);Md(d);g=0;return g|0}f[g+1128>>2]=16;c=Ld((f[265]|0)>>>3<<4)|0;f[g+1120>>2]=c;if(!c){Ya(a);Md(g);Md(e);Md(d);g=0;return g|0}else{a=g+3616|0;f[a>>2]=0;f[a+4>>2]=0;f[a+8>>2]=0;f[a+12>>2]=0;f[a+16>>2]=0;f[a+20>>2]=0;f[a+24>>2]=0;f[a+28>>2]=0;f[g+220>>2]=0;f[g+224>>2]=0;f[g+252>>2]=0;a=g+124|0;b=g+60|0;c=b+64|0;do{f[b>>2]=0;b=b+4|0}while((b|0)<(c|0));cc(a);cc(g+136|0);cc(g+148|0);cc(g+160|0);cc(g+172|0);cc(g+184|0);cc(g+196|0);cc(g+208|0);f[g+52>>2]=0;f[g+48>>2]=0;a=g+608|0;cf(a|0,0,512)|0;b=g;c=b+36|0;do{f[b>>2]=0;b=b+4|0}while((b|0)<(c|0));f[a>>2]=1;f[g+1124>>2]=0;f[e+28>>2]=0;f[e>>2]=9;g=d;return g|0}return 0}function zc(a){a=a|0;var b=0,c=0,d=0;if(!a)return;Ac(a)|0;d=a+4|0;b=f[d>>2]|0;c=f[b+1120>>2]|0;if(c){Md(c);b=f[d>>2]|0}Ya(f[b+56>>2]|0);dc((f[d>>2]|0)+124|0);dc((f[d>>2]|0)+136|0);dc((f[d>>2]|0)+148|0);dc((f[d>>2]|0)+160|0);dc((f[d>>2]|0)+172|0);dc((f[d>>2]|0)+184|0);dc((f[d>>2]|0)+196|0);dc((f[d>>2]|0)+208|0);Md(f[d>>2]|0);Md(f[a>>2]|0);Md(a);return}function Ac(a){a=a|0;var b=0,c=0,d=0,e=0;if((f[f[a>>2]>>2]|0)==9){e=1;return e|0}e=a+4|0;b=f[e>>2]|0;qc(b+3756|0,b+3660|0);Md(f[(f[e>>2]|0)+452>>2]|0);f[(f[e>>2]|0)+452>>2]=0;b=f[e>>2]|0;f[b+252>>2]=0;Za(f[b+56>>2]|0);b=f[e>>2]|0;c=f[b+60>>2]|0;if(c){Md(c+-16|0);f[(f[e>>2]|0)+60>>2]=0;b=f[e>>2]|0}c=f[b+3616>>2]|0;if(c){Md(c);f[(f[e>>2]|0)+92>>2]=0;f[(f[e>>2]|0)+3616>>2]=0;b=f[e>>2]|0}c=f[b+64>>2]|0;if(c){Md(c+-16|0);f[(f[e>>2]|0)+64>>2]=0;b=f[e>>2]|0}c=f[b+3620>>2]|0;if(c){Md(c);f[(f[e>>2]|0)+96>>2]=0;f[(f[e>>2]|0)+3620>>2]=0;b=f[e>>2]|0}c=f[b+68>>2]|0;if(c){Md(c+-16|0);f[(f[e>>2]|0)+68>>2]=0;b=f[e>>2]|0}c=f[b+3624>>2]|0;if(c){Md(c);f[(f[e>>2]|0)+100>>2]=0;f[(f[e>>2]|0)+3624>>2]=0;b=f[e>>2]|0}c=f[b+72>>2]|0;if(c){Md(c+-16|0);f[(f[e>>2]|0)+72>>2]=0;b=f[e>>2]|0}c=f[b+3628>>2]|0;if(c){Md(c);f[(f[e>>2]|0)+104>>2]=0;f[(f[e>>2]|0)+3628>>2]=0;b=f[e>>2]|0}c=f[b+76>>2]|0;if(c){Md(c+-16|0);f[(f[e>>2]|0)+76>>2]=0;b=f[e>>2]|0}c=f[b+3632>>2]|0;if(c){Md(c);f[(f[e>>2]|0)+108>>2]=0;f[(f[e>>2]|0)+3632>>2]=0;b=f[e>>2]|0}c=f[b+80>>2]|0;if(c){Md(c+-16|0);f[(f[e>>2]|0)+80>>2]=0;b=f[e>>2]|0}c=f[b+3636>>2]|0;if(c){Md(c);f[(f[e>>2]|0)+112>>2]=0;f[(f[e>>2]|0)+3636>>2]=0;b=f[e>>2]|0}c=f[b+84>>2]|0;if(c){Md(c+-16|0);f[(f[e>>2]|0)+84>>2]=0;b=f[e>>2]|0}c=f[b+3640>>2]|0;if(c){Md(c);f[(f[e>>2]|0)+116>>2]=0;f[(f[e>>2]|0)+3640>>2]=0;b=f[e>>2]|0}c=f[b+88>>2]|0;if(c){Md(c+-16|0);f[(f[e>>2]|0)+88>>2]=0;b=f[e>>2]|0}c=f[b+3644>>2]|0;if(c){Md(c);f[(f[e>>2]|0)+120>>2]=0;f[(f[e>>2]|0)+3644>>2]=0;b=f[e>>2]|0}f[b+220>>2]=0;f[b+224>>2]=0;c=b+52|0;d=f[c>>2]|0;if(d){if((d|0)!=(f[380]|0)){Ke(d)|0;b=f[e>>2]|0;c=b+52|0}f[c>>2]=0}if(!(f[b+3648>>2]|0))d=1;else d=(ee(b+312|0,b+3756|0,16)|0)==0&1;f[b+3656>>2]=0;f[b+48>>2]=0;cf(b+608|0,0,512)|0;c=b+36|0;do{f[b>>2]=0;b=b+4|0}while((b|0)<(c|0));e=f[e>>2]|0;f[e+608>>2]=1;f[e+1124>>2]=0;e=f[a>>2]|0;f[e+28>>2]=0;f[e>>2]=9;e=d;return e|0}function Bc(a,b,c,d,e,f,g,h,i,j){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;return Cc(a,b,c,d,e,f,g,h,i,j,0)|0}function Cc(a,b,c,d,e,g,h,i,j,k,l){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;var m=0;if((f[f[a>>2]>>2]|0)!=9){k=5;return k|0}if(l|0){k=1;return k|0}if((b|0)==0|(h|0)==0|(j|0)==0){k=2;return k|0}if(c|0?(d|0)==0|(e|0)==0|(g|0)==0:0){k=2;return k|0}l=a+4|0;Mb((f[l>>2]|0)+3524|0);m=f[l>>2]|0;f[m+36>>2]=6;f[m+40>>2]=7;f[m+44>>2]=6;if(!(_a(f[m+56>>2]|0,9,a)|0)){f[f[a>>2]>>2]=8;m=3;return m|0}else{m=f[l>>2]|0;f[m+4>>2]=b;f[m+8>>2]=c;f[m+12>>2]=d;f[m+16>>2]=e;f[m+20>>2]=g;f[m+24>>2]=h;f[m+28>>2]=i;f[m+32>>2]=j;f[m+48>>2]=k;f[m+232>>2]=0;f[m+228>>2]=0;k=m+240|0;f[k>>2]=0;f[k+4>>2]=0;f[m+248>>2]=0;f[m+3520>>2]=0;f[m+3648>>2]=f[(f[a>>2]|0)+28>>2];f[m+3656>>2]=0;f[m+3652>>2]=1;m=(Ec(a)|0)==0;m=m?3:0;return m|0}return 0}function Dc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0;g=c+4|0;d=f[g>>2]|0;e=f[d+20>>2]|0;if(e|0?Ia[e&7](c,f[d+48>>2]|0)|0:0){f[b>>2]=0;f[f[c>>2]>>2]=4;c=0;return c|0}if(!(f[b>>2]|0)){f[f[c>>2]>>2]=7;c=0;return c|0}d=f[g>>2]|0;if(f[d+3656>>2]|0?(f[d+6176>>2]|0)>>>0>20:0){f[f[c>>2]>>2]=7;c=0;return c|0}d=Ka[f[d+4>>2]&7](c,a,b,f[d+48>>2]|0)|0;if((d|0)==2){f[f[c>>2]>>2]=7;c=0;return c|0}if(f[b>>2]|0){c=1;return c|0}if((d|0)!=1){d=f[g>>2]|0;e=f[d+20>>2]|0;if(!e){c=1;return c|0}if(!(Ia[e&7](c,f[d+48>>2]|0)|0)){c=1;return c|0}}f[f[c>>2]>>2]=4;c=0;return c|0}function Ec(a){a=a|0;var b=0,c=0,d=0,e=0;e=a+4|0;b=f[e>>2]|0;if((f[b+3652>>2]|0)==0?(f[f[a>>2]>>2]|0)==9:0){e=0;return e|0}d=b+240|0;f[d>>2]=0;f[d+4>>2]=0;f[b+3648>>2]=0;d=($a(f[b+56>>2]|0)|0)==0;c=f[a>>2]|0;if(d){f[c>>2]=8;e=0;return e|0}f[c>>2]=2;b=f[e>>2]|0;d=b+3652|0;do if(!(f[d>>2]|0)){if((f[b+52>>2]|0)==(f[380]|0)){e=0;return e|0}d=f[b+8>>2]|0;if(d)if((Ka[d&7](a,0,0,f[b+48>>2]|0)|0)==1){e=0;return e|0}else{c=f[a>>2]|0;b=f[e>>2]|0;break}}else f[d>>2]=0;while(0);f[c>>2]=0;f[b+248>>2]=0;Md(f[b+452>>2]|0);f[(f[e>>2]|0)+452>>2]=0;d=f[e>>2]|0;f[d+252>>2]=0;f[d+3648>>2]=f[(f[a>>2]|0)+28>>2];f[d+232>>2]=0;f[d+228>>2]=0;pc(d+3660|0);e=f[e>>2]|0;a=e+6160|0;f[a>>2]=0;f[a+4>>2]=0;f[e+6176>>2]=0;e=1;return e|0}function Fc(a,b){a=a|0;b=b|0;a=f[a>>2]|0;if((f[a>>2]|0)!=9){b=0;return b|0}f[a+28>>2]=b;b=1;return b|0}function Gc(a){a=a|0;return f[f[a>>2]>>2]|0}function Hc(a){a=a|0;return f[(f[a>>2]|0)+28>>2]|0}function Ic(a){a=a|0;var b=0,c=0,d=0;d=u;u=u+16|0;b=d;a:while(1)switch(f[f[a>>2]>>2]|0){case 1:{c=5;break a}case 7:case 4:{b=1;c=9;break a}case 0:{if(!(Jc(a)|0)){b=0;c=9;break a}break}case 2:{if(!(Lc(a)|0)){b=1;c=9;break a}break}case 3:{if(!(Mc(a,b,1)|0)){b=0;c=9;break a}if(f[b>>2]|0){b=1;c=9;break a}break}default:{b=0;c=10;break a}}if((c|0)==5){c=(Kc(a)|0)!=0&1;u=d;return c|0}else if((c|0)==9){c=b;u=d;return c|0}else if((c|0)==10){u=d;return b|0}return 0}function Jc(a){a=a|0;var c=0,d=0,e=0,g=0,i=0,j=0,k=0,l=0,m=0,n=0;n=u;u=u+16|0;j=n+4|0;k=n;l=a+4|0;d=1;c=0;a:while(1){i=0;while(1){e=f[l>>2]|0;g=e+3520|0;if(!(f[g>>2]|0)){if(!(fb(f[e+56>>2]|0,k,8)|0)){c=0;g=31;break a}e=f[k>>2]|0}else{e=h[e+3614>>0]|0;f[k>>2]=e;f[g>>2]=0}if((e|0)==(h[2353+c>>0]|0|0)){g=8;break}if(i>>>0>2){c=0;g=31;break a}if((e|0)!=(h[3178+i>>0]|0|0)){g=21;break}c=i+1|0;if((c|0)==3){if(!(fb(f[(f[l>>2]|0)+56>>2]|0,j,24)|0)){g=18;break a}if(!(fb(f[(f[l>>2]|0)+56>>2]|0,j,8)|0)){g=18;break a}e=f[j>>2]|0;if(!(fb(f[(f[l>>2]|0)+56>>2]|0,j,8)|0)){g=18;break a}g=f[j>>2]|0;if(!(fb(f[(f[l>>2]|0)+56>>2]|0,j,8)|0)){g=18;break a}i=f[j>>2]|0;if(!(fb(f[(f[l>>2]|0)+56>>2]|0,j,8)|0)){g=18;break a}if(!(lb(f[(f[l>>2]|0)+56>>2]|0,f[j>>2]&127|(i&127|(g&127|e<<7&16256)<<7)<<7)|0)){c=0;g=31;break a}}i=c;c=0}if((g|0)==8){d=1;c=c+1|0}else if((g|0)==21){do if((e|0)==255){b[(f[l>>2]|0)+3612>>0]=-1;if(!(fb(f[(f[l>>2]|0)+56>>2]|0,k,8)|0)){c=0;g=31;break a}c=f[k>>2]|0;if((c|0)!=255)if((c&-2|0)==248){g=26;break a}else break;else{i=f[l>>2]|0;b[i+3614>>0]=-1;f[i+3520>>2]=1;break}}while(0);if((d|0)!=0?(m=f[l>>2]|0,(f[m+3656>>2]|0)==0):0){Ma[f[m+32>>2]&7](a,0,f[m+48>>2]|0);d=0;c=0}else{d=0;c=0}}if(c>>>0>=4){g=30;break}}if((g|0)==18){m=0;u=n;return m|0}else if((g|0)==26){b[(f[l>>2]|0)+3613>>0]=c;f[f[a>>2]>>2]=3;m=1;u=n;return m|0}else if((g|0)==30){f[f[a>>2]>>2]=1;m=1;u=n;return m|0}else if((g|0)==31){u=n;return c|0}return 0}
function Kc(a){a=a|0;var c=0,d=0,e=0,g=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0;E=u;u=u+192|0;x=E+188|0;A=E;c=E+184|0;z=E+180|0;o=E+176|0;D=a+4|0;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,c,f[277]|0)|0)){a=0;u=E;return a|0}C=(f[c>>2]|0)!=0;p=C&1;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,z,f[266]|0)|0)){a=0;u=E;return a|0}if(!(fb(f[(f[D>>2]|0)+56>>2]|0,o,f[267]|0)|0)){a=0;u=E;return a|0}n=f[z>>2]|0;a:do switch(n|0){case 0:{d=f[o>>2]|0;B=f[D>>2]|0;f[B+256>>2]=0;f[B+260>>2]=p;f[B+264>>2]=d;c=f[279]|0;if((((((((fb(f[B+56>>2]|0,x,c)|0?(f[(f[D>>2]|0)+272>>2]=f[x>>2],e=f[279]|0,fb(f[(f[D>>2]|0)+56>>2]|0,x,e)|0):0)?(f[(f[D>>2]|0)+276>>2]=f[x>>2],g=f[267]|0,fb(f[(f[D>>2]|0)+56>>2]|0,x,g)|0):0)?(f[(f[D>>2]|0)+280>>2]=f[x>>2],i=f[267]|0,fb(f[(f[D>>2]|0)+56>>2]|0,x,i)|0):0)?(f[(f[D>>2]|0)+284>>2]=f[x>>2],j=f[259]|0,fb(f[(f[D>>2]|0)+56>>2]|0,x,j)|0):0)?(f[(f[D>>2]|0)+288>>2]=f[x>>2],k=f[270]|0,fb(f[(f[D>>2]|0)+56>>2]|0,x,k)|0):0)?(f[(f[D>>2]|0)+292>>2]=(f[x>>2]|0)+1,l=f[275]|0,fb(f[(f[D>>2]|0)+56>>2]|0,x,l)|0):0)?(f[(f[D>>2]|0)+296>>2]=(f[x>>2]|0)+1,m=f[260]|0,B=f[D>>2]|0,ib(f[B+56>>2]|0,B+304|0,m)|0):0)?(B=f[D>>2]|0,mb(f[B+56>>2]|0,B+312|0,16)|0):0){if(!(lb(f[(f[D>>2]|0)+56>>2]|0,d-((c+128+e+g+i+j+k+l+m|0)>>>3)|0)|0)){a=0;u=E;return a|0}c=f[D>>2]|0;f[c+248>>2]=1;if(!(ee(c+312|0,4056,16)|0))f[c+3648>>2]=0;if(f[c+3656>>2]|0){B=200;break a}if(!(f[c+608>>2]|0)){B=200;break a}d=f[c+28>>2]|0;if(!d){B=200;break a}Ma[d&7](a,c+256|0,f[c+48>>2]|0);B=200;break a}a=0;u=E;return a|0}case 3:{d=f[D>>2]|0;f[d+252>>2]=0;k=f[o>>2]|0;f[d+432>>2]=3;f[d+436>>2]=p;f[d+440>>2]=k;f[d+448>>2]=(k>>>0)/18|0;d=f[D>>2]|0;c=d+452|0;e=f[c>>2]|0;d=f[d+448>>2]|0;do if(!d){c=Od(e,0)|0;B=28}else{if(d>>>0<=178956970){B=d*24|0;c=Od(e,B)|0;if(!((B|0)!=0&(c|0)==0)){B=28;break}Md(e);c=(f[D>>2]|0)+452|0}f[c>>2]=0;B=29}while(0);b:do if((B|0)==28){f[(f[D>>2]|0)+452>>2]=c;if(!c)B=29;else{c=f[D>>2]|0;if(!(f[c+448>>2]|0))d=0;else{g=f[280]|0;i=f[280]|0;j=f[279]|0;e=0;do{if(!(ib(f[c+56>>2]|0,A,g)|0))break b;v=A;w=f[v+4>>2]|0;z=f[D>>2]|0;y=(f[z+452>>2]|0)+(e*24|0)|0;f[y>>2]=f[v>>2];f[y+4>>2]=w;if(!(ib(f[z+56>>2]|0,A,i)|0))break b;v=A;w=f[v+4>>2]|0;z=f[D>>2]|0;y=(f[z+452>>2]|0)+(e*24|0)+8|0;f[y>>2]=f[v>>2];f[y+4>>2]=w;if(!(fb(f[z+56>>2]|0,x,j)|0))break b;c=f[D>>2]|0;f[(f[c+452>>2]|0)+(e*24|0)+16>>2]=f[x>>2];e=e+1|0;d=f[c+448>>2]|0}while(e>>>0<d>>>0);d=W(d,-18)|0}d=d+k|0;if(d){if(!(lb(f[c+56>>2]|0,d)|0))break;c=f[D>>2]|0}f[c+252>>2]=1;if(f[c+3656>>2]|0){B=200;break a}if(!(f[c+620>>2]|0)){B=200;break a}d=f[c+28>>2]|0;if(!d){B=200;break a}Ma[d&7](a,c+432|0,f[c+48>>2]|0);B=200;break a}}while(0);if((B|0)==29)f[f[a>>2]>>2]=8;a=0;u=E;return a|0}default:{e=f[D>>2]|0;m=(f[e+608+(n<<2)>>2]|0)!=0;d=(m^1)&1;c=f[o>>2]|0;cf(A|0,0,176)|0;f[A+4>>2]=p;f[A>>2]=n;f[A+8>>2]=c;c:do if((n|0)==2){k=A+16|0;l=(f[265]|0)>>>3;if(mb(f[e+56>>2]|0,k,l)|0){if(c>>>0<l>>>0){f[f[a>>2]>>2]=8;break}c=c-l|0;e=f[D>>2]|0;j=f[e+1124>>2]|0;if(!j)B=54;else{i=f[e+1120>>2]|0;g=0;while(1){if(!(ee(i+(W(g,l)|0)|0,k,l)|0))break;g=g+1|0;if(g>>>0>=j>>>0){B=54;break c}}d=m&1;B=54}}}else B=54;while(0);do if((B|0)==54){if(!d){d:do switch(f[z>>2]|0){case 1:{if(!(lb(f[e+56>>2]|0,c)|0))j=0;else B=167;break}case 2:{if(!c){f[A+20>>2]=0;B=167;break d}d=Ld(c)|0;f[A+20>>2]=d;if(d)if(!(mb(f[e+56>>2]|0,d,c)|0)){j=0;break d}else{B=167;break d}else{f[f[a>>2]>>2]=8;j=0;break d}}case 4:{e:do if(c>>>0>7){i=A+16|0;c=c+-8|0;if(!(jb(f[e+56>>2]|0,i)|0)){j=0;break d}g=f[i>>2]|0;do if(!g)f[A+20>>2]=0;else{if(c>>>0<g>>>0){f[i>>2]=0;f[A+20>>2]=0;break e}c=c-g|0;d=g+1|0;if((g|0)!=-1){d=Ld(d+((d|0)==0&1)|0)|0;e=A+20|0;f[e>>2]=d;if(d|0){if(!(mb(f[(f[D>>2]|0)+56>>2]|0,d,g)|0)){j=0;break d}b[(f[e>>2]|0)+(f[i>>2]|0)>>0]=0;break}}else f[A+20>>2]=0;f[f[a>>2]>>2]=8;j=0;break d}while(0);l=A+24|0;if(!(jb(f[(f[D>>2]|0)+56>>2]|0,l)|0)){j=0;break d}d=f[l>>2]|0;if(d>>>0>1e5){f[l>>2]=0;j=0;break d}if(d){e=xc(d,8)|0;k=A+28|0;f[k>>2]=e;if(!e){f[l>>2]=0;f[f[a>>2]>>2]=8;j=0;break d}if(f[l>>2]|0){f[e>>2]=0;f[e+4>>2]=0;f:do if(c>>>0<4)d=0;else{d=0;while(1){c=c+-4|0;if(!(jb(f[(f[D>>2]|0)+56>>2]|0,e)|0)){B=86;break}i=f[k>>2]|0;j=i+(d<<3)|0;g=f[j>>2]|0;if(!g)f[i+(d<<3)+4>>2]=0;else{if(c>>>0<g>>>0){B=89;break}c=c-g|0;e=g+1|0;if((g|0)==-1){B=91;break}g=Ld(e+((e|0)==0&1)|0)|0;e=i+(d<<3)+4|0;f[e>>2]=g;if(!g)break;cf(g|0,0,f[j>>2]|0)|0;if(!(mb(f[(f[D>>2]|0)+56>>2]|0,f[e>>2]|0,f[j>>2]|0)|0)){B=95;break}x=f[k>>2]|0;b[(f[x+(d<<3)+4>>2]|0)+(f[x+(d<<3)>>2]|0)>>0]=0}d=d+1|0;if(d>>>0>=(f[l>>2]|0)>>>0)break e;x=f[k>>2]|0;e=x+(d<<3)|0;f[e>>2]=0;f[x+(d<<3)+4>>2]=0;if(c>>>0<4)break f}if((B|0)==86){f[l>>2]=d;j=0;break d}else if((B|0)==89){f[l>>2]=d;break e}else if((B|0)==91)f[i+(d<<3)+4>>2]=0;else if((B|0)==95){f[l>>2]=d;break e}f[f[a>>2]>>2]=8;f[l>>2]=d;j=0;break d}while(0);f[l>>2]=d}}}while(0);if(c){if(!(f[A+24>>2]|0)){x=A+28|0;Md(f[x>>2]|0);f[x>>2]=0}if(!(lb(f[(f[D>>2]|0)+56>>2]|0,c)|0))j=0;else B=167}else B=167;break}case 5:{w=A+16|0;cf(w|0,0,160)|0;g:do if((((mb(f[e+56>>2]|0,w,(f[263]|0)>>>3)|0?ib(f[(f[D>>2]|0)+56>>2]|0,A+152|0,f[280]|0)|0:0)?fb(f[(f[D>>2]|0)+56>>2]|0,x,f[277]|0)|0:0)?(f[A+160>>2]=(f[x>>2]|0)!=0&1,kb(f[(f[D>>2]|0)+56>>2]|0,f[264]|0)|0):0)?fb(f[(f[D>>2]|0)+56>>2]|0,x,f[271]|0)|0:0){c=f[x>>2]|0;w=A+164|0;f[w>>2]=c;h:do if(c|0){c=Nd(c,32)|0;k=A+168|0;f[k>>2]=c;if(!c){f[f[a>>2]>>2]=8;break g}l=f[280]|0;m=f[271]|0;n=(f[261]|0)>>>3;o=f[277]|0;p=f[277]|0;q=f[262]|0;r=f[271]|0;s=f[280]|0;t=f[271]|0;v=f[267]|0;if(!(ib(f[(f[D>>2]|0)+56>>2]|0,c,l)|0))break g;j=0;i:while(1){if(!(fb(f[(f[D>>2]|0)+56>>2]|0,x,m)|0))break g;b[c+(j<<5)+8>>0]=f[x>>2];if(!(mb(f[(f[D>>2]|0)+56>>2]|0,c+(j<<5)+9|0,n)|0))break g;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,x,o)|0))break g;d=c+(j<<5)+22|0;b[d>>0]=b[d>>0]&-2|f[x>>2]&1;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,x,p)|0))break g;b[d>>0]=(f[x>>2]&255)<<1&2|b[d>>0]&-3;if(!(kb(f[(f[D>>2]|0)+56>>2]|0,q)|0))break g;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,x,r)|0))break g;d=f[x>>2]|0;i=c+(j<<5)+23|0;b[i>>0]=d;d=d&255;j:do if(d|0){e=Nd(d,16)|0;g=c+(j<<5)+24|0;f[g>>2]=e;if(!e)break i;if(!(b[i>>0]|0))break;if(!(ib(f[(f[D>>2]|0)+56>>2]|0,e,s)|0))break g;d=0;c=e;while(1){if(!(fb(f[(f[D>>2]|0)+56>>2]|0,x,t)|0))break g;b[c+(d<<4)+8>>0]=f[x>>2];d=d+1|0;if(!(kb(f[(f[D>>2]|0)+56>>2]|0,v)|0))break g;if(d>>>0>=(h[i>>0]|0)>>>0)break j;c=f[g>>2]|0;if(!(ib(f[(f[D>>2]|0)+56>>2]|0,c+(d<<4)|0,s)|0))break g}}while(0);j=j+1|0;if(j>>>0>=(f[w>>2]|0)>>>0)break h;c=f[k>>2]|0;if(!(ib(f[(f[D>>2]|0)+56>>2]|0,c+(j<<5)|0,l)|0))break g}f[f[a>>2]>>2]=8;break g}while(0);B=167;break d}while(0);j=0;break}case 6:{do if(fb(f[e+56>>2]|0,x,f[265]|0)|0?(f[A+16>>2]=f[x>>2],fb(f[(f[D>>2]|0)+56>>2]|0,x,f[265]|0)|0):0){e=f[x>>2]|0;c=e+1|0;if((e|0)!=-1){c=Ld(c+((c|0)==0&1)|0)|0;d=A+20|0;f[d>>2]=c;if(c|0){if(!e)d=0;else{if(!(mb(f[(f[D>>2]|0)+56>>2]|0,c,e)|0))break;c=f[d>>2]|0;d=f[x>>2]|0}b[c+d>>0]=0;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,x,f[265]|0)|0))break;e=f[x>>2]|0;c=e+1|0;if((e|0)!=-1){d=Ld(c+((c|0)==0&1)|0)|0;c=A+24|0;f[c>>2]=d;if(d|0){if(!e)c=0;else{if(!(mb(f[(f[D>>2]|0)+56>>2]|0,d,e)|0))break;d=f[c>>2]|0;c=f[x>>2]|0}b[d+c>>0]=0;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,A+28|0,f[265]|0)|0))break;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,A+32|0,f[265]|0)|0))break;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,A+36|0,f[265]|0)|0))break;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,A+40|0,f[265]|0)|0))break;c=A+44|0;if(!(fb(f[(f[D>>2]|0)+56>>2]|0,c,f[265]|0)|0))break;c=f[c>>2]|0;d=(c|0)==0;e=Ld(c+(d&1)|0)|0;f[A+48>>2]=e;if(!e){f[f[a>>2]>>2]=8;break}if(!d?(mb(f[(f[D>>2]|0)+56>>2]|0,e,c)|0)==0:0)break;B=167;break d}}else f[A+24>>2]=0;f[f[a>>2]>>2]=8;break}}else f[A+20>>2]=0;f[f[a>>2]>>2]=8}while(0);j=0;break}case 3:case 0:{B=167;break}default:{if(!c){f[A+16>>2]=0;B=167;break d}d=Ld(c)|0;f[A+16>>2]=d;if(d)if(!(mb(f[e+56>>2]|0,d,c)|0)){j=0;break d}else{B=167;break d}else{f[f[a>>2]>>2]=8;j=0;break d}}}while(0);if((B|0)==167){c=f[D>>2]|0;if((f[c+3656>>2]|0)==0?(y=f[c+28>>2]|0,(y|0)!=0):0){Ma[y&7](a,A,f[c+48>>2]|0);j=1}else j=1}switch(f[z>>2]|0){case 1:break;case 2:{c=f[A+20>>2]|0;if(c|0)Md(c);break}case 4:{c=f[A+20>>2]|0;if(c|0)Md(c);i=A+24|0;c=f[i>>2]|0;g=A+28|0;if(c|0){e=0;do{d=f[(f[g>>2]|0)+(e<<3)+4>>2]|0;if(d){Md(d);c=f[i>>2]|0}e=e+1|0}while(e>>>0<c>>>0)}c=f[g>>2]|0;if(c|0)Md(c);break}case 5:{i=A+164|0;c=f[i>>2]|0;g=A+168|0;if(c|0){e=0;do{d=f[(f[g>>2]|0)+(e<<5)+24>>2]|0;if(d){Md(d);c=f[i>>2]|0}e=e+1|0}while(e>>>0<c>>>0)}c=f[g>>2]|0;if(c|0)Md(c);break}case 6:{c=f[A+20>>2]|0;if(c|0)Md(c);c=f[A+24>>2]|0;if(c|0)Md(c);c=f[A+48>>2]|0;if(c|0)Md(c);break}default:{c=f[A+16>>2]|0;if(c|0)Md(c)}}if(!j)break}else if(!(lb(f[e+56>>2]|0,c)|0))break;if(C)break a;else c=1;u=E;return c|0}while(0);a=0;u=E;return a|0}}while(0);if((B|0)==200?!C:0){a=1;u=E;return a|0}c=f[D>>2]|0;d=c+6160|0;e=f[c+12>>2]|0;if(((e|0)!=0?(Ja[e&15](a,d,f[c+48>>2]|0)|0)==0:0)?(cb(f[(f[D>>2]|0)+56>>2]|0)|0)!=0:0){C=(eb(f[(f[D>>2]|0)+56>>2]|0)|0)>>>3;D=d;C=Te(f[D>>2]|0,f[D+4>>2]|0,C|0,0)|0;D=d;f[D>>2]=C;f[D+4>>2]=H}else{D=(f[D>>2]|0)+6160|0;f[D>>2]=0;f[D+4>>2]=0}f[f[a>>2]>>2]=2;a=1;u=E;return a|0}function Lc(a){a=a|0;var c=0,d=0,e=0,g=0,i=0,j=0,k=0,l=0,m=0;k=u;u=u+16|0;g=k;j=a+4|0;e=f[j>>2]|0;if((f[e+248>>2]|0?(d=e+304|0,c=f[d>>2]|0,d=f[d+4>>2]|0,!((c|0)==0&(d|0)==0)):0)?(l=e+240|0,m=f[l+4>>2]|0,!(m>>>0<d>>>0|((m|0)==(d|0)?(f[l>>2]|0)>>>0<c>>>0:0))):0){f[f[a>>2]>>2]=4;m=1;u=k;return m|0}if((cb(f[e+56>>2]|0)|0)==0?(m=f[(f[j>>2]|0)+56>>2]|0,(fb(m,g,db(m)|0)|0)==0):0){m=0;u=k;return m|0}e=1;a:while(1){c=f[j>>2]|0;d=c+3520|0;if(!(f[d>>2]|0)){if(!(fb(f[c+56>>2]|0,g,8)|0)){c=0;d=22;break}c=f[g>>2]|0}else{c=h[c+3614>>0]|0;f[g>>2]=c;f[d>>2]=0}do if((c|0)==255){b[(f[j>>2]|0)+3612>>0]=-1;if(!(fb(f[(f[j>>2]|0)+56>>2]|0,g,8)|0)){c=0;d=22;break a}c=f[g>>2]|0;if((c|0)!=255)if((c&-2|0)==248){d=17;break a}else break;else{m=f[j>>2]|0;b[m+3614>>0]=-1;f[m+3520>>2]=1;break}}while(0);if(e|0?(i=f[j>>2]|0,(f[i+3656>>2]|0)==0):0)Ma[f[i+32>>2]&7](a,0,f[i+48>>2]|0);e=0}if((d|0)==17){b[(f[j>>2]|0)+3613>>0]=c;f[f[a>>2]>>2]=3;m=1;u=k;return m|0}else if((d|0)==22){u=k;return c|0}return 0}function Mc(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,g=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,I=0,J=0,K=0,L=0;L=u;u=u+80|0;J=L+40|0;A=L;B=L+24|0;p=L+16|0;o=L+12|0;E=L+8|0;f[c>>2]=0;K=a+4|0;j=f[K>>2]|0;z=f[8+((h[j+3612>>0]|0)<<2)>>2]|0;ab(f[j+56>>2]|0,(f[8+((z>>>8^(h[j+3613>>0]|0))<<2)>>2]^z<<8)&65535);z=f[K>>2]|0;b[B>>0]=b[z+3612>>0]|0;j=b[z+3613>>0]|0;l=B+1|0;b[l>>0]=j;f[p>>2]=2;j=(j&255)>>>1&1;a:do if(fb(f[z+56>>2]|0,J,8)|0){e=f[J>>2]|0;b:do if((e|0)!=255){f[p>>2]=3;i=B+2|0;b[i>>0]=e;if(!(fb(f[(f[K>>2]|0)+56>>2]|0,J,8)|0))break a;e=f[J>>2]|0;if((e|0)!=255){z=f[p>>2]|0;f[p>>2]=z+1;b[B+z>>0]=e;i=b[i>>0]|0;e=(i&255)>>>4;f[J>>2]=e;switch(e&15){case 0:{m=0;k=1;break}case 1:{f[(f[K>>2]|0)+1136>>2]=192;m=0;k=j;break}case 5:case 4:case 3:case 2:{f[(f[K>>2]|0)+1136>>2]=576<<e+-2;m=0;k=j;break}case 7:case 6:{m=e;k=j;break}case 15:case 14:case 13:case 12:case 11:case 10:case 9:case 8:{f[(f[K>>2]|0)+1136>>2]=256<<e+-8;m=0;k=j;break}default:{}}e=i&15;f[J>>2]=e;do switch(i&15){case 0:{e=f[K>>2]|0;if(!(f[e+248>>2]|0)){n=0;g=1}else{f[e+1140>>2]=f[e+288>>2];n=0;g=k}break}case 1:{f[(f[K>>2]|0)+1140>>2]=88200;n=0;g=k;break}case 2:{f[(f[K>>2]|0)+1140>>2]=176400;n=0;g=k;break}case 3:{f[(f[K>>2]|0)+1140>>2]=192e3;n=0;g=k;break}case 4:{f[(f[K>>2]|0)+1140>>2]=8e3;n=0;g=k;break}case 5:{f[(f[K>>2]|0)+1140>>2]=16e3;n=0;g=k;break}case 6:{f[(f[K>>2]|0)+1140>>2]=22050;n=0;g=k;break}case 7:{f[(f[K>>2]|0)+1140>>2]=24e3;n=0;g=k;break}case 8:{f[(f[K>>2]|0)+1140>>2]=32e3;n=0;g=k;break}case 9:{f[(f[K>>2]|0)+1140>>2]=44100;n=0;g=k;break}case 10:{f[(f[K>>2]|0)+1140>>2]=48e3;n=0;g=k;break}case 11:{f[(f[K>>2]|0)+1140>>2]=96e3;n=0;g=k;break}case 14:case 13:case 12:{n=e;g=k;break}case 15:{e=f[K>>2]|0;if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,1,f[e+48>>2]|0);e=f[a>>2]|0;f[e>>2]=2;z=a;break b}default:{}}while(0);k=b[B+3>>0]|0;z=k&255;e=z>>>4;f[J>>2]=e;c:do if(!(z&128)){G=f[K>>2]|0;f[G+1144>>2]=e+1;i=0;e=G;G=35}else{j=f[K>>2]|0;f[j+1144>>2]=2;switch(e&7){case 0:{i=1;e=j;G=35;break c}case 1:{i=2;e=j;G=35;break c}case 2:{i=3;e=j;G=35;break c}default:{g=1;e=j;break c}}}while(0);if((G|0)==35)f[e+1148>>2]=i;z=(k&14)>>>1;f[J>>2]=z;switch(z&7){case 0:{if(!(f[e+248>>2]|0))q=1;else{f[e+1152>>2]=f[e+296>>2];q=g}break}case 1:{f[e+1152>>2]=8;q=g;break}case 2:{f[e+1152>>2]=12;q=g;break}case 4:{f[e+1152>>2]=16;q=g;break}case 5:{f[e+1152>>2]=20;q=g;break}case 6:{f[e+1152>>2]=24;q=g;break}case 7:case 3:{q=1;break}default:{}}j=(k&1)==0;do if(!(b[l>>0]&1)){if(f[e+248>>2]|0?(f[e+272>>2]|0)!=(f[e+276>>2]|0):0){G=48;break}if(!(pb(f[e+56>>2]|0,J,B,p)|0)){a=0;u=L;return a|0}e=f[J>>2]|0;if((e|0)!=-1){z=f[K>>2]|0;f[z+1156>>2]=0;f[z+1160>>2]=e;break}e=f[K>>2]|0;b[e+3614>>0]=b[B+((f[p>>2]|0)+-1)>>0]|0;f[e+3520>>2]=1;if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,1,f[e+48>>2]|0);e=f[a>>2]|0;f[e>>2]=2;z=a;break b}else G=48;while(0);do if((G|0)==48){if(!(qb(f[e+56>>2]|0,A,B,p)|0)){a=0;u=L;return a|0}g=A;e=f[g>>2]|0;g=f[g+4>>2]|0;if(!((e|0)==-1&(g|0)==-1)){z=f[K>>2]|0;f[z+1156>>2]=1;z=z+1160|0;f[z>>2]=e;f[z+4>>2]=g;break}e=f[K>>2]|0;b[e+3614>>0]=b[B+((f[p>>2]|0)+-1)>>0]|0;f[e+3520>>2]=1;if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,1,f[e+48>>2]|0);e=f[a>>2]|0;f[e>>2]=2;z=a;break b}while(0);e=f[K>>2]|0;if(m){if(!(fb(f[e+56>>2]|0,J,8)|0)){a=0;u=L;return a|0}e=f[J>>2]|0;z=f[p>>2]|0;f[p>>2]=z+1;b[B+z>>0]=e;do if((m|0)==7){if(fb(f[(f[K>>2]|0)+56>>2]|0,o,8)|0){e=f[o>>2]|0;z=f[p>>2]|0;f[p>>2]=z+1;b[B+z>>0]=e;e=f[J>>2]<<8|e;f[J>>2]=e;break}a=0;u=L;return a|0}while(0);z=f[K>>2]|0;f[z+1136>>2]=e+1;e=z}if(n){if(!(fb(f[e+56>>2]|0,J,8)|0)){a=0;u=L;return a|0}e=f[J>>2]|0;z=f[p>>2]|0;f[p>>2]=z+1;b[B+z>>0]=e;do if((n|0)!=12){if(!(fb(f[(f[K>>2]|0)+56>>2]|0,o,8)|0)){a=0;u=L;return a|0}g=f[o>>2]|0;z=f[p>>2]|0;f[p>>2]=z+1;b[B+z>>0]=g;g=f[J>>2]<<8|g;f[J>>2]=g;if((n|0)==13){e=f[K>>2]|0;break}else{g=g*10|0;e=f[K>>2]|0;break}}else{g=e*1e3|0;e=f[K>>2]|0}while(0);f[e+1140>>2]=g}if(!(fb(f[e+56>>2]|0,J,8)|0)){a=0;u=L;return a|0}z=f[J>>2]|0;z=(Nb(B,f[p>>2]|0)|0)<<24>>24==(z&255)<<24>>24;e=f[K>>2]|0;if(!z){if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,1,f[e+48>>2]|0);e=f[a>>2]|0;f[e>>2]=2;z=a;break}f[e+232>>2]=0;g=e+1156|0;do if(!(f[g>>2]|0)){i=f[e+1160>>2]|0;f[J>>2]=i;f[g>>2]=1;g=f[e+228>>2]|0;if(g|0){z=Re(g|0,0,i|0,0)|0;G=e+1160|0;f[G>>2]=z;f[G+4>>2]=H;G=91;break}if(f[e+248>>2]|0){g=f[e+272>>2]|0;if((g|0)!=(f[e+276>>2]|0))break;z=Re(g|0,0,i|0,0)|0;G=e+1160|0;f[G>>2]=z;f[G+4>>2]=H;G=f[K>>2]|0;f[G+232>>2]=f[G+276>>2];G=91;break}if(!i){G=e+1160|0;f[G>>2]=0;f[G+4>>2]=0;G=f[K>>2]|0;f[G+232>>2]=f[G+1136>>2];G=91;break}else{z=Re(f[e+1136>>2]|0,0,i|0,0)|0;G=e+1160|0;f[G>>2]=z;f[G+4>>2]=H;G=91;break}}else G=91;while(0);do if((G|0)==91)if(j&(q|0)==0){e=f[a>>2]|0;z=a;break b}else{e=f[K>>2]|0;break}while(0);if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,3,f[e+48>>2]|0);else{z=e+6176|0;f[z>>2]=(f[z>>2]|0)+1}e=f[a>>2]|0;f[e>>2]=2;z=a}else G=3}else G=3;while(0);if((G|0)==3){e=f[K>>2]|0;b[e+3614>>0]=-1;f[e+3520>>2]=1;if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,1,f[e+48>>2]|0);e=f[a>>2]|0;f[e>>2]=2;z=a}if((f[e>>2]|0)==2){a=1;u=L;return a|0}e=f[K>>2]|0;k=f[e+1136>>2]|0;g=f[e+1144>>2]|0;if(!((f[e+220>>2]|0)>>>0>=k>>>0?(f[e+224>>2]|0)>>>0>=g>>>0:0)){i=f[e+60>>2]|0;if(i){Md(i+-16|0);f[(f[K>>2]|0)+60>>2]=0;e=f[K>>2]|0}i=f[e+3616>>2]|0;if(i){Md(i);f[(f[K>>2]|0)+92>>2]=0;f[(f[K>>2]|0)+3616>>2]=0;e=f[K>>2]|0}i=f[e+64>>2]|0;if(i){Md(i+-16|0);f[(f[K>>2]|0)+64>>2]=0;e=f[K>>2]|0}i=f[e+3620>>2]|0;if(i){Md(i);f[(f[K>>2]|0)+96>>2]=0;f[(f[K>>2]|0)+3620>>2]=0;e=f[K>>2]|0}i=f[e+68>>2]|0;if(i){Md(i+-16|0);f[(f[K>>2]|0)+68>>2]=0;e=f[K>>2]|0}i=f[e+3624>>2]|0;if(i){Md(i);f[(f[K>>2]|0)+100>>2]=0;f[(f[K>>2]|0)+3624>>2]=0;e=f[K>>2]|0}i=f[e+72>>2]|0;if(i){Md(i+-16|0);f[(f[K>>2]|0)+72>>2]=0;e=f[K>>2]|0}i=f[e+3628>>2]|0;if(i){Md(i);f[(f[K>>2]|0)+104>>2]=0;f[(f[K>>2]|0)+3628>>2]=0;e=f[K>>2]|0}i=f[e+76>>2]|0;if(i){Md(i+-16|0);f[(f[K>>2]|0)+76>>2]=0;e=f[K>>2]|0}i=f[e+3632>>2]|0;if(i){Md(i);f[(f[K>>2]|0)+108>>2]=0;f[(f[K>>2]|0)+3632>>2]=0;e=f[K>>2]|0}i=f[e+80>>2]|0;if(i){Md(i+-16|0);f[(f[K>>2]|0)+80>>2]=0;e=f[K>>2]|0}i=f[e+3636>>2]|0;if(i){Md(i);f[(f[K>>2]|0)+112>>2]=0;f[(f[K>>2]|0)+3636>>2]=0;e=f[K>>2]|0}i=f[e+84>>2]|0;if(i){Md(i+-16|0);f[(f[K>>2]|0)+84>>2]=0;e=f[K>>2]|0}i=f[e+3640>>2]|0;if(i){Md(i);f[(f[K>>2]|0)+116>>2]=0;f[(f[K>>2]|0)+3640>>2]=0;e=f[K>>2]|0}i=f[e+88>>2]|0;if(i){Md(i+-16|0);f[(f[K>>2]|0)+88>>2]=0;e=f[K>>2]|0}e=f[e+3644>>2]|0;if(e|0){Md(e);f[(f[K>>2]|0)+120>>2]=0;f[(f[K>>2]|0)+3644>>2]=0}d:do if(g|0){y=k+4|0;i=y<<2;e:do if(!(k>>>0>4294967291|y>>>0>1073741823)){e=0;while(1){j=Ld(i)|0;if(!j)break e;f[j>>2]=0;f[j+4>>2]=0;f[j+8>>2]=0;f[j+12>>2]=0;f[(f[K>>2]|0)+60+(e<<2)>>2]=j+16;y=f[K>>2]|0;if(!(tc(k,y+3616+(e<<2)|0,y+92+(e<<2)|0)|0))break;e=e+1|0;if(e>>>0>=g>>>0)break d}f[f[z>>2]>>2]=8;a=0;u=L;return a|0}while(0);f[f[z>>2]>>2]=8;a=0;u=L;return a|0}while(0);y=f[K>>2]|0;f[y+220>>2]=k;f[y+224>>2]=g;e=y;g=f[y+1144>>2]|0}f:do if(g){r=(d|0)!=0;s=(d|0)==0;t=f[278]|0;v=f[274]|0;w=f[274]|0;x=(1<<w)+-1|0;y=f[275]|0;q=0;g:while(1){g=f[e+1152>>2]|0;switch(f[e+1148>>2]|0){case 3:{g=g+((q|0)==1&1)|0;break}case 1:{g=g+((q|0)==1&1)|0;break}case 2:{g=g+((q|0)==0&1)|0;break}default:{}}if(!(fb(f[e+56>>2]|0,B,8)|0))break;p=f[B>>2]|0;e=p&254;f[B>>2]=e;p=(p&1|0)!=0;if(p){if(!(nb(f[(f[K>>2]|0)+56>>2]|0,J)|0)){G=155;break}i=(f[J>>2]|0)+1|0;e=f[K>>2]|0;f[e+1176+(q*292|0)+288>>2]=i;if(g>>>0<=i>>>0)break;g=g-i|0;i=f[B>>2]|0}else{o=f[K>>2]|0;f[o+1176+(q*292|0)+288>>2]=0;i=e;e=o}h:do if(!(i&128)){i:do switch(i|0){case 0:{i=f[e+60+(q<<2)>>2]|0;f[e+1176+(q*292|0)>>2]=0;if(!(hb(f[e+56>>2]|0,J,g)|0)){G=171;break g}g=f[J>>2]|0;f[e+1176+(q*292|0)+4>>2]=g;if((!s?(C=(f[K>>2]|0)+1136|0,f[C>>2]|0):0)?(f[i>>2]=g,(f[C>>2]|0)>>>0>1):0){e=1;do{f[i+(e<<2)>>2]=f[J>>2];e=e+1|0}while(e>>>0<(f[C>>2]|0)>>>0)}G=250;break}case 2:{l=e+1176+(q*292|0)+4|0;k=f[e+92+(q<<2)>>2]|0;f[e+1176+(q*292|0)>>2]=1;f[l>>2]=k;if(!(f[e+1136>>2]|0))g=0;else{j=0;do{if(!(hb(f[e+56>>2]|0,J,g)|0)){G=180;break g}f[k+(j<<2)>>2]=f[J>>2];j=j+1|0;e=f[K>>2]|0;i=f[e+1136>>2]|0}while(j>>>0<i>>>0);g=i<<2}if(!s)af(f[e+60+(q<<2)>>2]|0,f[l>>2]|0,g|0)|0;G=250;break}default:{if(i>>>0<16){if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,3,f[e+48>>2]|0);else{p=e+6176|0;f[p>>2]=(f[p>>2]|0)+1}f[f[z>>2]>>2]=2;break h}if(i>>>0<25){l=i>>>1&7;f[e+1176+(q*292|0)>>2]=2;f[e+1176+(q*292|0)+36>>2]=f[e+92+(q<<2)>>2];f[e+1176+(q*292|0)+16>>2]=l;i=f[e+56>>2]|0;if(!l)g=i;else{k=e+1176+(q*292|0)+20|0;j=0;while(1){if(!(hb(i,J,g)|0)){G=206;break g}f[k+(j<<2)>>2]=f[J>>2];j=j+1|0;i=f[(f[K>>2]|0)+56>>2]|0;if(j>>>0>=l>>>0){g=i;break}}}if(!(fb(g,A,t)|0)){G=206;break g}o=f[A>>2]|0;i=e+1176+(q*292|0)+4|0;f[i>>2]=o;g=f[K>>2]|0;do if(o>>>0<2){if(!(fb(f[g+56>>2]|0,A,v)|0)){G=206;break g}g=f[K>>2]|0;j=f[A>>2]|0;if((f[g+1136>>2]|0)>>>j>>>0<l>>>0){if(!(f[g+3656>>2]|0))Ma[f[g+32>>2]&7](a,0,f[g+48>>2]|0);f[f[z>>2]>>2]=2;break}f[e+1176+(q*292|0)+8>>2]=j;f[e+1176+(q*292|0)+12>>2]=(f[K>>2]|0)+124+(q*12|0);g=f[i>>2]|0;if(g>>>0<2?(o=f[K>>2]|0,(Nc(a,l,j,o+124+(q*12|0)|0,f[o+92+(q<<2)>>2]|0,(g|0)==1&1)|0)==0):0){G=206;break g}if(s)break;af(f[(f[K>>2]|0)+60+(q<<2)>>2]|0,e+1176+(q*292|0)+20|0,l<<2|0)|0;o=f[K>>2]|0;Sb(f[o+92+(q<<2)>>2]|0,(f[o+1136>>2]|0)-l|0,l,(f[o+60+(q<<2)>>2]|0)+(l<<2)|0)}else{if(!(f[g+3656>>2]|0))Ma[f[g+32>>2]&7](a,3,f[g+48>>2]|0);else{o=g+6176|0;f[o>>2]=(f[o>>2]|0)+1}f[f[z>>2]>>2]=2}while(0);if(r&p&(f[f[z>>2]>>2]|0)!=2)break i;else break h}if(i>>>0<64){if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,3,f[e+48>>2]|0);else{p=e+6176|0;f[p>>2]=(f[p>>2]|0)+1}f[f[z>>2]>>2]=2;break h}m=i>>>1&31;o=m+1|0;f[e+1176+(q*292|0)>>2]=3;f[e+1176+(q*292|0)+284>>2]=f[e+92+(q<<2)>>2];f[e+1176+(q*292|0)+16>>2]=o;i=0;j=f[e+56>>2]|0;while(1){if(!(hb(j,J,g)|0)){G=248;break g}f[e+1176+(q*292|0)+156+(i<<2)>>2]=f[J>>2];j=f[(f[K>>2]|0)+56>>2]|0;if(i>>>0>=m>>>0)break;else i=i+1|0}if(!(fb(j,A,w)|0)){G=248;break g}i=f[A>>2]|0;do if((i|0)!=(x|0)){l=e+1176+(q*292|0)+20|0;f[l>>2]=i+1;if(!(hb(f[(f[K>>2]|0)+56>>2]|0,J,y)|0)){G=248;break g}i=f[J>>2]|0;if((i|0)<0){e=f[K>>2]|0;if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,0,f[e+48>>2]|0);f[f[z>>2]>>2]=2;break}n=e+1176+(q*292|0)+24|0;f[n>>2]=i;i=0;j=f[(f[K>>2]|0)+56>>2]|0;while(1){if(!(hb(j,J,f[l>>2]|0)|0)){G=248;break g}f[e+1176+(q*292|0)+28+(i<<2)>>2]=f[J>>2];j=f[(f[K>>2]|0)+56>>2]|0;if(i>>>0>=m>>>0)break;else i=i+1|0}if(!(fb(j,A,t)|0)){G=248;break g}k=f[A>>2]|0;j=e+1176+(q*292|0)+4|0;f[j>>2]=k;i=f[K>>2]|0;if(k>>>0>=2){if(!(f[i+3656>>2]|0))Ma[f[i+32>>2]&7](a,3,f[i+48>>2]|0);else{o=i+6176|0;f[o>>2]=(f[o>>2]|0)+1}f[f[z>>2]>>2]=2;break}if(!(fb(f[i+56>>2]|0,A,v)|0)){G=248;break g}i=f[K>>2]|0;k=f[A>>2]|0;if((f[i+1136>>2]|0)>>>k>>>0<=m>>>0){if(!(f[i+3656>>2]|0))Ma[f[i+32>>2]&7](a,0,f[i+48>>2]|0);f[f[z>>2]>>2]=2;break}f[e+1176+(q*292|0)+8>>2]=k;f[e+1176+(q*292|0)+12>>2]=(f[K>>2]|0)+124+(q*12|0);i=f[j>>2]|0;if(i>>>0<2?(m=f[K>>2]|0,(Nc(a,o,k,m+124+(q*12|0)|0,f[m+92+(q<<2)>>2]|0,(i|0)==1&1)|0)==0):0){G=248;break g}if(s)break;af(f[(f[K>>2]|0)+60+(q<<2)>>2]|0,e+1176+(q*292|0)+156|0,o<<2|0)|0;i=f[l>>2]|0;if((((Z(o|0)|0)^31)+g+i|0)>>>0>=33){m=f[K>>2]|0;Oa[f[m+40>>2]&15](f[m+92+(q<<2)>>2]|0,(f[m+1136>>2]|0)-o|0,e+1176+(q*292|0)+28|0,o,f[n>>2]|0,(f[m+60+(q<<2)>>2]|0)+(o<<2)|0);break}j=f[K>>2]|0;if(g>>>0<17&i>>>0<17){Oa[f[j+44>>2]&15](f[j+92+(q<<2)>>2]|0,(f[j+1136>>2]|0)-o|0,e+1176+(q*292|0)+28|0,o,f[n>>2]|0,(f[j+60+(q<<2)>>2]|0)+(o<<2)|0);break}else{Oa[f[j+36>>2]&15](f[j+92+(q<<2)>>2]|0,(f[j+1136>>2]|0)-o|0,e+1176+(q*292|0)+28|0,o,f[n>>2]|0,(f[j+60+(q<<2)>>2]|0)+(o<<2)|0);break}}else{e=f[K>>2]|0;if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,0,f[e+48>>2]|0);f[f[z>>2]>>2]=2}while(0);if(!(r&p&(f[f[z>>2]>>2]|0)!=2))break h}}while(0);if((G|0)==250?(G=0,!(r&p)):0)break;e=f[K>>2]|0;g=f[e+1176+(q*292|0)+288>>2]|0;f[B>>2]=g;i=e+1136|0;if(f[i>>2]|0?(D=f[e+60+(q<<2)>>2]|0,f[D>>2]=f[D>>2]<<g,(f[i>>2]|0)>>>0>1):0){e=1;do{p=D+(e<<2)|0;f[p>>2]=f[p>>2]<<f[B>>2];e=e+1|0}while(e>>>0<(f[i>>2]|0)>>>0)}}else{if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,0,f[e+48>>2]|0);f[f[z>>2]>>2]=2}while(0);q=q+1|0;if((f[f[z>>2]>>2]|0)==2){e=1;G=310;break}e=f[K>>2]|0;if(q>>>0>=(f[e+1144>>2]|0)>>>0)break f}if((G|0)!=155)if((G|0)!=171)if((G|0)!=180)if((G|0)!=206)if((G|0)!=248)if((G|0)==310){u=L;return e|0}a=0;u=L;return a|0}while(0);if(!(cb(f[e+56>>2]|0)|0)){f[J>>2]=0;D=f[(f[K>>2]|0)+56>>2]|0;if(!(fb(D,J,db(D)|0)|0)){a=0;u=L;return a|0}if(f[J>>2]|0){e=f[K>>2]|0;if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,0,f[e+48>>2]|0);f[f[z>>2]>>2]=2}}if((f[f[z>>2]>>2]|0)==2){a=1;u=L;return a|0}e=bb(f[(f[K>>2]|0)+56>>2]|0)|0;if(!(fb(f[(f[K>>2]|0)+56>>2]|0,E,f[279]|0)|0)){a=0;u=L;return a|0}j:do if((f[E>>2]|0)==(e&65535|0)){if(d|0){e=f[K>>2]|0;switch(f[e+1148>>2]|0){case 3:{i=e+1136|0;if(!(f[i>>2]|0))break j;j=f[e+60>>2]|0;g=f[e+64>>2]|0;e=0;do{C=j+(e<<2)|0;F=g+(e<<2)|0;E=f[F>>2]|0;D=E&1|f[C>>2]<<1;f[C>>2]=D+E>>1;f[F>>2]=D-E>>1;e=e+1|0}while(e>>>0<(f[i>>2]|0)>>>0);break}case 1:{i=e+1136|0;if(!(f[i>>2]|0))break j;j=f[e+60>>2]|0;g=f[e+64>>2]|0;e=0;do{F=g+(e<<2)|0;f[F>>2]=(f[j+(e<<2)>>2]|0)-(f[F>>2]|0);e=e+1|0}while(e>>>0<(f[i>>2]|0)>>>0);break}case 2:{i=e+1136|0;if(!(f[i>>2]|0))break j;j=f[e+64>>2]|0;g=f[e+60>>2]|0;e=0;do{F=g+(e<<2)|0;f[F>>2]=(f[F>>2]|0)+(f[j+(e<<2)>>2]|0);e=e+1|0}while(e>>>0<(f[i>>2]|0)>>>0);break}default:break j}}}else{e=f[K>>2]|0;if(!(f[e+3656>>2]|0))Ma[f[e+32>>2]&7](a,2,f[e+48>>2]|0);if(d|0?(F=f[K>>2]|0,f[F+1144>>2]|0):0){g=0;e=F;do{cf(f[e+60+(g<<2)>>2]|0,0,f[e+1136>>2]<<2|0)|0;g=g+1|0;e=f[K>>2]|0}while(g>>>0<(f[e+1144>>2]|0)>>>0)}}while(0);f[c>>2]=1;e=f[K>>2]|0;g=f[e+232>>2]|0;if(g|0)f[e+228>>2]=g;q=f[e+1144>>2]|0;l=f[z>>2]|0;f[l+8>>2]=q;f[l+12>>2]=f[e+1148>>2];i=f[e+1152>>2]|0;f[l+16>>2]=i;f[l+20>>2]=f[e+1140>>2];j=f[e+1136>>2]|0;f[l+24>>2]=j;l=e+1160|0;k=f[l>>2]|0;l=f[l+4>>2]|0;m=Se(k|0,l|0,j|0,0)|0;n=H;c=e+240|0;f[c>>2]=m;f[c+4>>2]=n;k:do if(d|0){o=e+1136|0;p=e+60|0;do if(!(f[e+3656>>2]|0)){g=e+3648|0;if(f[e+248>>2]|0){if(f[g>>2]|0){if(!(sc(e+3660|0,p,q,j,(i+7|0)>>>3)|0))break;e=f[K>>2]|0}}else f[g>>2]=0;I=Ka[f[e+24>>2]&7](a,o,p,f[e+48>>2]|0)|0;G=302}else{i=e+6168|0;g=f[i>>2]|0;i=f[i+4>>2]|0;af(e+3776|0,o|0,2384)|0;if(!((i>>>0>l>>>0|(i|0)==(l|0)&g>>>0>=k>>>0)&(i>>>0<n>>>0|(i|0)==(n|0)&g>>>0<m>>>0)))break k;i=Te(g|0,i|0,k|0,l|0)|0;j=f[K>>2]|0;f[j+3656>>2]=0;if(!i){I=Ka[f[j+24>>2]&7](a,o,p,f[j+48>>2]|0)|0;G=302;break}if(q|0){g=0;do{f[J+(g<<2)>>2]=(f[e+60+(g<<2)>>2]|0)+(i<<2);g=g+1|0}while((g|0)!=(q|0))}I=j+3776|0;f[I>>2]=(f[I>>2]|0)-i;I=j+3800|0;G=I;G=Se(f[G>>2]|0,f[G+4>>2]|0,i|0,0)|0;f[I>>2]=G;f[I+4>>2]=H;I=f[K>>2]|0;I=Ka[f[I+24>>2]&7](a,I+3776|0,J,f[I+48>>2]|0)|0;G=302}while(0);if((G|0)==302?(I|0)==0:0)break;f[f[z>>2]>>2]=7;a=0;u=L;return a|0}while(0);f[f[z>>2]>>2]=2;a=1;u=L;return a|0}while(0);a=0;u=L;return a|0}function Nc(a,b,c,d,e,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;r=u;u=u+16|0;o=r+4|0;p=r;q=1<<c;h=(c|0)==0;m=a+4|0;n=f[(f[m>>2]|0)+1136>>2]|0;n=h?n-b|0:n>>>c;l=(g|0)!=0;k=l?f[275]|0:f[274]|0;l=l?f[273]|0:f[272]|0;if(!(ec(d,c>>>0>6?c:6)|0)){f[f[a>>2]>>2]=8;q=0;u=r;return q|0}i=d+4|0;j=f[275]|0;if(h){h=(n|0)==0;c=0;g=0;a:while(1){if(!(fb(f[(f[m>>2]|0)+56>>2]|0,o,k)|0)){g=0;a=26;break}f[(f[d>>2]|0)+(c<<2)>>2]=f[o>>2];if((f[o>>2]|0)>>>0>=l>>>0){if(!(fb(f[(f[m>>2]|0)+56>>2]|0,o,j)|0)){g=0;a=26;break}f[(f[i>>2]|0)+(c<<2)>>2]=f[o>>2];if(!h){a=0;do{if(!(hb(f[(f[m>>2]|0)+56>>2]|0,p,f[o>>2]|0)|0)){g=0;a=26;break a}f[e+(g<<2)>>2]=f[p>>2];a=a+1|0;g=g+1|0}while(a>>>0<n>>>0)}}else{f[(f[i>>2]|0)+(c<<2)>>2]=0;if(!(ob(f[(f[m>>2]|0)+56>>2]|0,e+(g<<2)|0,n,f[o>>2]|0)|0)){g=0;a=26;break}g=n+g|0}c=c+1|0;if(c>>>0>=q>>>0){g=1;a=26;break}}if((a|0)==26){u=r;return g|0}}else{c=0;g=0;b:while(1){if(!(fb(f[(f[m>>2]|0)+56>>2]|0,o,k)|0)){g=0;a=26;break}f[(f[d>>2]|0)+(c<<2)>>2]=f[o>>2];if((f[o>>2]|0)>>>0>=l>>>0){if(!(fb(f[(f[m>>2]|0)+56>>2]|0,o,j)|0)){g=0;a=26;break}f[(f[i>>2]|0)+(c<<2)>>2]=f[o>>2];a=(c|0)==0?b:0;if(a>>>0<n>>>0)do{if(!(hb(f[(f[m>>2]|0)+56>>2]|0,p,f[o>>2]|0)|0)){g=0;a=26;break b}f[e+(g<<2)>>2]=f[p>>2];a=a+1|0;g=g+1|0}while(a>>>0<n>>>0)}else{f[(f[i>>2]|0)+(c<<2)>>2]=0;a=n-((c|0)==0?b:0)|0;if(!(ob(f[(f[m>>2]|0)+56>>2]|0,e+(g<<2)|0,a,f[o>>2]|0)|0)){g=0;a=26;break}g=a+g|0}c=c+1|0;if(c>>>0>=q>>>0){g=1;a=26;break}}if((a|0)==26){u=r;return g|0}}return 0}function Oc(a){a=a|0;var b=0;a:while(1)switch(f[f[a>>2]>>2]|0){case 7:case 4:case 3:case 2:{a=1;break a}case 0:{if(!(Jc(a)|0)){a=0;break a}break}case 1:{if(!(Kc(a)|0)){a=0;break a}break}default:{a=0;b=7;break a}}if((b|0)==7)return a|0;b=a;return b|0}function Pc(a){a=a|0;var b=0,c=0,d=0;d=u;u=u+16|0;b=d;a:while(1)switch(f[f[a>>2]>>2]|0){case 7:case 4:{a=1;break a}case 0:{if(!(Jc(a)|0)){a=0;break a}break}case 1:{if(!(Kc(a)|0)){a=0;break a}break}case 2:{if(!(Lc(a)|0)){a=1;break a}break}case 3:{if(!(Mc(a,b,1)|0)){a=0;break a}break}default:{a=0;c=9;break a}}if((c|0)==9){u=d;return a|0}c=a;u=d;return c|0}function Qc(){var a=0,b=0,c=0,d=0,e=0,g=0;d=Nd(1,8)|0;if(!d){g=0;return g|0}a=Nd(1,632)|0;f[d>>2]=a;if(!a){Md(d);g=0;return g|0}c=Nd(1,11880)|0;g=d+4|0;f[g>>2]=c;if(!c){Md(a);Md(d);g=0;return g|0}c=rb()|0;a=f[g>>2]|0;f[a+6856>>2]=c;if(!c){Md(a);Md(f[d>>2]|0);Md(d);g=0;return g|0}f[a+7320>>2]=0;a=f[d>>2]|0;f[a+4>>2]=0;f[a+8>>2]=1;f[a+12>>2]=1;f[a+16>>2]=0;f[a+20>>2]=0;f[a+24>>2]=2;f[a+28>>2]=16;f[a+32>>2]=44100;f[a+36>>2]=0;f[a+40>>2]=1;f[a+44>>2]=13;n[a+48>>2]=.5;a=f[d>>2]|0;c=a+556|0;b=a+592|0;f[c>>2]=0;f[c+4>>2]=0;f[c+8>>2]=0;f[c+12>>2]=0;f[c+16>>2]=0;f[c+20>>2]=0;f[c+24>>2]=0;f[c+28>>2]=0;f[b>>2]=0;f[b+4>>2]=0;f[b+8>>2]=0;f[b+12>>2]=0;b=f[g>>2]|0;f[b+7048>>2]=0;b=b+7272|0;c=b+44|0;do{f[b>>2]=0;b=b+4|0}while((b|0)<(c|0));if((f[a>>2]|0)==1?(f[a+16>>2]=1,f[a+20>>2]=0,Rc(d,3181)|0,e=f[d>>2]|0,(f[e>>2]|0)==1):0){f[e+556>>2]=8;f[e+560>>2]=0;f[e+564>>2]=0;f[e+568>>2]=0;f[e+576>>2]=0;f[e+580>>2]=5}e=f[g>>2]|0;f[e+11872>>2]=0;f[e+6176>>2]=e+336;e=f[g>>2]|0;f[e+6180>>2]=e+628;e=f[g>>2]|0;f[e+6184>>2]=e+920;e=f[g>>2]|0;f[e+6188>>2]=e+1212;e=f[g>>2]|0;f[e+6192>>2]=e+1504;e=f[g>>2]|0;f[e+6196>>2]=e+1796;e=f[g>>2]|0;f[e+6200>>2]=e+2088;e=f[g>>2]|0;f[e+6204>>2]=e+2380;e=f[g>>2]|0;f[e+6208>>2]=e+2672;e=f[g>>2]|0;f[e+6212>>2]=e+2964;e=f[g>>2]|0;f[e+6216>>2]=e+3256;e=f[g>>2]|0;f[e+6220>>2]=e+3548;e=f[g>>2]|0;f[e+6224>>2]=e+3840;e=f[g>>2]|0;f[e+6228>>2]=e+4132;e=f[g>>2]|0;f[e+6232>>2]=e+4424;e=f[g>>2]|0;f[e+6236>>2]=e+4716;e=f[g>>2]|0;f[e+6240>>2]=e+5008;e=f[g>>2]|0;f[e+6244>>2]=e+5300;e=f[g>>2]|0;f[e+6248>>2]=e+5592;e=f[g>>2]|0;f[e+6252>>2]=e+5884;e=f[g>>2]|0;f[e+6640>>2]=e+6256;e=f[g>>2]|0;f[e+6644>>2]=e+6268;e=f[g>>2]|0;f[e+6648>>2]=e+6280;e=f[g>>2]|0;f[e+6652>>2]=e+6292;e=f[g>>2]|0;f[e+6656>>2]=e+6304;e=f[g>>2]|0;f[e+6660>>2]=e+6316;e=f[g>>2]|0;f[e+6664>>2]=e+6328;e=f[g>>2]|0;f[e+6668>>2]=e+6340;e=f[g>>2]|0;f[e+6672>>2]=e+6352;e=f[g>>2]|0;f[e+6676>>2]=e+6364;e=f[g>>2]|0;f[e+6680>>2]=e+6376;e=f[g>>2]|0;f[e+6684>>2]=e+6388;e=f[g>>2]|0;f[e+6688>>2]=e+6400;e=f[g>>2]|0;f[e+6692>>2]=e+6412;e=f[g>>2]|0;f[e+6696>>2]=e+6424;e=f[g>>2]|0;f[e+6700>>2]=e+6436;e=f[g>>2]|0;f[e+6704>>2]=e+6448;e=f[g>>2]|0;f[e+6708>>2]=e+6460;e=f[g>>2]|0;f[e+6712>>2]=e+6472;e=f[g>>2]|0;f[e+6716>>2]=e+6484;cc((f[g>>2]|0)+6256|0);cc((f[g>>2]|0)+6268|0);cc((f[g>>2]|0)+6280|0);cc((f[g>>2]|0)+6292|0);cc((f[g>>2]|0)+6304|0);cc((f[g>>2]|0)+6316|0);cc((f[g>>2]|0)+6328|0);cc((f[g>>2]|0)+6340|0);cc((f[g>>2]|0)+6352|0);cc((f[g>>2]|0)+6364|0);cc((f[g>>2]|0)+6376|0);cc((f[g>>2]|0)+6388|0);cc((f[g>>2]|0)+6400|0);cc((f[g>>2]|0)+6412|0);cc((f[g>>2]|0)+6424|0);cc((f[g>>2]|0)+6436|0);cc((f[g>>2]|0)+6448|0);cc((f[g>>2]|0)+6460|0);cc((f[g>>2]|0)+6472|0);cc((f[g>>2]|0)+6484|0);cc((f[g>>2]|0)+11748|0);cc((f[g>>2]|0)+11760|0);f[f[d>>2]>>2]=1;g=d;return g|0}function Rc(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0,g=0,h=0,i=0,j=0.0,k=0.0,l=0,m=0,o=0,p=0;c=f[a>>2]|0;if((f[c>>2]|0)!=1){o=0;return o|0}f[c+40>>2]=0;g=0;while(1){l=De(b,59)|0;m=(l|0)!=0;if(m)h=l-b|0;else h=Ce(b)|0;e=(h|0)==8;a:do if(e){if(!(fe(3193,b,8)|0)){f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=0;break}if(!(fe(3216,b,8)|0)){f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=2}else{i=0;o=21}}else switch(h|0){case 13:{if(fe(3202,b,13)|0){i=1;o=21;break a}f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=1;break a}case 26:{if(fe(3225,b,26)|0){i=0;o=21;break a}f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=3;break a}case 6:{if(fe(3252,b,6)|0)break a;f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=4;break a}case 7:{if(!(fe(3259,b,7)|0)){f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=5;break a}if(!(fe(3274,b,7)|0)){f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=7;break a}if(fe(3301,b,h)|0)break a;f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=10;break a}default:{if(h>>>0>7){i=0;o=21;break a}switch(h|0){case 4:{if(fe(3282,b,4)|0)break a;f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=8;break a}case 5:{if(fe(3366,b,5)|0)break a;f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=16;break a}default:break a}}}while(0);b:do if((o|0)==21){o=0;if(!(fe(3267,b,6)|0)){d=+Ne(b+6|0,0);if(!(d>0.0)|!(d<=.5))break;h=f[a>>2]|0;n[h+44+(f[h+40>>2]<<4)+4>>2]=d;h=f[a>>2]|0;g=h+40|0;i=f[g>>2]|0;f[g>>2]=i+1;f[h+44+(i<<4)>>2]=6;break}if(i?(fe(3287,b,h)|0)==0:0){f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=9;break}if((h|0)==9?(fe(3309,b,9)|0)==0:0){f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=11;break}if(e?(fe(3319,b,8)|0)==0:0){f[c+40>>2]=g+1;f[c+44+(g<<4)>>2]=12;break}if(!(fe(3328,b,6)|0)){d=+Ne(b+6|0,0);if(!(d>=0.0)|!(d<=1.0))break;h=f[a>>2]|0;n[h+44+(f[h+40>>2]<<4)+4>>2]=d;h=f[a>>2]|0;g=h+40|0;i=f[g>>2]|0;f[g>>2]=i+1;f[h+44+(i<<4)>>2]=13;break}if(h>>>0>15){if(!(fe(3335,b,14)|0)){h=~~+Ne(b+14|0,0);e=De(b,47)|0;c=(e|0)!=0;e=e+1|0;if(c)if(+Ne(e,0)<.9900000095367432)d=+Ne(e,0);else d=.9900000095367432;else d=.10000000149011612;k=1.0/(1.0-d)+-1.0;c=De(c?e:b,47)|0;if(!c)d=.20000000298023224;else d=+Ne(c+1|0,0);c=f[a>>2]|0;e=f[c+40>>2]|0;if((h|0)<2){n[c+44+(e<<4)+4>>2]=d;h=f[a>>2]|0;g=h+40|0;i=f[g>>2]|0;f[g>>2]=i+1;f[h+44+(i<<4)>>2]=13;break}if((e+h|0)>>>0>=32)break;j=k+ +(h|0);g=0;while(1){n[c+44+(e<<4)+4>>2]=d;p=f[a>>2]|0;n[p+44+(f[p+40>>2]<<4)+8>>2]=+(g|0)/j;g=g+1|0;p=f[a>>2]|0;n[p+44+(f[p+40>>2]<<4)+12>>2]=(k+ +(g|0))/j;c=f[a>>2]|0;p=c+40|0;i=f[p>>2]|0;e=i+1|0;f[p>>2]=e;f[c+44+(i<<4)>>2]=14;if((g|0)==(h|0))break b}}if(h>>>0>16?(fe(3350,b,15)|0)==0:0){h=~~+Ne(b+15|0,0);e=De(b,47)|0;c=(e|0)!=0;e=e+1|0;if(c)if(+Ne(e,0)<.9900000095367432)d=+Ne(e,0);else d=.9900000095367432;else d=.20000000298023224;k=1.0/(1.0-d)+-1.0;c=De(c?e:b,47)|0;if(!c)d=.20000000298023224;else d=+Ne(c+1|0,0);c=f[a>>2]|0;e=f[c+40>>2]|0;if((h|0)<2){n[c+44+(e<<4)+4>>2]=d;i=f[a>>2]|0;h=i+40|0;p=f[h>>2]|0;f[h>>2]=p+1;f[i+44+(p<<4)>>2]=13;break}if((e+h|0)>>>0<32){j=k+ +(h|0);g=0;do{n[c+44+(e<<4)+4>>2]=d;i=f[a>>2]|0;n[i+44+(f[i+40>>2]<<4)+8>>2]=+(g|0)/j;g=g+1|0;i=f[a>>2]|0;n[i+44+(f[i+40>>2]<<4)+12>>2]=(k+ +(g|0))/j;c=f[a>>2]|0;i=c+40|0;p=f[i>>2]|0;e=p+1|0;f[i>>2]=e;f[c+44+(p<<4)>>2]=15}while((g|0)!=(h|0))}}}}while(0);c=f[a>>2]|0;g=f[c+40>>2]|0;e=(g|0)==32;if(!(m&(e^1)))break;b=e?b:l+1|0}if(g|0){p=1;return p|0}f[c+40>>2]=1;f[c+44>>2]=13;n[c+48>>2]=.5;p=1;return p|0}function Sc(a){a=a|0;var b=0,c=0,d=0;if(!a)return;d=a+4|0;f[(f[d>>2]|0)+11872>>2]=1;Tc(a)|0;b=f[d>>2]|0;c=f[b+11776>>2]|0;if(c){zc(c);b=f[d>>2]|0}dc(b+6256|0);dc((f[d>>2]|0)+6268|0);dc((f[d>>2]|0)+6280|0);dc((f[d>>2]|0)+6292|0);dc((f[d>>2]|0)+6304|0);dc((f[d>>2]|0)+6316|0);dc((f[d>>2]|0)+6328|0);dc((f[d>>2]|0)+6340|0);dc((f[d>>2]|0)+6352|0);dc((f[d>>2]|0)+6364|0);dc((f[d>>2]|0)+6376|0);dc((f[d>>2]|0)+6388|0);dc((f[d>>2]|0)+6400|0);dc((f[d>>2]|0)+6412|0);dc((f[d>>2]|0)+6424|0);dc((f[d>>2]|0)+6436|0);dc((f[d>>2]|0)+6448|0);dc((f[d>>2]|0)+6460|0);dc((f[d>>2]|0)+6472|0);dc((f[d>>2]|0)+6484|0);dc((f[d>>2]|0)+11748|0);dc((f[d>>2]|0)+11760|0);sb(f[(f[d>>2]|0)+6856>>2]|0);Md(f[d>>2]|0);Md(f[a>>2]|0);Md(a);return}function Tc(a){a=a|0;var c=0,d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;D=u;u=u+32|0;A=D;e=f[a>>2]|0;c=f[e>>2]|0;switch(c|0){case 0:{g=2;break}case 1:{a=c;u=D;return a|0}default:{d=0;c=e}}if((g|0)==2){c=f[a+4>>2]|0;if((f[c+11872>>2]|0)==0?(d=f[c+7052>>2]|0,(d|0)!=0):0){C=e+36|0;c=(f[C>>2]|0)!=(d|0)&1;f[C>>2]=d;d=(Uc(a,c)|0)==0&1;c=f[a>>2]|0}else{d=0;c=e}}C=a+4|0;if(f[c+12>>2]|0){z=f[C>>2]|0;qc(z+6928|0,z+7060|0)}g=f[C>>2]|0;if(!(f[g+11872>>2]|0)){c=f[a>>2]|0;if(!(f[c>>2]|0)){e=f[g+7292>>2]|0;if(e){i=g+6920|0;j=f[i>>2]|0;i=f[i+4>>2]|0;k=f[g+6896>>2]|0;m=f[g+6900>>2]|0;h=f[g+6912>>2]|0;o=(f[279]|0)+(f[279]|0)|0;l=o+(f[267]|0)+(f[267]|0)+(f[259]|0)+(f[270]|0)+(f[275]|0)|0;z=c+608|0;z=Se(f[z>>2]|0,f[z+4>>2]|0,((l+(f[260]|0)|0)>>>3)+4|0,0)|0;a:do switch(Ka[e&7](a,z,H,f[g+7312>>2]|0)|0){case 0:{z=f[C>>2]|0;if(La[f[z+7300>>2]&7](a,g+6928|0,16,0,0,f[z+7312>>2]|0)|0){f[f[a>>2]>>2]=5;break a}b[A>>0]=(h<<4)+240|i&15;x=Ze(j|0,i|0,24)|0;z=A+1|0;b[z>>0]=x;x=Ze(j|0,i|0,16)|0;y=A+2|0;b[y>>0]=x;x=Ze(j|0,i|0,8)|0;w=A+3|0;b[w>>0]=x;x=A+4|0;b[x>>0]=j;v=f[C>>2]|0;s=f[v+7292>>2]|0;t=(f[a>>2]|0)+608|0;t=Se(f[t>>2]|0,f[t+4>>2]|0,((l+-4|0)>>>3)+4|0,0)|0;switch(Ka[s&7](a,t,H,f[v+7312>>2]|0)|0){case 0:break;case 1:{f[f[a>>2]>>2]=5;break a}default:break a}v=f[C>>2]|0;if(La[f[v+7300>>2]&7](a,A,5,0,0,f[v+7312>>2]|0)|0){f[f[a>>2]>>2]=5;break a}b[A>>0]=k>>>16;b[z>>0]=k>>>8;b[y>>0]=k;b[w>>0]=m>>>16;b[x>>0]=m>>>8;v=A+5|0;b[v>>0]=m;t=f[C>>2]|0;r=f[t+7292>>2]|0;s=(f[a>>2]|0)+608|0;s=Se(f[s>>2]|0,f[s+4>>2]|0,(o>>>3)+4|0,0)|0;switch(Ka[r&7](a,s,H,f[t+7312>>2]|0)|0){case 0:break;case 1:{f[f[a>>2]>>2]=5;break a}default:break a}t=f[C>>2]|0;if(La[f[t+7300>>2]&7](a,A,6,0,0,f[t+7312>>2]|0)|0){f[f[a>>2]>>2]=5;break a}c=f[(f[C>>2]|0)+7048>>2]|0;if((c|0?f[c>>2]|0:0)?(t=(f[a>>2]|0)+616|0,!((f[t>>2]|0)==0&(f[t+4>>2]|0)==0)):0){Xb(c)|0;t=f[C>>2]|0;r=f[t+7292>>2]|0;s=(f[a>>2]|0)+616|0;s=Se(f[s>>2]|0,f[s+4>>2]|0,4,0)|0;switch(Ka[r&7](a,s,H,f[t+7312>>2]|0)|0){case 0:break;case 1:{f[f[a>>2]>>2]=5;break a}default:break a}e=f[C>>2]|0;c=f[e+7048>>2]|0;if(f[c>>2]|0){h=A+7|0;i=A+6|0;j=A+15|0;k=A+14|0;l=A+13|0;m=A+12|0;o=A+11|0;p=A+10|0;q=A+9|0;r=A+8|0;s=A+17|0;t=A+16|0;g=0;while(1){c=c+4|0;E=(f[c>>2]|0)+(g*24|0)|0;F=f[E>>2]|0;E=f[E+4>>2]|0;b[h>>0]=F;G=Ze(F|0,E|0,8)|0;b[i>>0]=G;G=Ze(F|0,E|0,16)|0;b[v>>0]=G;G=Ze(F|0,E|0,24)|0;b[x>>0]=G;b[w>>0]=E;G=Ze(F|0,E|0,40)|0;b[y>>0]=G;G=Ze(F|0,E|0,48)|0;b[z>>0]=G;E=Ze(F|0,E|0,56)|0;b[A>>0]=E;E=(f[c>>2]|0)+(g*24|0)+8|0;F=f[E>>2]|0;E=f[E+4>>2]|0;b[j>>0]=F;G=Ze(F|0,E|0,8)|0;b[k>>0]=G;G=Ze(F|0,E|0,16)|0;b[l>>0]=G;G=Ze(F|0,E|0,24)|0;b[m>>0]=G;b[o>>0]=E;G=Ze(F|0,E|0,40)|0;b[p>>0]=G;G=Ze(F|0,E|0,48)|0;b[q>>0]=G;E=Ze(F|0,E|0,56)|0;b[r>>0]=E;c=f[(f[c>>2]|0)+(g*24|0)+16>>2]|0;b[s>>0]=c;b[t>>0]=c>>>8;g=g+1|0;if(La[f[e+7300>>2]&7](a,A,18,0,0,f[e+7312>>2]|0)|0)break;e=f[C>>2]|0;c=f[e+7048>>2]|0;if(g>>>0>=(f[c>>2]|0)>>>0)break a}f[f[a>>2]>>2]=5}}break}case 1:{f[f[a>>2]>>2]=5;break}default:{}}while(0);c=f[a>>2]|0;d=(f[c>>2]|0)==0?d:1;g=f[C>>2]|0}e=f[g+7304>>2]|0;if(e){Ma[e&7](a,g+6872|0,f[g+7312>>2]|0);c=f[a>>2]|0}}if(((f[c+4>>2]|0)!=0?(B=f[(f[C>>2]|0)+11776>>2]|0,(B|0)!=0):0)?(Ac(B)|0)==0:0)if(!d){f[f[a>>2]>>2]=4;h=1}else h=1;else h=d}else h=d;c=(f[C>>2]|0)+7320|0;d=f[c>>2]|0;if(d|0){if((d|0)!=(f[412]|0)){Ke(d)|0;c=(f[C>>2]|0)+7320|0}f[c>>2]=0}c=f[a>>2]|0;d=f[c+600>>2]|0;if(d){Md(d);c=f[a>>2]|0;f[c+600>>2]=0;f[c+604>>2]=0}if(f[c+24>>2]|0){e=0;do{c=f[C>>2]|0;d=f[c+7352+(e<<2)>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7352+(e<<2)>>2]=0;c=f[C>>2]|0}c=f[c+7392+(e<<2)>>2]|0;if(c|0){Md(c);f[(f[C>>2]|0)+7392+(e<<2)>>2]=0}e=e+1|0}while(e>>>0<(f[(f[a>>2]|0)+24>>2]|0)>>>0)}c=f[C>>2]|0;d=f[c+7384>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7384>>2]=0;c=f[C>>2]|0}d=f[c+7424>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7424>>2]=0;c=f[C>>2]|0}d=f[c+7388>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7388>>2]=0;c=f[C>>2]|0}d=f[c+7428>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7428>>2]=0;c=f[C>>2]|0}e=f[a>>2]|0;if(f[e+40>>2]|0){g=0;do{d=f[c+7432+(g<<2)>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7432+(g<<2)>>2]=0;e=f[a>>2]|0;c=f[C>>2]|0}g=g+1|0}while(g>>>0<(f[e+40>>2]|0)>>>0)}d=f[c+7560>>2]|0;if(!d)d=e;else{Md(d);c=f[C>>2]|0;f[c+7560>>2]=0;d=f[a>>2]|0}if(f[d+24>>2]|0){e=0;do{d=f[c+7564+(e<<3)>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7564+(e<<3)>>2]=0;c=f[C>>2]|0}d=f[c+7564+(e<<3)+4>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7564+(e<<3)+4>>2]=0;c=f[C>>2]|0}e=e+1|0}while(e>>>0<(f[(f[a>>2]|0)+24>>2]|0)>>>0)}d=f[c+7628>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7628>>2]=0;c=f[C>>2]|0}d=f[c+7632>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7632>>2]=0;c=f[C>>2]|0}d=f[c+7636>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7636>>2]=0;c=f[C>>2]|0}d=f[c+7640>>2]|0;if(d){Md(d);f[(f[C>>2]|0)+7640>>2]=0;c=f[C>>2]|0}d=f[c+7644>>2]|0;if(d){Md(d);c=f[C>>2]|0;f[c+7644>>2]=0}d=f[c+7648>>2]|0;if(d){Md(d);c=f[C>>2]|0;f[c+7648>>2]=0}d=f[a>>2]|0;do if(f[d+4>>2]|0){if(!(f[d+24>>2]|0))break;g=0;do{e=f[c+11788+(g<<2)>>2]|0;if(e){Md(e);f[(f[C>>2]|0)+11788+(g<<2)>>2]=0;c=f[C>>2]|0;d=f[a>>2]|0}g=g+1|0}while(g>>>0<(f[d+24>>2]|0)>>>0)}while(0);tb(f[c+6856>>2]|0);c=f[a>>2]|0;f[c+4>>2]=0;f[c+8>>2]=1;f[c+12>>2]=1;f[c+16>>2]=0;f[c+20>>2]=0;f[c+24>>2]=2;f[c+28>>2]=16;f[c+32>>2]=44100;f[c+36>>2]=0;f[c+40>>2]=1;f[c+44>>2]=13;n[c+48>>2]=.5;c=f[a>>2]|0;d=c+556|0;c=c+592|0;f[d>>2]=0;f[d+4>>2]=0;f[d+8>>2]=0;f[d+12>>2]=0;f[d+16>>2]=0;f[d+20>>2]=0;f[d+24>>2]=0;f[d+28>>2]=0;f[c>>2]=0;f[c+4>>2]=0;f[c+8>>2]=0;f[c+12>>2]=0;c=f[C>>2]|0;f[c+7048>>2]=0;c=c+7272|0;d=c+44|0;do{f[c>>2]=0;c=c+4|0}while((c|0)<(d|0));c=f[a>>2]|0;do if((f[c>>2]|0)==1){f[c+16>>2]=1;f[c+20>>2]=0;Rc(a,3181)|0;c=f[a>>2]|0;if((f[c>>2]|0)!=1)break;f[c+556>>2]=8;f[c+560>>2]=0;f[c+564>>2]=0;f[c+568>>2]=0;f[c+576>>2]=0;f[c+580>>2]=5}while(0);d=(h|0)!=0;if(!d)f[c>>2]=1;G=(d^1)&1;u=D;return G|0}function Uc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0,i=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,w=0;w=u;u=u+48|0;s=w;t=w+40|0;c=f[a>>2]|0;if(f[c+12>>2]|0){r=f[a+4>>2]|0;r=(sc(r+7060|0,r+4|0,f[c+24>>2]|0,f[c+36>>2]|0,((f[c+28>>2]|0)+7|0)>>>3)|0)==0;c=f[a>>2]|0;if(r){f[c>>2]=8;v=0;u=w;return v|0}}d=f[c+576>>2]|0;if(!b){r=bc(f[c+36>>2]|0)|0;c=f[a>>2]|0;p=f[c+580>>2]|0;p=r>>>0<p>>>0?r:p}else p=0;o=d>>>0<p>>>0?d:p;k=c+36|0;d=f[k>>2]|0;f[s>>2]=d;f[s+4>>2]=f[c+32>>2];l=c+24|0;f[s+8>>2]=f[l>>2];q=s+12|0;f[q>>2]=0;m=c+28|0;f[s+16>>2]=f[m>>2];f[s+20>>2]=0;r=a+4|0;n=f[r>>2]|0;f[s+24>>2]=f[n+7056>>2];if(f[c+16>>2]|0)if((f[c+20>>2]|0)!=0?(f[n+6864>>2]|0)!=0:0){i=(f[n+6868>>2]|0)==0;c=(i^1)&1;if(i)v=10;else h=0}else{c=1;v=10}else{c=0;v=10}a:do if((v|0)==10)if(!(f[l>>2]|0))h=1;else{b=0;i=d;while(1){h=f[n+4+(b<<2)>>2]|0;if(i){d=0;e=0;do{e=f[h+(d<<2)>>2]|e;d=d+1|0;g=(e&1|0)==0}while(d>>>0<i>>>0&g);if((e|0)!=0&g){d=0;while(1){d=d+1|0;if(e&2|0)break;else e=e>>1}if(d){e=0;do{g=h+(e<<2)|0;f[g>>2]=f[g>>2]>>d;e=e+1|0}while((e|0)!=(i|0))}else d=0}else d=0}else d=0;h=f[m>>2]|0;i=d>>>0>h>>>0?h:d;f[n+336+(b*584|0)+580>>2]=i;f[n+336+(b*584|0)+288>>2]=i;f[n+216+(b<<2)>>2]=h-i;b=b+1|0;if(b>>>0>=(f[l>>2]|0)>>>0){h=1;break a}i=f[k>>2]|0}}while(0);i=(c|0)!=0;if(i){e=f[n+36>>2]|0;g=f[k>>2]|0;if(g){c=0;b=0;do{b=f[e+(c<<2)>>2]|b;c=c+1|0;d=(b&1|0)==0}while(c>>>0<g>>>0&d);if((b|0)!=0&d){c=0;while(1){c=c+1|0;if(b&2|0)break;else b=b>>1}if(c){b=0;do{d=e+(b<<2)|0;f[d>>2]=f[d>>2]>>c;b=b+1|0}while((b|0)!=(g|0))}else c=0}else c=0}else c=0;e=f[m>>2]|0;g=c>>>0>e>>>0?e:c;f[n+5588>>2]=g;f[n+5296>>2]=g;f[n+248>>2]=e-g;g=f[n+40>>2]|0;e=f[k>>2]|0;if(e){c=0;b=0;do{b=f[g+(c<<2)>>2]|b;c=c+1|0;d=(b&1|0)==0}while(c>>>0<e>>>0&d);if((b|0)!=0&d){c=0;while(1){c=c+1|0;if(b&2|0)break;else b=b>>1}if(c){b=0;do{k=g+(b<<2)|0;f[k>>2]=f[k>>2]>>c;b=b+1|0}while((b|0)!=(e|0))}else c=0}else c=0}else c=0;k=f[m>>2]|0;m=c>>>0>k>>>0?k:c;f[n+6172>>2]=m;f[n+5880>>2]=m;f[n+252>>2]=k+1-m}if(h?(f[l>>2]|0)!=0:0){b=0;c=n;do{Vc(a,o,p,s,f[c+216+(b<<2)>>2]|0,f[c+4+(b<<2)>>2]|0,c+6176+(b<<3)|0,c+6640+(b<<3)|0,c+256+(b<<3)|0,c+6768+(b<<2)|0,c+6808+(b<<2)|0);b=b+1|0;c=f[r>>2]|0}while(b>>>0<(f[(f[a>>2]|0)+24>>2]|0)>>>0)}else c=n;b:do if(i){Vc(a,o,p,s,f[c+248>>2]|0,f[c+36>>2]|0,c+6240|0,c+6704|0,c+320|0,c+6800|0,c+6840|0);b=f[r>>2]|0;Vc(a,o,p,s,f[b+252>>2]|0,f[b+40>>2]|0,b+6248|0,b+6712|0,b+328|0,b+6804|0,b+6844|0);b=f[r>>2]|0;if((f[(f[a>>2]|0)+20>>2]|0)!=0?(f[b+6864>>2]|0)!=0:0)c=(f[b+6868>>2]|0)==0?0:3;else{p=f[b+6808>>2]|0;n=f[b+6812>>2]|0;o=n+p|0;m=f[b+6844>>2]|0;p=m+p|0;n=m+n|0;c=p>>>0<o>>>0;o=c?p:o;p=n>>>0<o>>>0;c=((f[b+6840>>2]|0)+m|0)>>>0<(p?n:o)>>>0?3:p?2:c&1}f[q>>2]=c;if(!(od(s,f[b+6856>>2]|0)|0)){f[f[a>>2]>>2]=7;v=71;break}switch(c|0){case 0:{g=f[r>>2]|0;c=g+920+((f[g+6772>>2]|0)*292|0)|0;b=g+336+((f[g+6768>>2]|0)*292|0)|0;d=f[g+220>>2]|0;e=f[g+216>>2]|0;break}case 1:{g=f[r>>2]|0;c=g+5592+((f[g+6804>>2]|0)*292|0)|0;b=g+336+((f[g+6768>>2]|0)*292|0)|0;d=f[g+252>>2]|0;e=f[g+216>>2]|0;break}case 2:{g=f[r>>2]|0;c=g+920+((f[g+6772>>2]|0)*292|0)|0;b=g+5592+((f[g+6804>>2]|0)*292|0)|0;d=f[g+220>>2]|0;e=f[g+252>>2]|0;break}case 3:{g=f[r>>2]|0;c=g+5592+((f[g+6804>>2]|0)*292|0)|0;b=g+5008+((f[g+6800>>2]|0)*292|0)|0;d=f[g+252>>2]|0;e=f[g+248>>2]|0;break}default:{c=0;b=0;d=0;e=0;g=f[r>>2]|0}}if((Wc(a,f[s>>2]|0,e,b,f[g+6856>>2]|0)|0)!=0?(Wc(a,f[s>>2]|0,d,c,f[(f[r>>2]|0)+6856>>2]|0)|0)!=0:0)c=f[a>>2]|0;else v=71}else{p=(od(s,f[c+6856>>2]|0)|0)==0;c=f[a>>2]|0;if(p){f[c>>2]=7;v=71;break}if(f[c+24>>2]|0){b=0;do{p=f[r>>2]|0;if(!(Wc(a,f[s>>2]|0,f[p+216+(b<<2)>>2]|0,p+336+(b*584|0)+((f[p+6768+(b<<2)>>2]|0)*292|0)|0,f[p+6856>>2]|0)|0)){v=71;break b}b=b+1|0;c=f[a>>2]|0}while(b>>>0<(f[c+24>>2]|0)>>>0)}}while(0);if((v|0)==71){v=0;u=w;return v|0}if(f[c+20>>2]|0){p=f[r>>2]|0;v=p+6864|0;s=(f[v>>2]|0)+1|0;f[v>>2]=s>>>0<(f[p+6860>>2]|0)>>>0?s:0}v=f[r>>2]|0;f[v+6868>>2]=f[q>>2];if(!(Lb(f[v+6856>>2]|0)|0)){f[f[a>>2]>>2]=8;v=0;u=w;return v|0}if(wb(f[(f[r>>2]|0)+6856>>2]|0,t)|0?Bb(f[(f[r>>2]|0)+6856>>2]|0,j[t>>1]|0,f[279]|0)|0:0){if(!(Xc(a,f[(f[a>>2]|0)+36>>2]|0)|0)){v=0;u=w;return v|0}v=f[r>>2]|0;f[v+7052>>2]=0;t=v+7056|0;f[t>>2]=(f[t>>2]|0)+1;v=v+6920|0;t=v;t=Se(f[t>>2]|0,f[t+4>>2]|0,f[(f[a>>2]|0)+36>>2]|0,0)|0;f[v>>2]=t;f[v+4>>2]=H;v=1;u=w;return v|0}f[f[a>>2]>>2]=8;v=0;u=w;return v|0}function Vc(a,b,c,d,e,g,h,i,j,k,l){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;var m=0,o=0,q=0,r=0,s=0,t=0,v=0.0,w=0,x=0,y=0.0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,X=0,Y=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0;ea=u;u=u+544|0;Y=ea+416|0;_=ea+412|0;E=ea+392|0;$=ea+260|0;aa=ea;ba=ea+256|0;ca=(f[(f[a>>2]|0)+28>>2]|0)>>>0>16?f[273]|0:f[272]|0;da=a+4|0;o=f[d>>2]|0;if(!(o>>>0>3?(f[(f[da>>2]|0)+7280>>2]|0)!=0:0)){m=f[h>>2]|0;f[m>>2]=1;f[m+4>>2]=g;m=(f[277]|0)+(W(o,e)|0)+(f[276]|0)+(f[277]|0)+(f[m+288>>2]|0)|0;o=f[d>>2]|0;if(o>>>0>3)q=3;else o=0}else{m=-1;q=3}a:do if((q|0)==3){o=o+-4|0;X=(e+4+((Z(o|1|0)|0)^31)|0)>>>0<33;q=f[da>>2]|0;if(X)t=Ja[f[q+7248>>2]&15](g+16|0,o,E)|0;else t=Ja[f[q+7252>>2]&15](g+16|0,o,E)|0;s=f[da>>2]|0;b:do if((f[s+7272>>2]|0)==0?+n[E+4>>2]==0.0:0){q=f[d>>2]|0;r=f[g>>2]|0;if(q>>>0>1){o=1;do{if((r|0)!=(f[g+(o<<2)>>2]|0))break b;o=o+1|0}while(o>>>0<q>>>0)}j=f[h+4>>2]|0;f[j>>2]=0;f[j+4>>2]=r;j=(f[277]|0)+e+(f[276]|0)+(f[277]|0)+(f[j+288>>2]|0)|0;i=j>>>0<m>>>0;o=i&1;m=i?j:m;break a}while(0);q=f[a>>2]|0;if((f[s+7276>>2]|0)!=0?!((m|0)==-1&(f[q+556>>2]|0)==0):0)o=0;else{X=(f[q+568>>2]|0)==0;r=X?t:0;X=X?t:4;D=f[d>>2]|0;D=X>>>0<D>>>0?X:D+-1|0;if(r>>>0>D>>>0)o=0;else{y=+(e>>>0);z=ca+-1|0;A=f[277]|0;B=f[276]|0;C=f[277]|0;o=0;do{v=+n[E+(r<<2)>>2];if(!(v>=y)){V=v>0.0?(~~(v+.5)>>>0)+1|0:1;s=(o|0)==0&1;R=f[j+(s<<2)>>2]|0;T=f[da>>2]|0;S=f[T+6848>>2]|0;T=f[T+6852>>2]|0;X=f[(f[a>>2]|0)+572>>2]|0;t=f[h+(s<<2)>>2]|0;Q=f[i+(s<<2)>>2]|0;U=(f[d>>2]|0)-r|0;Rb(g+(r<<2)|0,U,r,R);f[t>>2]=2;w=t+4|0;f[w>>2]=0;f[t+12>>2]=Q;f[t+36>>2]=R;w=Yc(f[da>>2]|0,R,S,T,U,r,V>>>0<ca>>>0?V:z,ca,b,c,e,X,w)|0;f[t+16>>2]=r;if(r|0){x=t+20|0;q=0;do{f[x+(q<<2)>>2]=f[g+(q<<2)>>2];q=q+1|0}while((q|0)!=(r|0))}X=w+(W(r,e)|0)+A+B+C+(f[t+288>>2]|0)|0;V=X>>>0<m>>>0;o=V?s:o;m=V?X:m}r=r+1|0}while(r>>>0<=D>>>0);q=f[a>>2]|0}}r=f[q+556>>2]|0;if(((r|0)!=0?(F=f[d>>2]|0,X=r>>>0<F>>>0?r:F+-1|0,f[ba>>2]=X,(X|0)!=0):0)?(f[q+40>>2]|0)!=0:0){y=+(e>>>0);O=ca+-1|0;P=e>>>0<18;Q=32-e|0;R=e>>>0<17;S=f[277]|0;T=f[276]|0;U=f[277]|0;V=f[274]|0;X=f[275]|0;q=0;r=F;while(1){N=f[da>>2]|0;fc(g,f[N+84+(q<<2)>>2]|0,f[N+212>>2]|0,r);N=f[da>>2]|0;Na[f[N+7256>>2]&7](f[N+212>>2]|0,f[d>>2]|0,(f[ba>>2]|0)+1|0,$);c:do if(+n[$>>2]!=0.0){hc($,ba,(f[da>>2]|0)+7652|0,aa);r=f[a>>2]|0;s=f[ba>>2]|0;if(!(f[r+568>>2]|0)){if(!(f[r+564>>2]|0))r=f[r+560>>2]|0;else r=5;s=oc(aa,s,f[d>>2]|0,r+e|0)|0;f[ba>>2]=s;r=s}else r=1;t=f[d>>2]|0;if(s>>>0>=t>>>0){s=t+-1|0;f[ba>>2]=s}if(r>>>0<=s>>>0){s=t;while(1){N=r+-1|0;v=+nc(+p[aa+(N<<3)>>3],s-r|0);d:do if(!(v>=y)){M=v>0.0?(~~(v+.5)>>>0)+1|0:1;M=M>>>0<ca>>>0?M:O;w=f[a>>2]|0;do if(f[w+564>>2]|0)if(P){s=Q-((Z(r|0)|0)^31)|0;if(s>>>0<15){if(s>>>0<=5){s=5;t=5;break}}else s=15;if(s>>>0<5)break d;else t=5}else{s=15;t=5}else{t=f[w+560>>2]|0;s=t}while(0);I=(Z(r|0)|0)^31;J=Q-I|0;K=(r|0)==0;L=g+(r<<2)|0;while(1){H=(o|0)==0&1;C=f[j+(H<<2)>>2]|0;G=f[da>>2]|0;D=f[G+6848>>2]|0;E=f[G+6852>>2]|0;w=f[w+572>>2]|0;F=f[h+(H<<2)>>2]|0;x=f[i+(H<<2)>>2]|0;z=(f[d>>2]|0)-r|0;B=P?(J>>>0>t>>>0?t:J):t;if(!(ic(G+7652+(N<<7)|0,r,B,Y,_)|0)){G=B+e|0;do if((G+I|0)>>>0<33){A=f[da>>2]|0;if(R&B>>>0<17){Oa[f[A+7268>>2]&15](L,z,Y,r,f[_>>2]|0,C);break}else{Oa[f[A+7260>>2]&15](L,z,Y,r,f[_>>2]|0,C);break}}else Oa[f[(f[da>>2]|0)+7264>>2]&15](L,z,Y,r,f[_>>2]|0,C);while(0);f[F>>2]=3;A=F+4|0;f[A>>2]=0;f[F+12>>2]=x;f[F+284>>2]=C;A=Yc(f[da>>2]|0,C,D,E,z,r,M,ca,b,c,e,w,A)|0;f[F+16>>2]=r;f[F+20>>2]=B;f[F+24>>2]=f[_>>2];w=F+28|0;x=Y;z=w+128|0;do{f[w>>2]=f[x>>2];w=w+4|0;x=x+4|0}while((w|0)<(z|0));if(!K){w=0;do{f[F+156+(w<<2)>>2]=f[g+(w<<2)>>2];w=w+1|0}while((w|0)!=(r|0))}w=A+(W(G,r)|0)+S+T+U+(f[F+288>>2]|0)+V+X|0}else w=0;G=(w|0)!=0&w>>>0<m>>>0;m=G?w:m;o=G?H:o;t=t+1|0;if(t>>>0>s>>>0)break d;w=f[a>>2]|0}}while(0);r=r+1|0;if(r>>>0>(f[ba>>2]|0)>>>0)break c;s=f[d>>2]|0}}}while(0);q=q+1|0;if(q>>>0>=(f[(f[a>>2]|0)+40>>2]|0)>>>0)break a;r=f[d>>2]|0}}}while(0);if((m|0)!=-1){h=m;f[k>>2]=o;f[l>>2]=h;u=ea;return}d=f[d>>2]|0;h=f[h+(o<<2)>>2]|0;f[h>>2]=1;f[h+4>>2]=g;h=(f[277]|0)+(W(d,e)|0)+(f[276]|0)+(f[277]|0)+(f[h+288>>2]|0)|0;f[k>>2]=o;f[l>>2]=h;u=ea;return}function Wc(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;switch(f[d>>2]|0){case 0:{if(pd(d+4|0,c,f[d+288>>2]|0,e)|0){e=1;return e|0}f[f[a>>2]>>2]=7;e=0;return e|0}case 2:{if(qd(d+4|0,b-(f[d+16>>2]|0)|0,c,f[d+288>>2]|0,e)|0){e=1;return e|0}f[f[a>>2]>>2]=7;e=0;return e|0}case 3:{if(sd(d+4|0,b-(f[d+16>>2]|0)|0,c,f[d+288>>2]|0,e)|0){e=1;return e|0}f[f[a>>2]>>2]=7;e=0;return e|0}case 1:{if(td(d+4|0,b,c,f[d+288>>2]|0,e)|0){e=1;return e|0}f[f[a>>2]>>2]=7;e=0;return e|0}default:{e=1;return e|0}}return 0}function Xc(a,c){a=a|0;c=c|0;var d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,w=0,x=0,y=0,z=0,A=0;A=u;u=u+16|0;v=A;e=A+12|0;w=A+8|0;x=a+4|0;m=(xb(f[(f[x>>2]|0)+6856>>2]|0,e,w)|0)==0;d=f[a>>2]|0;if(m){f[d>>2]=8;z=0;u=A;return z|0}do if(f[d+4>>2]|0){d=f[x>>2]|0;f[d+11828>>2]=f[e>>2];f[d+11836>>2]=f[w>>2];if(!(f[d+11780>>2]|0)){f[d+11784>>2]=1;break}if(!(Ic(f[d+11776>>2]|0)|0)){vb(f[(f[x>>2]|0)+6856>>2]|0);d=f[a>>2]|0;if((f[d>>2]|0)==4){z=0;u=A;return z|0}f[d>>2]=3;z=0;u=A;return z|0}}while(0);l=f[e>>2]|0;m=f[w>>2]|0;d=v;f[d>>2]=0;f[d+4>>2]=0;d=f[x>>2]|0;e=f[d+7296>>2]|0;if((e|0)!=0?(Ja[e&15](a,v,f[d+7312>>2]|0)|0)==1:0){f[f[a>>2]>>2]=5;vb(f[(f[x>>2]|0)+6856>>2]|0)}else g=12;do if((g|0)==12){k=(c|0)==0;a:do if(k){switch(b[l>>0]&127){case 0:{e=v;g=f[e+4>>2]|0;j=(f[a>>2]|0)+608|0;f[j>>2]=f[e>>2];f[j+4>>2]=g;break a}case 3:break;default:break a}d=(f[a>>2]|0)+616|0;j=d;if((f[j>>2]|0)==0&(f[j+4>>2]|0)==0){e=v;g=f[e+4>>2]|0;j=d;f[j>>2]=f[e>>2];f[j+4>>2]=g}}while(0);j=f[x>>2]|0;d=f[j+7048>>2]|0;b:do if(((d|0?(i=f[a>>2]|0,z=i+624|0,y=f[z>>2]|0,z=f[z+4>>2]|0,!((y|0)==0&(z|0)==0)):0)?(n=f[d>>2]|0,n|0):0)?(o=f[i+36>>2]|0,q=j+7336|0,p=f[q>>2]|0,q=f[q+4>>2]|0,r=Se(o|0,0,-1,-1)|0,r=Se(r|0,H|0,p|0,q|0)|0,s=H,t=j+7316|0,h=f[t>>2]|0,h>>>0<n>>>0):0){i=f[d+4>>2]|0;do{d=i+(h*24|0)|0;g=d;e=f[g>>2]|0;g=f[g+4>>2]|0;if(g>>>0>s>>>0|(g|0)==(s|0)&e>>>0>r>>>0)break b;if(!(g>>>0<q>>>0|(g|0)==(q|0)&e>>>0<p>>>0)){e=d;f[e>>2]=p;f[e+4>>2]=q;e=v;e=Te(f[e>>2]|0,f[e+4>>2]|0,y|0,z|0)|0;g=i+(h*24|0)+8|0;f[g>>2]=e;f[g+4>>2]=H;f[i+(h*24|0)+16>>2]=o}h=h+1|0;f[t>>2]=h}while(h>>>0<n>>>0)}while(0);if(La[f[j+7300>>2]&7](a,l,m,c,f[j+7056>>2]|0,f[j+7312>>2]|0)|0){f[f[a>>2]>>2]=5;vb(f[(f[x>>2]|0)+6856>>2]|0);break}y=f[x>>2]|0;z=y+7328|0;v=z;v=Se(f[v>>2]|0,f[v+4>>2]|0,m|0,0)|0;f[z>>2]=v;f[z+4>>2]=H;z=y+7336|0;v=z;v=Se(f[v>>2]|0,f[v+4>>2]|0,c|0,0)|0;f[z>>2]=v;f[z+4>>2]=H;z=y+7344|0;v=f[z>>2]|0;y=(f[y+7056>>2]|0)+1|0;f[z>>2]=v>>>0>y>>>0?v:y;vb(f[(f[x>>2]|0)+6856>>2]|0);if(k){z=1;u=A;return z|0}w=f[w>>2]|0;z=(f[x>>2]|0)+6896|0;y=f[z>>2]|0;f[z>>2]=w>>>0<y>>>0?w:y;z=(f[x>>2]|0)+6900|0;y=f[z>>2]|0;f[z>>2]=w>>>0>y>>>0?w:y;z=1;u=A;return z|0}while(0);f[f[a>>2]>>2]=5;z=0;u=A;return z|0}function Yc(a,b,c,d,e,g,h,i,j,k,l,m,n){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0;P=g+e|0;u=ac(k,P,g)|0;O=u>>>0>j>>>0?j:u;Pa[f[a+7244>>2]&7](b,c,e,g,O,u,l);Q=(m|0)!=0;if(Q){if((u|0)>-1){k=1<<u;t=P>>>u;j=0;s=0;do{p=(s|0)==0?g:0;q=t-p|0;if(q){r=0;l=0;o=j;while(1){N=f[b+(o<<2)>>2]|0;r=N>>31^N|r;l=l+1|0;if((l|0)==(q|0))break;else o=o+1|0}j=j+t-p|0;l=((Z(r|0)|0)^31)+2|0;if(!r)R=8}else R=8;if((R|0)==8){R=0;l=1}f[d+(s<<2)>>2]=l;s=s+1|0}while(s>>>0<k>>>0)}else k=0;if((u|0)>(O|0)){j=0;p=u;do{p=p+-1|0;o=1<<p;l=0;do{M=f[d+(j<<2)>>2]|0;N=f[d+(j+1<<2)>>2]|0;f[d+(k<<2)>>2]=M>>>0>N>>>0?M:N;j=j+2|0;k=k+1|0;l=l+1|0}while(l>>>0<o>>>0)}while((p|0)>(O|0))}}if((u|0)<(O|0)){f[n+4>>2]=0;o=0;j=0;p=0;k=6}else{K=(f[274]|0)+(f[278]|0)|0;L=f[274]|0;I=W(h+1|0,e)|0;M=(h|0)==0;N=i+-1|0;F=(m|0)==0;G=(f[275]|0)+(f[275]|0)|0;I=I-(e>>>1)+L|0;J=h+-1|0;E=0;l=0;j=0;k=0;while(1){B=c+(E<<3)|0;y=d+(E<<2)|0;D=(j|0)==0&1;C=a+11748+(D*12|0)|0;ec(C,u>>>0>6?u:6)|0;C=f[C>>2]|0;z=f[a+11748+(D*12|0)+4>>2]|0;if(u){p=1<<u;A=P>>>u;if(A>>>0<=g>>>0)break;if(F){x=0;o=K;y=0;do{w=A-((x|0)==0?g:0)|0;v=B+(x<<3)|0;m=f[v>>2]|0;v=f[v+4>>2]|0;if(v>>>0<0|(v|0)==0&m>>>0<268435457){if(w<<3>>>0<m>>>0){s=w;q=0;while(1){q=q+4|0;r=s<<4;if(s<<7>>>0<m>>>0)s=r;else break}}else{r=w;q=0}if(r>>>0<m>>>0)do{q=q+1|0;r=r<<1}while(r>>>0<m>>>0)}else{z=_e(w|0,0,7)|0;b=H;if((v>>>0<16777216|(v|0)==16777216&m>>>0<1)&(b>>>0<v>>>0|(b|0)==(v|0)&z>>>0<m>>>0)){q=0;t=w;b=0;while(1){q=q+8|0;r=_e(t|0,b|0,8)|0;s=H;z=_e(t|0,b|0,15)|0;b=H;if(b>>>0<v>>>0|(b|0)==(v|0)&z>>>0<m>>>0){t=r;b=s}else break}}else{q=0;s=0;r=w}if(s>>>0<v>>>0|(s|0)==(v|0)&r>>>0<m>>>0)do{q=q+1|0;r=_e(r|0,s|0,1)|0;s=H}while(s>>>0<v>>>0|(s|0)==(v|0)&r>>>0<m>>>0)}s=q>>>0<i>>>0?q:N;r=W(s+1|0,w)|0;if(!s)q=m<<1;else q=Ze(m|0,v|0,s+-1|0)|0;z=L-(w>>>1)+r+q|0;y=(z|0)==-1?y:s;f[C+(x<<2)>>2]=y;o=z+o|0;x=x+1|0}while(x>>>0<p>>>0)}else{x=0;o=K;do{w=A-((x|0)==0?g:0)|0;v=B+(x<<3)|0;m=f[v>>2]|0;v=f[v+4>>2]|0;if(v>>>0<0|(v|0)==0&m>>>0<268435457){if(w<<3>>>0<m>>>0){s=w;q=0;while(1){q=q+4|0;r=s<<4;if(s<<7>>>0<m>>>0)s=r;else break}}else{r=w;q=0}if(r>>>0<m>>>0)do{q=q+1|0;r=r<<1}while(r>>>0<m>>>0)}else{b=_e(w|0,0,7)|0;t=H;if((v>>>0<16777216|(v|0)==16777216&m>>>0<1)&(t>>>0<v>>>0|(t|0)==(v|0)&b>>>0<m>>>0)){q=0;t=w;b=0;while(1){q=q+8|0;r=_e(t|0,b|0,8)|0;s=H;b=_e(t|0,b|0,15)|0;t=H;if(t>>>0<v>>>0|(t|0)==(v|0)&b>>>0<m>>>0){t=r;b=s}else break}}else{q=0;s=0;r=w}if(s>>>0<v>>>0|(s|0)==(v|0)&r>>>0<m>>>0)do{q=q+1|0;r=_e(r|0,s|0,1)|0;s=H}while(s>>>0<v>>>0|(s|0)==(v|0)&r>>>0<m>>>0)}s=q>>>0<i>>>0?q:N;r=W(s+1|0,w)|0;if(!s)q=m<<1;else q=Ze(m|0,v|0,s+-1|0)|0;v=L-(w>>>1)+r+q|0;b=f[y+(x<<2)>>2]|0;w=G+(W(b,w)|0)|0;m=w>>>0>v>>>0;f[z+(x<<2)>>2]=m?0:b;f[C+(x<<2)>>2]=m?s:0;o=(m?v:w)+o|0;x=x+1|0}while(x>>>0<p>>>0)}}else{p=B;o=f[p>>2]|0;p=Ze(o|0,f[p+4>>2]|0,J|0)|0;p=I+(M?o<<1:p)|0;o=(p|0)==-1?0:h;if(!F){y=f[y>>2]|0;A=G+(W(y,e)|0)|0;B=A>>>0>p>>>0;f[z>>2]=B?0:y;p=B?p:A;o=B?o:0}f[C>>2]=o;o=p+K|0;p=1}C=(k+-1|0)>>>0>=o>>>0;k=C?o:k;j=C?D:j;l=C?u:l;if((u|0)>(O|0)){E=E+p|0;u=u+-1|0}else break}f[n+4>>2]=l;o=l;p=k;k=l>>>0>6?l:6}l=f[n+8>>2]|0;ec(l,k)|0;o=1<<o;k=o<<2;af(f[l>>2]|0,f[a+11748+(j*12|0)>>2]|0,k|0)|0;if(Q)af(f[l+4>>2]|0,f[a+11748+(j*12|0)+4>>2]|0,k|0)|0;j=f[l>>2]|0;l=f[272]|0;k=0;while(1){if((f[j+(k<<2)>>2]|0)>>>0>=l>>>0)break;k=k+1|0;if(k>>>0>=o>>>0){R=68;break}}if((R|0)==68)return p|0;f[n>>2]=1;return p|0}function Zc(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return _c(a,0,b,c,d,e,f,0)|0}function _c(a,b,c,d,e,g,h,i){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,w=0,x=0,y=0,z=0;z=u;u=u+176|0;w=z;k=f[a>>2]|0;if((f[k>>2]|0)!=1){a=13;u=z;return a|0}if(i|0){a=2;u=z;return a|0}if(!c){a=3;u=z;return a|0}if((d|0)!=0&(e|0)==0){a=3;u=z;return a|0}i=f[k+24>>2]|0;if((i+-1|0)>>>0>7){a=4;u=z;return a|0}j=k+16|0;if((i|0)==2){if(!(f[j>>2]|0))x=9}else{f[j>>2]=0;x=9}if((x|0)==9)f[k+20>>2]=0;i=f[k+28>>2]|0;if(i>>>0>31){f[j>>2]=0;a=5;u=z;return a|0}if((i+-4|0)>>>0>20){a=5;u=z;return a|0}if(!(Tb(f[k+32>>2]|0)|0)){a=6;u=z;return a|0}k=f[a>>2]|0;i=k+36|0;j=f[i>>2]|0;if(j){if((j+-16|0)>>>0>65519){a=7;u=z;return a|0}}else{j=(f[k+556>>2]|0)==0?1152:4096;f[i>>2]=j}i=f[k+556>>2]|0;if(i>>>0>32){a=8;u=z;return a|0}if(j>>>0<i>>>0){a=10;u=z;return a|0}l=k+560|0;i=f[l>>2]|0;if(i){if((i+-5|0)>>>0>10){a=9;u=z;return a|0}}else{i=f[k+28>>2]|0;do if(i>>>0>=16){if((i|0)!=16){if(j>>>0<385){i=13;break}i=j>>>0<1153?14:15;break}if(j>>>0>=193)if(j>>>0>=385)if(j>>>0<577)i=9;else{if(j>>>0<1153){i=10;break}if(j>>>0<2305){i=11;break}i=j>>>0<4609?12:13}else i=8;else i=7}else{i=(i>>>1)+2|0;i=i>>>0>5?i:5}while(0);f[l>>2]=i}do if(f[k+8>>2]|0){if(!(Ub(j,f[k+32>>2]|0)|0)){a=11;u=z;return a|0}if(!(Vb(f[(f[a>>2]|0)+32>>2]|0)|0)){a=11;u=z;return a|0}k=f[a>>2]|0;q=(f[k+28>>2]|0)+-8|0;if((q>>>2|q<<30)>>>0>=5){a=11;u=z;return a|0}i=k+580|0;j=f[i>>2]|0;if(j>>>0>8){a=11;u=z;return a|0}if((f[k+32>>2]|0)>>>0<48001){if((f[k+36>>2]|0)>>>0>4608){a=11;u=z;return a|0}if((f[k+556>>2]|0)>>>0>12)y=11;else break;u=z;return y|0}}else{j=k+580|0;i=j;j=f[j>>2]|0}while(0);l=1<<f[274];if(j>>>0>=l>>>0){j=l+-1|0;f[i>>2]=j}i=k+576|0;if((f[i>>2]|0)>>>0>=j>>>0)f[i>>2]=j;o=f[k+600>>2]|0;l=f[k+604>>2]|0;i=(l|0)==0;a:do if(!o)if(i){v=0;r=k}else{a=12;u=z;return a|0}else if(!i){i=0;do{j=f[o+(i<<2)>>2]|0;if(j|0?(f[j>>2]|0)==3:0){x=51;break}i=i+1|0}while(i>>>0<l>>>0);if((x|0)==51)f[(f[a+4>>2]|0)+7048>>2]=j+16;q=0;p=0;k=0;l=0;m=0;i=o;b:while(1){j=f[i+(m<<2)>>2]|0;c:do switch(f[j>>2]|0){case 0:{y=12;x=173;break b}case 3:{if(l|0){y=12;x=173;break b}if(!(Wb(j+16|0)|0)){y=12;x=173;break b}else{i=q;j=p;l=1}break}case 4:{if(!k){i=q;j=p;k=1}else{y=12;x=173;break b}break}case 5:{if(!(_b(j+16|0,f[j+160>>2]|0,0)|0)){y=12;x=173;break b}else{i=q;j=p}break}case 6:{i=j+16|0;if(!($b(i,0)|0)){y=12;x=173;break b}switch(f[i>>2]|0){case 1:break;case 2:if(!q){i=1;j=p;break c}else{y=12;x=173;break b}default:{i=q;j=p;break c}}if(p|0){y=12;x=173;break b}i=f[j+20>>2]|0;if(de(i,3372)|0?de(i,3382)|0:0){y=12;x=173;break b}if((f[j+28>>2]|0)!=32){y=12;x=173;break b}if((f[j+32>>2]|0)==32){i=q;j=1}else{y=12;x=173;break b}break}default:{i=q;j=p}}while(0);m=m+1|0;o=f[a>>2]|0;if(m>>>0>=(f[o+604>>2]|0)>>>0){v=k;r=o;break a}q=i;p=j;i=f[o+600>>2]|0}if((x|0)==173){u=z;return y|0}}else{v=0;r=k}while(0);o=a+4|0;i=f[o>>2]|0;f[i>>2]=0;if(f[r+24>>2]|0){j=0;do{f[i+4+(j<<2)>>2]=0;f[(f[o>>2]|0)+7352+(j<<2)>>2]=0;f[(f[o>>2]|0)+44+(j<<2)>>2]=0;f[(f[o>>2]|0)+7392+(j<<2)>>2]=0;j=j+1|0;i=f[o>>2]|0}while(j>>>0<(f[(f[a>>2]|0)+24>>2]|0)>>>0)}f[i+36>>2]=0;f[(f[o>>2]|0)+7384>>2]=0;f[(f[o>>2]|0)+76>>2]=0;f[(f[o>>2]|0)+7424>>2]=0;f[(f[o>>2]|0)+40>>2]=0;f[(f[o>>2]|0)+7388>>2]=0;f[(f[o>>2]|0)+80>>2]=0;f[(f[o>>2]|0)+7428>>2]=0;j=f[a>>2]|0;i=f[o>>2]|0;if(f[j+40>>2]|0){k=0;do{f[i+84+(k<<2)>>2]=0;f[(f[o>>2]|0)+7432+(k<<2)>>2]=0;k=k+1|0;j=f[a>>2]|0;i=f[o>>2]|0}while(k>>>0<(f[j+40>>2]|0)>>>0)}f[i+212>>2]=0;f[i+7560>>2]=0;if(f[j+24>>2]|0){j=0;do{f[i+256+(j<<3)>>2]=0;f[(f[o>>2]|0)+7564+(j<<3)>>2]=0;f[(f[o>>2]|0)+256+(j<<3)+4>>2]=0;f[(f[o>>2]|0)+7564+(j<<3)+4>>2]=0;i=f[o>>2]|0;f[i+6768+(j<<2)>>2]=0;j=j+1|0}while(j>>>0<(f[(f[a>>2]|0)+24>>2]|0)>>>0)}f[i+320>>2]=0;f[(f[o>>2]|0)+7628>>2]=0;f[(f[o>>2]|0)+324>>2]=0;f[(f[o>>2]|0)+7632>>2]=0;i=f[o>>2]|0;f[i+6800>>2]=0;f[i+328>>2]=0;f[(f[o>>2]|0)+7636>>2]=0;f[(f[o>>2]|0)+332>>2]=0;f[(f[o>>2]|0)+7640>>2]=0;i=f[o>>2]|0;f[i+6804>>2]=0;f[i+6848>>2]=0;f[i+7644>>2]=0;f[i+6852>>2]=0;f[i+7648>>2]=0;j=f[a>>2]|0;j=~~(+((f[j+32>>2]|0)>>>0)*.4/+((f[j+36>>2]|0)>>>0)+.5)>>>0;f[i+6860>>2]=(j|0)==0?1:j;f[i+6864>>2]=0;f[i+7052>>2]=0;f[i+7056>>2]=0;Mb(i+7156|0);i=f[o>>2]|0;f[i+7256>>2]=6;f[i+7244>>2]=6;f[i+7248>>2]=10;f[i+7252>>2]=11;f[i+7260>>2]=8;f[i+7264>>2]=9;f[i+7268>>2]=8;j=f[a>>2]|0;f[j>>2]=0;f[i+7288>>2]=b;f[i+7300>>2]=c;f[i+7292>>2]=d;f[i+7296>>2]=e;f[i+7304>>2]=g;f[i+7312>>2]=h;m=f[j+36>>2]|0;d:do if((f[i>>2]|0)>>>0<m>>>0){k=m+5|0;i=f[o>>2]|0;e:do if(!(f[j+24>>2]|0)){s=i;x=84}else{j=0;while(1){x=(tc(k,i+7352+(j<<2)|0,i+4+(j<<2)|0)|0)==0;h=f[(f[o>>2]|0)+4+(j<<2)>>2]|0;f[h>>2]=0;f[h+4>>2]=0;f[h+8>>2]=0;f[h+12>>2]=0;h=(f[o>>2]|0)+4+(j<<2)|0;f[h>>2]=(f[h>>2]|0)+16;if(x){x=129;break e}j=j+1|0;i=f[o>>2]|0;if(j>>>0>=(f[(f[a>>2]|0)+24>>2]|0)>>>0){s=i;x=84;break}}}while(0);f:do if((x|0)==84){s=(tc(k,s+7384|0,s+36|0)|0)==0;h=f[(f[o>>2]|0)+36>>2]|0;f[h>>2]=0;f[h+4>>2]=0;f[h+8>>2]=0;f[h+12>>2]=0;h=(f[o>>2]|0)+36|0;f[h>>2]=(f[h>>2]|0)+16;if(s){x=129;break}s=f[o>>2]|0;s=tc(k,s+7388|0,s+40|0)|0;h=f[(f[o>>2]|0)+40>>2]|0;f[h>>2]=0;f[h+4>>2]=0;f[h+8>>2]=0;f[h+12>>2]=0;h=(f[o>>2]|0)+40|0;f[h>>2]=(f[h>>2]|0)+16;if(!s){x=129;break}j=f[a>>2]|0;if(f[j+556>>2]|0){i=f[o>>2]|0;if(f[j+40>>2]|0){j=0;do{if(!(wc(m,i+7432+(j<<2)|0,i+84+(j<<2)|0)|0)){x=129;break f}j=j+1|0;i=f[o>>2]|0}while(j>>>0<(f[(f[a>>2]|0)+40>>2]|0)>>>0)}if(!(wc(m,i+7560|0,i+212|0)|0)){x=129;break}}i=0;while(1){if(i>>>0>=(f[(f[a>>2]|0)+24>>2]|0)>>>0)break;s=f[o>>2]|0;if(!(tc(m,s+7564+(i<<3)|0,s+256+(i<<3)|0)|0)){x=129;break f}s=f[o>>2]|0;if(!(tc(m,s+7564+(i<<3)+4|0,s+256+(i<<3)+4|0)|0)){x=129;break f}else i=i+1|0}i=0;while(1){s=f[o>>2]|0;if(!(tc(m,s+7628+(i<<3)|0,s+320+(i<<3)|0)|0)){x=129;break f}j=f[o>>2]|0;j=(tc(m,j+7628+(i<<3)+4|0,j+320+(i<<3)+4|0)|0)!=0;if((i+1|0)>>>0<2&j)i=1;else break}if(!j){x=129;break}i=m<<1;j=f[o>>2]|0;j=(uc(i,j+7644|0,j+6848|0)|0)!=0;k=f[a>>2]|0;l=(f[k+572>>2]|0)==0;if(l|j^1){if(!(j&l)){t=k;break}}else{s=f[o>>2]|0;if(!(vc(i,s+7648|0,s+6852|0)|0)){x=129;break}}k=f[o>>2]|0;do if((f[k>>2]|0)==(m|0)){j=k;i=k}else{i=f[a>>2]|0;if(!(f[i+556>>2]|0)){j=k;i=k;break}if(!(f[i+40>>2]|0)){j=k;i=k;break}j=0;do{do switch(f[i+44+(j<<4)>>2]|0){case 0:{ud(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 1:{vd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 2:{wd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 3:{xd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 4:{yd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 5:{zd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 6:{Ad(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m,+n[i+44+(j<<4)+4>>2]);break}case 7:{Bd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 8:{Cd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 9:{Dd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 10:{Ed(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 11:{Fd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 12:{Gd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}case 13:{Hd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m,+n[i+44+(j<<4)+4>>2]);break}case 14:{Id(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m,+n[i+44+(j<<4)+4>>2],+n[i+44+(j<<4)+8>>2],+n[i+44+(j<<4)+12>>2]);break}case 15:{Jd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m,+n[i+44+(j<<4)+4>>2],+n[i+44+(j<<4)+8>>2],+n[i+44+(j<<4)+12>>2]);break}case 16:{Kd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m);break}default:Cd(f[(f[o>>2]|0)+84+(j<<2)>>2]|0,m)}while(0);j=j+1|0;i=f[a>>2]|0}while(j>>>0<(f[i+40>>2]|0)>>>0);i=f[o>>2]|0;j=i}while(0);f[j>>2]=m;break d}while(0);if((x|0)==129)t=f[a>>2]|0;f[t>>2]=8;a=1;u=z;return a|0}while(0);t=(ub(f[i+6856>>2]|0)|0)==0;j=f[a>>2]|0;if(t){f[j>>2]=8;a=1;u=z;return a|0}do if(!(f[j+4>>2]|0))k=1;else{k=(f[j+36>>2]|0)+1|0;i=f[o>>2]|0;f[i+11820>>2]=k;g:do if(f[j+24>>2]|0){t=xc(4,k)|0;f[(f[o>>2]|0)+11788>>2]=t;i=f[a>>2]|0;if(t){k=1;while(1){j=f[o>>2]|0;if(k>>>0>=(f[i+24>>2]|0)>>>0){i=j;break g}t=xc(4,f[j+11820>>2]|0)|0;f[(f[o>>2]|0)+11788+(k<<2)>>2]=t;i=f[a>>2]|0;if(!t)break;else k=k+1|0}}f[i>>2]=8;a=1;u=z;return a|0}while(0);f[i+11824>>2]=0;i=f[i+11776>>2]|0;do if(!i){i=yc()|0;f[(f[o>>2]|0)+11776>>2]=i;if(i|0)break;f[f[a>>2]>>2]=3;a=1;u=z;return a|0}while(0);t=(Bc(i,6,0,0,0,0,7,6,7,a)|0)==0;i=f[a>>2]|0;if(t){j=i;k=(f[i+4>>2]|0)==0;break}f[i>>2]=3;a=1;u=z;return a|0}while(0);i=f[o>>2]|0;s=i+11840|0;f[i+7316>>2]=0;t=i+7336|0;f[t>>2]=0;f[t+4>>2]=0;t=j+608|0;f[s>>2]=0;f[s+4>>2]=0;f[s+8>>2]=0;f[s+12>>2]=0;f[s+16>>2]=0;f[s+20>>2]=0;f[s+24>>2]=0;f[t>>2]=0;f[t+4>>2]=0;f[t+8>>2]=0;f[t+12>>2]=0;f[t+16>>2]=0;f[t+20>>2]=0;if(!k)f[i+11780>>2]=0;if(!(Bb(f[i+6856>>2]|0,f[258]|0,f[265]|0)|0)){f[f[a>>2]>>2]=7;a=1;u=z;return a|0}if(!(Xc(a,0)|0)){a=1;u=z;return a|0}j=f[a>>2]|0;i=f[o>>2]|0;if(f[j+4>>2]|0)f[i+11780>>2]=1;f[i+6872>>2]=0;f[i+6876>>2]=0;f[i+6880>>2]=34;f[i+6888>>2]=f[j+36>>2];f[(f[o>>2]|0)+6892>>2]=f[(f[a>>2]|0)+36>>2];f[(f[o>>2]|0)+6896>>2]=0;f[(f[o>>2]|0)+6900>>2]=0;f[(f[o>>2]|0)+6904>>2]=f[(f[a>>2]|0)+32>>2];f[(f[o>>2]|0)+6908>>2]=f[(f[a>>2]|0)+24>>2];f[(f[o>>2]|0)+6912>>2]=f[(f[a>>2]|0)+28>>2];h=(f[a>>2]|0)+592|0;s=f[h+4>>2]|0;t=(f[o>>2]|0)+6920|0;f[t>>2]=f[h>>2];f[t+4>>2]=s;t=(f[o>>2]|0)+6928|0;f[t>>2]=0;f[t+4>>2]=0;f[t+8>>2]=0;f[t+12>>2]=0;if(f[(f[a>>2]|0)+12>>2]|0)pc((f[o>>2]|0)+7060|0);t=f[o>>2]|0;if(!(nd(t+6872|0,f[t+6856>>2]|0)|0)){f[f[a>>2]>>2]=7;a=1;u=z;return a|0}if(!(Xc(a,0)|0)){a=1;u=z;return a|0}f[(f[o>>2]|0)+6896>>2]=(1<<f[267])+-1;t=(f[o>>2]|0)+6920|0;f[t>>2]=0;f[t+4>>2]=0;do if(!v){f[w>>2]=4;f[w+4>>2]=(f[(f[a>>2]|0)+604>>2]|0)==0&1;f[w+8>>2]=8;v=w+16|0;f[v>>2]=0;f[v+4>>2]=0;f[v+8>>2]=0;f[v+12>>2]=0;if(!(nd(w,f[(f[o>>2]|0)+6856>>2]|0)|0)){f[f[a>>2]>>2]=7;a=1;u=z;return a|0}else{if(!(Xc(a,0)|0))y=1;else break;u=z;return y|0}}while(0);i=f[a>>2]|0;j=f[i+604>>2]|0;h:do if(j){k=0;while(1){w=f[(f[i+600>>2]|0)+(k<<2)>>2]|0;f[w+4>>2]=(k|0)==(j+-1|0)&1;if(!(nd(w,f[(f[o>>2]|0)+6856>>2]|0)|0))break;k=k+1|0;if(!(Xc(a,0)|0)){y=1;x=173;break}i=f[a>>2]|0;j=f[i+604>>2]|0;if(k>>>0>=j>>>0)break h}if((x|0)==173){u=z;return y|0}f[f[a>>2]>>2]=7;a=1;u=z;return a|0}while(0);j=f[o>>2]|0;k=f[j+7296>>2]|0;do if(k){y=(Ja[k&15](a,i+624|0,f[j+7312>>2]|0)|0)==1;i=f[a>>2]|0;if(!y)break;f[i>>2]=5;a=1;u=z;return a|0}while(0);if(!(f[i+4>>2]|0)){a=0;u=z;return a|0}f[(f[o>>2]|0)+11780>>2]=2;a=0;u=z;return a|0}function $c(a,b,c,d,e,g,h){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0;l=(d+c|0)>>>g;m=1<<g;c=0-d|0;i=l-d|0;if((h+4|0)>>>0<(32-((Z(l|0)|0)^31)|0)>>>0){d=0;j=0;while(1){c=c+l|0;if(d>>>0<c>>>0){h=0;do{k=f[a+(d<<2)>>2]|0;h=((k|0)>-1?k:0-k|0)+h|0;d=d+1|0}while((d|0)!=(i|0));d=c}else h=0;k=b+(j<<3)|0;f[k>>2]=h;f[k+4>>2]=0;j=j+1|0;if(j>>>0>=m>>>0)break;else i=i+l|0}}else{k=0;d=0;j=i;while(1){c=c+l|0;if(d>>>0<c>>>0){i=0;h=0;do{n=f[a+(d<<2)>>2]|0;n=(n|0)>-1?n:0-n|0;i=Se(i|0,h|0,n|0,((n|0)<0)<<31>>31|0)|0;h=H;d=d+1|0}while((d|0)!=(j|0));d=c}else{i=0;h=0}n=b+(k<<3)|0;f[n>>2]=i;f[n+4>>2]=h;k=k+1|0;if(k>>>0>=m>>>0)break;else j=j+l|0}}if((g|0)<=(e|0))return;d=m;k=0;l=m;while(1){g=g+-1|0;c=l;l=l>>>1;if(!l)c=k;else{j=c&-2;c=0;h=k;i=d;while(1){m=b+(h<<3)|0;n=b+(h+1<<3)|0;m=Se(f[n>>2]|0,f[n+4>>2]|0,f[m>>2]|0,f[m+4>>2]|0)|0;n=b+(i<<3)|0;f[n>>2]=m;f[n+4>>2]=H;c=c+1|0;if((c|0)==(l|0))break;else{h=h+2|0;i=i+1|0}}d=d+l|0;c=k+j|0}if((g|0)<=(e|0))break;else k=c}return}function ad(a,c,d,e){a=a|0;c=c|0;d=d|0;e=e|0;var g=0,i=0;g=e+4|0;i=f[g>>2]|0;e=f[i+11836>>2]|0;if(f[i+11784>>2]|0){f[d>>2]=4;d=h[2353]|h[2354]<<8|h[2355]<<16|h[2356]<<24;b[c>>0]=d;b[c+1>>0]=d>>8;b[c+2>>0]=d>>16;b[c+3>>0]=d>>24;f[(f[g>>2]|0)+11784>>2]=0;d=0;return d|0}if(!e){d=2;return d|0}a=f[d>>2]|0;if(e>>>0<a>>>0){f[d>>2]=e;a=e}af(c|0,f[i+11828>>2]|0,a|0)|0;c=f[d>>2]|0;d=f[g>>2]|0;i=d+11828|0;f[i>>2]=(f[i>>2]|0)+c;d=d+11836|0;f[d>>2]=(f[d>>2]|0)-c;d=0;return d|0}function bd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;h=f[b+8>>2]|0;n=f[b>>2]|0;e=n<<2;g=d+4|0;m=f[g>>2]|0;if(!h){d=m+11824|0;f[d>>2]=(f[d>>2]|0)-n;n=0;return n|0}l=0;while(1){j=f[c+(l<<2)>>2]|0;k=f[m+11788+(l<<2)>>2]|0;if(ee(j,k,e)|0)break;a=l+1|0;if(a>>>0<h>>>0)l=a;else{i=11;break}}if((i|0)==11){b=m+11824|0;d=(f[b>>2]|0)-n|0;f[b>>2]=d;b=f[m+11788>>2]|0;bf(b|0,b+(n<<2)|0,d<<2|0)|0;if((h|0)==1){n=0;return n|0}a=1;do{d=f[g>>2]|0;b=f[d+11788+(a<<2)>>2]|0;bf(b|0,b+(n<<2)|0,f[d+11824>>2]<<2|0)|0;a=a+1|0}while((a|0)!=(h|0));a=0;return a|0}a:do if(!n){g=0;e=0;a=0}else{a=0;while(1){g=f[j+(a<<2)>>2]|0;e=f[k+(a<<2)>>2]|0;if((g|0)!=(e|0))break a;a=a+1|0;if(a>>>0>=n>>>0){g=0;e=0;a=0;break}}}while(0);b=b+24|0;j=b;j=Se(f[j>>2]|0,f[j+4>>2]|0,a|0,0)|0;k=m+11840|0;f[k>>2]=j;f[k+4>>2]=H;n=We(f[b>>2]|0,f[b+4>>2]|0,n|0,0)|0;f[m+11848>>2]=n;f[m+11852>>2]=l;f[m+11856>>2]=a;f[m+11860>>2]=e;f[m+11864>>2]=g;f[f[d>>2]>>2]=4;n=1;return n|0}function cd(a,b,c){a=a|0;b=b|0;c=c|0;return}function dd(a,b,c){a=a|0;b=b|0;c=c|0;f[f[c>>2]>>2]=3;return}function ed(a,b){a=a|0;b=b|0;a=f[a>>2]|0;if((f[a>>2]|0)!=1){b=0;return b|0}f[a+4>>2]=b;b=1;return b|0}function fd(a,b){a=a|0;b=b|0;a=f[a>>2]|0;if((f[a>>2]|0)!=1){b=0;return b|0}f[a+24>>2]=b;b=1;return b|0}function gd(a,b){a=a|0;b=b|0;a=f[a>>2]|0;if((f[a>>2]|0)!=1){b=0;return b|0}f[a+28>>2]=b;b=1;return b|0}function hd(a,b){a=a|0;b=b|0;a=f[a>>2]|0;if((f[a>>2]|0)!=1){b=0;return b|0}f[a+32>>2]=b;b=1;return b|0}function id(a,b){a=a|0;b=b|0;var c=0,d=0,e=0;c=f[a>>2]|0;if((f[c>>2]|0)!=1){e=0;return e|0}e=b>>>0<8?b:8;f[c+16>>2]=f[1124+(e*44|0)>>2];f[c+20>>2]=f[1124+(e*44|0)+4>>2];d=Rc(a,f[1124+(e*44|0)+40>>2]|0)|0;b=f[a>>2]|0;if((f[b>>2]|0)==1){f[b+556>>2]=f[1124+(e*44|0)+8>>2];f[b+560>>2]=f[1124+(e*44|0)+12>>2];f[b+564>>2]=f[1124+(e*44|0)+16>>2];f[b+568>>2]=f[1124+(e*44|0)+24>>2];f[b+576>>2]=f[1124+(e*44|0)+28>>2];f[b+580>>2]=f[1124+(e*44|0)+32>>2];c=1;b=d&1}else{c=0;b=0}e=c&b;return e|0}function jd(a,b){a=a|0;b=b|0;a=f[a>>2]|0;if((f[a>>2]|0)!=1){b=0;return b|0}f[a+36>>2]=b;b=1;return b|0}function kd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0;a=f[a>>2]|0;if((f[a>>2]|0)!=1){c=0;return c|0}g=_e(1,0,f[260]|0)|0;g=Se(g|0,H|0,-1,-1)|0;d=H;e=d>>>0>c>>>0|(d|0)==(c|0)&g>>>0>b>>>0;a=a+592|0;f[a>>2]=e?b:g;f[a+4>>2]=e?c:d;c=1;return c|0}function ld(a){a=a|0;return f[f[a>>2]>>2]|0}function md(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;q=f[a>>2]|0;v=f[q+24>>2]|0;w=f[q+36>>2]|0;t=a+4|0;u=w+1|0;if((v|0)==2&(f[q+16>>2]|0)!=0){g=0;e=0;d=q;while(1){o=f[t>>2]|0;p=o+7052|0;if(f[d+4>>2]|0){n=u-(f[p>>2]|0)|0;h=c-g|0;h=n>>>0<h>>>0?n:h;n=o+11824|0;d=f[n>>2]|0;if(h){l=f[o+11788>>2]|0;m=f[o+11792>>2]|0;i=0;j=g<<1;k=d;while(1){f[l+(k<<2)>>2]=f[b+(j<<2)>>2];f[m+(k<<2)>>2]=f[b+((j|1)<<2)>>2];i=i+1|0;if((i|0)==(h|0))break;else{j=j+2|0;k=k+1|0}}d=h+d|0}f[n>>2]=d}h=f[p>>2]|0;d=g>>>0<c>>>0;if(d&h>>>0<=w>>>0){k=f[o+4>>2]|0;l=f[o+8>>2]|0;m=f[o+40>>2]|0;j=f[o+36>>2]|0;while(1){d=f[b+(e<<2)>>2]|0;f[k+(h<<2)>>2]=d;i=e+2|0;s=f[b+(e+1<<2)>>2]|0;f[l+(h<<2)>>2]=s;f[m+(h<<2)>>2]=d-s;f[j+(h<<2)>>2]=s+d>>1;h=h+1|0;g=g+1|0;d=g>>>0<c>>>0;if(d&h>>>0<=w>>>0)e=i;else{e=i;break}}}f[p>>2]=h;if(h>>>0>w>>>0){if(!(Uc(a,0)|0)){d=0;e=45;break}s=f[t>>2]|0;r=f[s+4>>2]|0;f[r>>2]=f[r+(w<<2)>>2];r=f[s+8>>2]|0;f[r>>2]=f[r+(w<<2)>>2];r=f[s+36>>2]|0;f[r>>2]=f[r+(w<<2)>>2];r=f[s+40>>2]|0;f[r>>2]=f[r+(w<<2)>>2];f[s+7052>>2]=1}if(!d){d=1;e=45;break}d=f[a>>2]|0}if((e|0)==45)return d|0}r=(v|0)==0;s=(v|0)==1;d=0;i=0;e=q;while(1){o=f[t>>2]|0;p=o+7052|0;if(f[e+4>>2]|0){n=u-(f[p>>2]|0)|0;m=c-d|0;m=n>>>0<m>>>0?n:m;n=o+11824|0;e=f[n>>2]|0;do if(m){if(r){e=m+e|0;break}g=0;h=W(d,v)|0;k=e;while(1){j=0;l=h;while(1){f[(f[o+11788+(j<<2)>>2]|0)+(k<<2)>>2]=f[b+(l<<2)>>2];j=j+1|0;if((j|0)==(v|0))break;else l=l+1|0}g=g+1|0;if((g|0)==(m|0))break;else{h=h+v|0;k=k+1|0}}e=m+e|0}while(0);f[n>>2]=e}e=f[p>>2]|0;g=d>>>0<c>>>0;a:do if(g&e>>>0<=w>>>0){if(r)while(1){e=e+1|0;d=d+1|0;g=d>>>0<c>>>0;if(!(g&e>>>0<=w>>>0)){h=d;break a}}do{g=0;h=i;while(1){f[(f[o+4+(g<<2)>>2]|0)+(e<<2)>>2]=f[b+(h<<2)>>2];g=g+1|0;if((g|0)==(v|0))break;else h=h+1|0}i=v+i|0;e=e+1|0;d=d+1|0;g=d>>>0<c>>>0}while(g&e>>>0<=w>>>0);h=d}else h=d;while(0);f[p>>2]=e;if(e>>>0>w>>>0){if(!(Uc(a,0)|0)){d=0;e=45;break}e=f[t>>2]|0;if(!r?(q=f[e+4>>2]|0,f[q>>2]=f[q+(w<<2)>>2],!s):0){d=1;do{q=f[e+4+(d<<2)>>2]|0;f[q>>2]=f[q+(w<<2)>>2];d=d+1|0}while((d|0)!=(v|0))}f[e+7052>>2]=1}if(!g){d=1;e=45;break}d=h;e=f[a>>2]|0}if((e|0)==45)return d|0;return 0}function nd(a,c){a=a|0;c=c|0;var d=0,e=0,g=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;g=Ce(2320)|0;if(!(Bb(c,f[a+4>>2]|0,f[277]|0)|0)){c=0;return c|0}if(!(Bb(c,f[a>>2]|0,f[266]|0)|0)){c=0;return c|0}i=a+8|0;d=f[i>>2]|0;if((f[a>>2]|0)==4)d=d+g-(f[a+16>>2]|0)|0;e=f[267]|0;if(d>>>0>=1<<e>>>0){c=0;return c|0}if(!(Bb(c,d,e)|0)){c=0;return c|0}a:do switch(f[a>>2]|0){case 0:{if(!(Bb(c,f[a+16>>2]|0,f[279]|0)|0)){c=0;return c|0}if(!(Bb(c,f[a+20>>2]|0,f[279]|0)|0)){c=0;return c|0}if(!(Bb(c,f[a+24>>2]|0,f[267]|0)|0)){c=0;return c|0}if(!(Bb(c,f[a+28>>2]|0,f[267]|0)|0)){c=0;return c|0}if(!(Bb(c,f[a+32>>2]|0,f[259]|0)|0)){c=0;return c|0}if(!(Bb(c,(f[a+36>>2]|0)+-1|0,f[270]|0)|0)){c=0;return c|0}if(!(Bb(c,(f[a+40>>2]|0)+-1|0,f[275]|0)|0)){c=0;return c|0}u=a+48|0;if(!(Eb(c,f[u>>2]|0,f[u+4>>2]|0,f[260]|0)|0)){c=0;return c|0}if(!(Gb(c,a+56|0,16)|0)){c=0;return c|0}break}case 1:{if(!(Ab(c,f[i>>2]<<3)|0)){c=0;return c|0}break}case 2:{d=(f[265]|0)>>>3;if(!(Gb(c,a+16|0,d)|0)){c=0;return c|0}if(!(Gb(c,f[a+20>>2]|0,(f[i>>2]|0)-d|0)|0)){c=0;return c|0}break}case 3:{j=a+16|0;if(f[j>>2]|0){e=a+20|0;g=f[280]|0;i=f[280]|0;a=f[279]|0;d=0;while(1){u=(f[e>>2]|0)+(d*24|0)|0;if(!(Eb(c,f[u>>2]|0,f[u+4>>2]|0,g)|0)){d=0;e=68;break}u=(f[e>>2]|0)+(d*24|0)+8|0;if(!(Eb(c,f[u>>2]|0,f[u+4>>2]|0,i)|0)){d=0;e=68;break}if(!(Bb(c,f[(f[e>>2]|0)+(d*24|0)+16>>2]|0,a)|0)){d=0;e=68;break}d=d+1|0;if(d>>>0>=(f[j>>2]|0)>>>0)break a}if((e|0)==68)return d|0}break}case 4:{if(!(Fb(c,g)|0)){c=0;return c|0}if(!(Gb(c,2320,g)|0)){c=0;return c|0}g=a+24|0;if(!(Fb(c,f[g>>2]|0)|0)){c=0;return c|0}if(f[g>>2]|0){e=a+28|0;d=0;while(1){if(!(Fb(c,f[(f[e>>2]|0)+(d<<3)>>2]|0)|0)){d=0;e=68;break}u=f[e>>2]|0;if(!(Gb(c,f[u+(d<<3)+4>>2]|0,f[u+(d<<3)>>2]|0)|0)){d=0;e=68;break}d=d+1|0;if(d>>>0>=(f[g>>2]|0)>>>0)break a}if((e|0)==68)return d|0}break}case 5:{if(!(Gb(c,a+16|0,(f[263]|0)>>>3)|0)){c=0;return c|0}u=a+152|0;if(!(Eb(c,f[u>>2]|0,f[u+4>>2]|0,f[280]|0)|0)){c=0;return c|0}if(!(Bb(c,(f[a+160>>2]|0)!=0&1,f[277]|0)|0)){c=0;return c|0}if(!(Ab(c,f[264]|0)|0)){c=0;return c|0}u=a+164|0;if(!(Bb(c,f[u>>2]|0,f[271]|0)|0)){c=0;return c|0}if(f[u>>2]|0){j=a+168|0;k=f[280]|0;l=f[271]|0;m=(f[261]|0)>>>3;n=f[277]|0;o=f[277]|0;p=f[262]|0;q=f[271]|0;r=f[280]|0;s=f[271]|0;t=f[267]|0;a=0;b:while(1){e=f[j>>2]|0;i=e+(a<<5)|0;if(!(Eb(c,f[i>>2]|0,f[i+4>>2]|0,k)|0)){d=0;e=68;break}if(!(Bb(c,h[e+(a<<5)+8>>0]|0,l)|0)){d=0;e=68;break}if(!(Gb(c,e+(a<<5)+9|0,m)|0)){d=0;e=68;break}d=e+(a<<5)+22|0;if(!(Bb(c,b[d>>0]&1,n)|0)){d=0;e=68;break}if(!(Bb(c,(h[d>>0]|0)>>>1&1,o)|0)){d=0;e=68;break}if(!(Ab(c,p)|0)){d=0;e=68;break}i=e+(a<<5)+23|0;if(!(Bb(c,h[i>>0]|0,q)|0)){d=0;e=68;break}if(b[i>>0]|0){e=e+(a<<5)+24|0;d=0;do{g=f[e>>2]|0;v=g+(d<<4)|0;if(!(Eb(c,f[v>>2]|0,f[v+4>>2]|0,r)|0)){d=0;e=68;break b}if(!(Bb(c,h[g+(d<<4)+8>>0]|0,s)|0)){d=0;e=68;break b}d=d+1|0;if(!(Ab(c,t)|0)){d=0;e=68;break b}}while(d>>>0<(h[i>>0]|0)>>>0)}a=a+1|0;if(a>>>0>=(f[u>>2]|0)>>>0)break a}if((e|0)==68)return d|0}break}case 6:{if(!(Bb(c,f[a+16>>2]|0,f[265]|0)|0)){v=0;return v|0}d=a+20|0;e=Ce(f[d>>2]|0)|0;if(!(Bb(c,e,f[265]|0)|0)){v=0;return v|0}if(!(Gb(c,f[d>>2]|0,e)|0)){v=0;return v|0}d=a+24|0;e=Ce(f[d>>2]|0)|0;if(!(Bb(c,e,f[265]|0)|0)){v=0;return v|0}if(!(Gb(c,f[d>>2]|0,e)|0)){v=0;return v|0}if(!(Bb(c,f[a+28>>2]|0,f[265]|0)|0)){v=0;return v|0}if(!(Bb(c,f[a+32>>2]|0,f[265]|0)|0)){v=0;return v|0}if(!(Bb(c,f[a+36>>2]|0,f[265]|0)|0)){v=0;return v|0}if(!(Bb(c,f[a+40>>2]|0,f[265]|0)|0)){v=0;return v|0}d=a+44|0;if(!(Bb(c,f[d>>2]|0,f[265]|0)|0)){v=0;return v|0}if(!(Gb(c,f[a+48>>2]|0,f[d>>2]|0)|0)){v=0;return v|0}break}default:if(!(Gb(c,f[a+16>>2]|0,f[i>>2]|0)|0)){v=0;return v|0}}while(0);v=1;return v|0}function od(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,i=0,j=0,k=0,l=0;l=u;u=u+16|0;k=l;if(!(Bb(b,f[268]|0,f[269]|0)|0)){k=0;u=l;return k|0}if(!(Bb(b,0,f[277]|0)|0)){k=0;u=l;return k|0}j=a+20|0;if(!(Bb(b,(f[j>>2]|0)!=0&1,f[277]|0)|0)){k=0;u=l;return k|0}c=f[a>>2]|0;a:do if((c|0)>=2048){if((c|0)<4608)switch(c|0){case 2304:{i=0;c=4;break a}case 2048:{i=0;c=11;break a}case 4096:{i=0;c=12;break a}default:{e=17;break a}}if((c|0)<16384)if((c|0)<8192){switch(c|0){case 4608:break;default:{e=17;break a}}i=0;c=5;break}else{switch(c|0){case 8192:break;default:{e=17;break a}}i=0;c=13;break}else if((c|0)<32768){switch(c|0){case 16384:break;default:{e=17;break a}}i=0;c=14;break}else{switch(c|0){case 32768:break;default:{e=17;break a}}i=0;c=15;break}}else switch(c|0){case 192:{i=0;c=1;break a}case 576:{i=0;c=2;break a}case 1152:{i=0;c=3;break a}case 256:{i=0;c=8;break a}case 512:{i=0;c=9;break a}case 1024:{i=0;c=10;break a}default:{e=17;break a}}while(0);if((e|0)==17){c=c>>>0<257?6:7;i=c}if(!(Bb(b,c,f[274]|0)|0)){k=0;u=l;return k|0}g=a+4|0;c=f[g>>2]|0;b:do if((c|0)<44100){if((c|0)<22050)if((c|0)<16e3){switch(c|0){case 8e3:break;default:{e=30;break b}}d=0;c=4;break}else{switch(c|0){case 16e3:break;default:{e=30;break b}}d=0;c=5;break}if((c|0)<24e3){switch(c|0){case 22050:break;default:{e=30;break b}}d=0;c=6;break}if((c|0)<32e3){switch(c|0){case 24e3:break;default:{e=30;break b}}d=0;c=7;break}else{switch(c|0){case 32e3:break;default:{e=30;break b}}d=0;c=8;break}}else{if((c|0)<96e3){if((c|0)<48e3){switch(c|0){case 44100:break;default:{e=30;break b}}d=0;c=9;break}if((c|0)>=88200)switch(c|0){case 88200:{d=0;c=1;break b}default:{e=30;break b}}switch(c|0){case 48e3:break;default:{e=30;break b}}d=0;c=10;break}if((c|0)<176400){switch(c|0){case 96e3:break;default:{e=30;break b}}d=0;c=11;break}if((c|0)<192e3){switch(c|0){case 176400:break;default:{e=30;break b}}d=0;c=2;break}else{switch(c|0){case 192e3:break;default:{e=30;break b}}d=0;c=3;break}}while(0);if((e|0)==30)if(c>>>0<255001&((c>>>0)%1e3|0|0)==0){d=12;c=12}else{e=((c>>>0)%10|0|0)==0;c=c>>>0<65536;d=e?14:c?13:0;c=e?14:c?13:0}if(!(Bb(b,c,f[274]|0)|0)){k=0;u=l;return k|0}switch(f[a+12>>2]|0){case 0:{c=(f[a+8>>2]|0)+-1|0;break}case 1:{c=8;break}case 2:{c=9;break}case 3:{c=10;break}default:{}}if(!(Bb(b,c,f[274]|0)|0)){k=0;u=l;return k|0}e=(f[a+16>>2]|0)+-8|0;switch(e>>>2|e<<30|0){case 0:{c=1;break}case 1:{c=2;break}case 2:{c=4;break}case 3:{c=5;break}case 4:{c=6;break}default:c=0}if(!(Bb(b,c,f[270]|0)|0)){k=0;u=l;return k|0}if(!(Bb(b,0,f[277]|0)|0)){k=0;u=l;return k|0}c=a+24|0;if(!(f[j>>2]|0)){if(!(Jb(b,f[c>>2]|0)|0)){k=0;u=l;return k|0}}else{j=c;if(!(Kb(b,f[j>>2]|0,f[j+4>>2]|0)|0)){k=0;u=l;return k|0}}if(i|0?(Bb(b,(f[a>>2]|0)+-1|0,(i|0)==6?8:16)|0)==0:0){k=0;u=l;return k|0}switch(d&15){case 12:{if(!(Bb(b,((f[g>>2]|0)>>>0)/1e3|0,8)|0)){k=0;u=l;return k|0}break}case 13:{if(!(Bb(b,f[g>>2]|0,16)|0)){k=0;u=l;return k|0}break}case 14:{if(!(Bb(b,((f[g>>2]|0)>>>0)/10|0,16)|0)){k=0;u=l;return k|0}break}default:{}}if(!(zb(b,k)|0)){k=0;u=l;return k|0}k=(Bb(b,h[k>>0]|0,f[271]|0)|0)!=0&1;u=l;return k|0}function pd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=(c|0)!=0;if(!(Bb(d,f[869]|e&1,(f[276]|0)+(f[277]|0)+(f[277]|0)|0)|0)){e=0;return e|0}if(e?(Hb(d,c+-1|0)|0)==0:0){e=0;return e|0}e=(Db(d,f[a>>2]|0,b)|0)!=0&1;return e|0}function qd(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var g=0,h=0;h=a+12|0;g=(d|0)!=0;if(!(Bb(e,f[279]|g&1|f[h>>2]<<1,(f[276]|0)+(f[277]|0)+(f[277]|0)|0)|0)){h=0;return h|0}if(g?(Hb(e,d+-1|0)|0)==0:0){h=0;return h|0}a:do if(f[h>>2]|0){d=0;while(1){if(!(Db(e,f[a+16+(d<<2)>>2]|0,c)|0)){d=0;break}d=d+1|0;if(d>>>0>=(f[h>>2]|0)>>>0)break a}return d|0}while(0);if(!(Bb(e,f[a>>2]|0,f[278]|0)|0)){h=0;return h|0}if((f[a>>2]|0)>>>0<2){if(!(Bb(e,f[a+4>>2]|0,f[274]|0)|0)){h=0;return h|0}d=f[a>>2]|0;if(d>>>0<2?(c=f[a+8>>2]|0,(rd(e,f[a+32>>2]|0,b,f[h>>2]|0,f[c>>2]|0,f[c+4>>2]|0,f[a+4>>2]|0,(d|0)==1&1)|0)==0):0){h=0;return h|0}}h=1;return h|0}function rd(a,b,c,d,e,g,h,i){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0;p=(i|0)!=0;o=p?f[275]|0:f[274]|0;p=p?f[273]|0:f[272]|0;if(!h){a:do if(!(f[g>>2]|0)){if(!(Bb(a,f[e>>2]|0,o)|0)){g=0;return g|0}if(!(Ib(a,b,c,f[e>>2]|0)|0)){g=0;return g|0}}else{if(!(Bb(a,p,o)|0)){g=0;return g|0}if(!(Bb(a,f[g>>2]|0,f[275]|0)|0)){g=0;return g|0}if(c|0){i=0;while(1){if(!(Db(a,f[b+(i<<2)>>2]|0,f[g>>2]|0)|0)){i=0;break}i=i+1|0;if(i>>>0>=c>>>0)break a}return i|0}}while(0);g=1;return g|0}n=(d+c|0)>>>h;l=1<<h;m=f[275]|0;j=0;k=0;b:while(1){h=n-((k|0)==0?d:0)|0;i=j;j=h+j|0;c=g+(k<<2)|0;if(!(f[c>>2]|0)){c=e+(k<<2)|0;if(!(Bb(a,f[c>>2]|0,o)|0)){i=0;c=23;break}if(!(Ib(a,b+(i<<2)|0,h,f[c>>2]|0)|0)){i=0;c=23;break}}else{if(!(Bb(a,p,o)|0)){i=0;c=23;break}if(!(Bb(a,f[c>>2]|0,m)|0)){i=0;c=23;break}if(i>>>0<j>>>0)do{if(!(Db(a,f[b+(i<<2)>>2]|0,f[c>>2]|0)|0)){i=0;c=23;break b}i=i+1|0}while(i>>>0<j>>>0)}k=k+1|0;if(k>>>0>=l>>>0){i=1;c=23;break}}if((c|0)==23)return i|0;return 0}function sd(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var g=0,h=0;h=a+12|0;g=(d|0)!=0;if(!(Bb(e,f[280]|g&1|(f[h>>2]<<1)+-2,(f[276]|0)+(f[277]|0)+(f[277]|0)|0)|0)){h=0;return h|0}if(g?(Hb(e,d+-1|0)|0)==0:0){h=0;return h|0}a:do if(f[h>>2]|0){g=0;while(1){if(!(Db(e,f[a+152+(g<<2)>>2]|0,c)|0)){g=0;break}g=g+1|0;if(g>>>0>=(f[h>>2]|0)>>>0)break a}return g|0}while(0);d=a+16|0;if(!(Bb(e,(f[d>>2]|0)+-1|0,f[274]|0)|0)){h=0;return h|0}if(!(Db(e,f[a+20>>2]|0,f[275]|0)|0)){h=0;return h|0}b:do if(f[h>>2]|0){g=0;while(1){if(!(Db(e,f[a+24+(g<<2)>>2]|0,f[d>>2]|0)|0)){g=0;break}g=g+1|0;if(g>>>0>=(f[h>>2]|0)>>>0)break b}return g|0}while(0);if(!(Bb(e,f[a>>2]|0,f[278]|0)|0)){h=0;return h|0}if((f[a>>2]|0)>>>0<2){if(!(Bb(e,f[a+4>>2]|0,f[274]|0)|0)){h=0;return h|0}g=f[a>>2]|0;if(g>>>0<2?(c=f[a+8>>2]|0,(rd(e,f[a+280>>2]|0,b,f[h>>2]|0,f[c>>2]|0,f[c+4>>2]|0,f[a+4>>2]|0,(g|0)==1&1)|0)==0):0){h=0;return h|0}}h=1;return h|0}function td(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var g=0;g=f[a>>2]|0;a=(d|0)!=0;if(!(Bb(e,f[278]|a&1,(f[276]|0)+(f[277]|0)+(f[277]|0)|0)|0)){e=0;return e|0}if(a?(Hb(e,d+-1|0)|0)==0:0){e=0;return e|0}if(!b){e=1;return e|0}a=0;while(1){if(!(Db(e,f[g+(a<<2)>>2]|0,c)|0)){a=0;d=8;break}a=a+1|0;if(a>>>0>=b>>>0){a=1;d=8;break}}if((d|0)==8)return a|0;return 0}function ud(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0,f=0,g=0;g=b+-1|0;if(!(b&1)){c=(b|0)/2|0;if((b|0)>1){d=+(g|0);e=0;do{n[a+(e<<2)>>2]=+(e|0)*2.0/d;e=e+1|0}while((e|0)!=(c|0))}else c=0;if((c|0)>=(b|0))return;d=+(g|0);do{n[a+(c<<2)>>2]=2.0-+(c|0)*2.0/d;c=c+1|0}while((c|0)!=(b|0));return}else{f=(g|0)/2|0;if((b|0)<0)c=0;else{d=+(g|0);e=0;while(1){n[a+(e<<2)>>2]=+(e|0)*2.0/d;c=e+1|0;if((e|0)<(f|0))e=c;else break}}if((c|0)>=(b|0))return;d=+(g|0);do{n[a+(c<<2)>>2]=2.0-+(c|0)*2.0/d;c=c+1|0}while((c|0)!=(b|0));return}}function vd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;if((b|0)<=0)return;d=+(b+-1|0);c=0;do{e=+(c|0)/d;e=.6200000047683716-+J(+(e+-.5))*.47999998927116394-+M(+(e*6.283185307179586))*.3799999952316284;n[a+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}function wd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;if((b|0)<=0)return;d=+(b+-1|0);c=0;do{e=+(c|0);e=+M(+(e*12.566370614359172/d))*.07999999821186066+(.41999998688697815-+M(+(e*6.283185307179586/d))*.5);n[a+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}function xd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;if((b|0)<=0)return;d=+(b+-1|0);c=0;do{e=+(c|0);e=+M(+(e*12.566370614359172/d))*.14127999544143677+(.35874998569488525-+M(+(e*6.283185307179586/d))*.488290011882782)-+M(+(e*18.84955592153876/d))*.011680000461637974;n[a+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}function yd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;d=+(b+-1|0)*.5;if((b|0)<=0)return;c=0;do{e=(+(c|0)-d)/d;e=1.0-e*e;n[a+(c<<2)>>2]=e*e;c=c+1|0}while((c|0)!=(b|0));return}function zd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;if((b|0)<=0)return;d=+(b+-1|0);c=0;do{e=+(c|0);e=+M(+(e*25.132741228718345/d))*.03220000118017197+(+M(+(e*12.566370614359172/d))*1.2899999618530273+(1.0-+M(+(e*6.283185307179586/d))*1.9299999475479126)-+M(+(e*18.84955592153876/d))*.3880000114440918);n[a+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}function Ad(a,b,c){a=a|0;b=b|0;c=+c;var d=0,e=0.0,f=0.0;e=+(b+-1|0)*.5;if((b|0)<=0)return;c=e*c;d=0;do{f=(+(d|0)-e)/c;f=+T(+(f*(f*-.5)));n[a+(d<<2)>>2]=f;d=d+1|0}while((d|0)!=(b|0));return}function Bd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;if((b|0)<=0)return;d=+(b+-1|0);c=0;do{e=.5400000214576721-+M(+(+(c|0)*6.283185307179586/d))*.46000000834465027;n[a+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}function Cd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;if((b|0)<=0)return;d=+(b+-1|0);c=0;do{e=.5-+M(+(+(c|0)*6.283185307179586/d))*.5;n[a+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}function Dd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;if((b|0)<=0)return;d=+(b+-1|0);c=0;do{e=+(c|0);e=+M(+(e*12.566370614359172/d))*.09799999743700027+(.4020000100135803-+M(+(e*6.283185307179586/d))*.49799999594688416)-+M(+(e*18.84955592153876/d))*1.0000000474974513e-03;n[a+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}function Ed(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;if((b|0)<=0)return;d=+(b+-1|0);c=0;do{e=+(c|0);e=+M(+(e*12.566370614359172/d))*.13659949600696564+(.36358189582824707-+M(+(e*6.283185307179586/d))*.48917749524116516)-+M(+(e*18.84955592153876/d))*.010641099885106087;n[a+(c<<2)>>2]=e;c=c+1|0}while((c|0)!=(b|0));return}function Fd(a,b){a=a|0;b=b|0;var c=0;if((b|0)<=0)return;c=0;do{n[a+(c<<2)>>2]=1.0;c=c+1|0}while((c|0)!=(b|0));return}function Gd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0.0,f=0;if(!(b&1)){if((b|0)<2)c=1;else{e=+(b|0)+1.0;c=(b>>>1)+1|0;d=1;do{n[a+(d+-1<<2)>>2]=+(d|0)*2.0/e;d=d+1|0}while((d|0)!=(c|0))}if((c|0)>(b|0))return;e=+(b|0)+1.0;while(1){n[a+(c+-1<<2)>>2]=+((b-c<<1)+2|0)/e;if((c|0)<(b|0))c=c+1|0;else break}return}else{f=(b+1|0)/2|0;if((b|0)<1)c=1;else{e=+(b|0)+1.0;d=1;while(1){n[a+(d+-1<<2)>>2]=+(d|0)*2.0/e;c=d+1|0;if((d|0)<(f|0))d=c;else break}}if((c|0)>(b|0))return;e=+(b|0)+1.0;while(1){n[a+(c+-1<<2)>>2]=+((b-c<<1)+2|0)/e;if((c|0)<(b|0))c=c+1|0;else break}return}}function Hd(a,b,c){a=a|0;b=b|0;c=+c;var d=0,e=0,f=0,g=0.0;if(c<=0.0){if((b|0)<=0)return;d=0;do{n[a+(d<<2)>>2]=1.0;d=d+1|0}while((d|0)!=(b|0));return}if(c>=1.0){if((b|0)<=0)return;c=+(b+-1|0);d=0;do{g=.5-+M(+(+(d|0)*6.283185307179586/c))*.5;n[a+(d<<2)>>2]=g;d=d+1|0}while((d|0)!=(b|0));return}e=~~(c*.5*+(b|0));f=e+-1|0;if((b|0)>0){d=0;do{n[a+(d<<2)>>2]=1.0;d=d+1|0}while((d|0)!=(b|0))}if((e|0)<=1)return;c=+(f|0);b=b+-1+(1-e)|0;d=0;do{g=.5-+M(+(+(d|0)*3.141592653589793/c))*.5;n[a+(d<<2)>>2]=g;g=.5-+M(+(+(d+f|0)*3.141592653589793/c))*.5;n[a+(b+d<<2)>>2]=g;d=d+1|0}while((d|0)!=(e|0));return}function Id(a,b,c,d,e){a=a|0;b=b|0;c=+c;d=+d;e=+e;var f=0,g=0,h=0,i=0,j=0,k=0.0;while(1)if(!(c<=0.0))if(!(c>=1.0))break;else c=.949999988079071;else c=.05000000074505806;k=+(b|0);h=~~(k*d);j=~~(k*e);g=~~(c*.5*+(j-h|0));if((h|0)>0&(b|0)>0){i=0-h|0;f=0-b|0;cf(a|0,0,W(i>>>0>f>>>0?i:f,-4)|0)|0;f=0;do f=f+1|0;while((f|0)<(h|0)&(f|0)<(b|0))}else f=0;i=g+h|0;if((f|0)<(i|0)&(f|0)<(b|0)){c=+(g|0);h=1;while(1){k=.5-+M(+(+(h|0)*3.141592653589793/c))*.5;n[a+(f<<2)>>2]=k;f=f+1|0;if((f|0)<(i|0)&(f|0)<(b|0))h=h+1|0;else break}}h=j-g|0;if((f|0)<(h|0)&(f|0)<(b|0))do{n[a+(f<<2)>>2]=1.0;f=f+1|0}while((f|0)<(h|0)&(f|0)<(b|0));if((f|0)<(j|0)&(f|0)<(b|0)){c=+(g|0);while(1){k=.5-+M(+(+(g|0)*3.141592653589793/c))*.5;n[a+(f<<2)>>2]=k;f=f+1|0;if((f|0)<(j|0)&(f|0)<(b|0))g=g+-1|0;else break}}if((f|0)>=(b|0))return;cf(a+(f<<2)|0,0,b-f<<2|0)|0;return}function Jd(a,b,c,d,e){a=a|0;b=b|0;c=+c;d=+d;e=+e;var f=0,g=0,h=0,i=0,j=0,k=0,l=0.0;while(1)if(!(c<=0.0))if(!(c>=1.0))break;else c=.949999988079071;else c=.05000000074505806;l=+(b|0);j=~~(l*d);k=~~(l*e);e=c*.5;i=~~(e*+(j|0));g=~~(e*+(b-k|0));if((i|0)>0&(b|0)>0){c=+(i|0);h=1;f=0;while(1){l=.5-+M(+(+(h|0)*3.141592653589793/c))*.5;n[a+(f<<2)>>2]=l;f=f+1|0;if((f|0)<(i|0)&(f|0)<(b|0))h=h+1|0;else break}}else f=0;h=j-i|0;if((f|0)<(h|0)&(f|0)<(b|0))do{n[a+(f<<2)>>2]=1.0;f=f+1|0}while((f|0)<(h|0)&(f|0)<(b|0));if((f|0)<(j|0)&(f|0)<(b|0)){c=+(i|0);h=i;while(1){l=.5-+M(+(+(h|0)*3.141592653589793/c))*.5;n[a+(f<<2)>>2]=l;f=f+1|0;if((f|0)<(j|0)&(f|0)<(b|0))h=h+-1|0;else break}}if((f|0)<(k|0)&(f|0)<(b|0)){i=f-b|0;j=f-k|0;cf(a+(f<<2)|0,0,W(i>>>0>j>>>0?i:j,-4)|0)|0;do f=f+1|0;while((f|0)<(k|0)&(f|0)<(b|0))}i=g+k|0;if((f|0)<(i|0)&(f|0)<(b|0)){c=+(g|0);h=1;while(1){l=.5-+M(+(+(h|0)*3.141592653589793/c))*.5;n[a+(f<<2)>>2]=l;f=f+1|0;if((f|0)<(i|0)&(f|0)<(b|0))h=h+1|0;else break}}h=b-g|0;if((f|0)<(h|0)&(f|0)<(b|0))do{n[a+(f<<2)>>2]=1.0;f=f+1|0}while((f|0)<(h|0)&(f|0)<(b|0));if((f|0)>=(b|0))return;c=+(g|0);while(1){l=.5-+M(+(+(g|0)*3.141592653589793/c))*.5;n[a+(f<<2)>>2]=l;f=f+1|0;if((f|0)==(b|0))break;else g=g+-1|0}return}function Kd(a,b){a=a|0;b=b|0;var c=0,d=0.0,e=0.0;d=+(b+-1|0)*.5;if((b|0)<=0)return;c=0;do{e=(+(c|0)-d)/d;n[a+(c<<2)>>2]=1.0-e*e;c=c+1|0}while((c|0)!=(b|0));return}function Ld(a){a=a|0;var b=0,c=0,d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,w=0,x=0;x=u;u=u+16|0;n=x;do if(a>>>0<245){k=a>>>0<11?16:a+11&-8;a=k>>>3;m=f[870]|0;c=m>>>a;if(c&3|0){b=(c&1^1)+a|0;a=3520+(b<<1<<2)|0;c=a+8|0;d=f[c>>2]|0;e=d+8|0;g=f[e>>2]|0;if((g|0)==(a|0))f[870]=m&~(1<<b);else{f[g+12>>2]=a;f[c>>2]=g}w=b<<3;f[d+4>>2]=w|3;w=d+w+4|0;f[w>>2]=f[w>>2]|1;w=e;u=x;return w|0}l=f[872]|0;if(k>>>0>l>>>0){if(c|0){b=2<<a;b=c<<a&(b|0-b);b=(b&0-b)+-1|0;i=b>>>12&16;b=b>>>i;c=b>>>5&8;b=b>>>c;g=b>>>2&4;b=b>>>g;a=b>>>1&2;b=b>>>a;d=b>>>1&1;d=(c|i|g|a|d)+(b>>>d)|0;b=3520+(d<<1<<2)|0;a=b+8|0;g=f[a>>2]|0;i=g+8|0;c=f[i>>2]|0;if((c|0)==(b|0)){a=m&~(1<<d);f[870]=a}else{f[c+12>>2]=b;f[a>>2]=c;a=m}w=d<<3;h=w-k|0;f[g+4>>2]=k|3;e=g+k|0;f[e+4>>2]=h|1;f[g+w>>2]=h;if(l|0){d=f[875]|0;b=l>>>3;c=3520+(b<<1<<2)|0;b=1<<b;if(!(a&b)){f[870]=a|b;b=c;a=c+8|0}else{a=c+8|0;b=f[a>>2]|0}f[a>>2]=d;f[b+12>>2]=d;f[d+8>>2]=b;f[d+12>>2]=c}f[872]=h;f[875]=e;w=i;u=x;return w|0}g=f[871]|0;if(g){c=(g&0-g)+-1|0;e=c>>>12&16;c=c>>>e;d=c>>>5&8;c=c>>>d;h=c>>>2&4;c=c>>>h;i=c>>>1&2;c=c>>>i;j=c>>>1&1;j=f[3784+((d|e|h|i|j)+(c>>>j)<<2)>>2]|0;c=j;i=j;j=(f[j+4>>2]&-8)-k|0;while(1){a=f[c+16>>2]|0;if(!a){a=f[c+20>>2]|0;if(!a)break}h=(f[a+4>>2]&-8)-k|0;e=h>>>0<j>>>0;c=a;i=e?a:i;j=e?h:j}h=i+k|0;if(h>>>0>i>>>0){e=f[i+24>>2]|0;b=f[i+12>>2]|0;do if((b|0)==(i|0)){a=i+20|0;b=f[a>>2]|0;if(!b){a=i+16|0;b=f[a>>2]|0;if(!b){c=0;break}}while(1){d=b+20|0;c=f[d>>2]|0;if(!c){d=b+16|0;c=f[d>>2]|0;if(!c)break;else{b=c;a=d}}else{b=c;a=d}}f[a>>2]=0;c=b}else{c=f[i+8>>2]|0;f[c+12>>2]=b;f[b+8>>2]=c;c=b}while(0);do if(e|0){b=f[i+28>>2]|0;a=3784+(b<<2)|0;if((i|0)==(f[a>>2]|0)){f[a>>2]=c;if(!c){f[871]=g&~(1<<b);break}}else{w=e+16|0;f[((f[w>>2]|0)==(i|0)?w:e+20|0)>>2]=c;if(!c)break}f[c+24>>2]=e;b=f[i+16>>2]|0;if(b|0){f[c+16>>2]=b;f[b+24>>2]=c}b=f[i+20>>2]|0;if(b|0){f[c+20>>2]=b;f[b+24>>2]=c}}while(0);if(j>>>0<16){w=j+k|0;f[i+4>>2]=w|3;w=i+w+4|0;f[w>>2]=f[w>>2]|1}else{f[i+4>>2]=k|3;f[h+4>>2]=j|1;f[h+j>>2]=j;if(l|0){d=f[875]|0;b=l>>>3;c=3520+(b<<1<<2)|0;b=1<<b;if(!(b&m)){f[870]=b|m;b=c;a=c+8|0}else{a=c+8|0;b=f[a>>2]|0}f[a>>2]=d;f[b+12>>2]=d;f[d+8>>2]=b;f[d+12>>2]=c}f[872]=j;f[875]=h}w=i+8|0;u=x;return w|0}else m=k}else m=k}else m=k}else if(a>>>0<=4294967231){a=a+11|0;k=a&-8;d=f[871]|0;if(d){e=0-k|0;a=a>>>8;if(a)if(k>>>0>16777215)j=31;else{m=(a+1048320|0)>>>16&8;q=a<<m;i=(q+520192|0)>>>16&4;q=q<<i;j=(q+245760|0)>>>16&2;j=14-(i|m|j)+(q<<j>>>15)|0;j=k>>>(j+7|0)&1|j<<1}else j=0;c=f[3784+(j<<2)>>2]|0;a:do if(!c){c=0;a=0;q=61}else{a=0;i=k<<((j|0)==31?0:25-(j>>>1)|0);g=0;while(1){h=(f[c+4>>2]&-8)-k|0;if(h>>>0<e>>>0)if(!h){a=c;e=0;q=65;break a}else{a=c;e=h}q=f[c+20>>2]|0;c=f[c+16+(i>>>31<<2)>>2]|0;g=(q|0)==0|(q|0)==(c|0)?g:q;if(!c){c=g;q=61;break}else i=i<<1}}while(0);if((q|0)==61){if((c|0)==0&(a|0)==0){a=2<<j;a=(a|0-a)&d;if(!a){m=k;break}m=(a&0-a)+-1|0;h=m>>>12&16;m=m>>>h;g=m>>>5&8;m=m>>>g;i=m>>>2&4;m=m>>>i;j=m>>>1&2;m=m>>>j;c=m>>>1&1;a=0;c=f[3784+((g|h|i|j|c)+(m>>>c)<<2)>>2]|0}if(!c){i=a;h=e}else q=65}if((q|0)==65){g=c;while(1){m=(f[g+4>>2]&-8)-k|0;c=m>>>0<e>>>0;e=c?m:e;a=c?g:a;c=f[g+16>>2]|0;if(!c)c=f[g+20>>2]|0;if(!c){i=a;h=e;break}else g=c}}if(((i|0)!=0?h>>>0<((f[872]|0)-k|0)>>>0:0)?(l=i+k|0,l>>>0>i>>>0):0){g=f[i+24>>2]|0;b=f[i+12>>2]|0;do if((b|0)==(i|0)){a=i+20|0;b=f[a>>2]|0;if(!b){a=i+16|0;b=f[a>>2]|0;if(!b){b=0;break}}while(1){e=b+20|0;c=f[e>>2]|0;if(!c){e=b+16|0;c=f[e>>2]|0;if(!c)break;else{b=c;a=e}}else{b=c;a=e}}f[a>>2]=0}else{w=f[i+8>>2]|0;f[w+12>>2]=b;f[b+8>>2]=w}while(0);do if(g){a=f[i+28>>2]|0;c=3784+(a<<2)|0;if((i|0)==(f[c>>2]|0)){f[c>>2]=b;if(!b){d=d&~(1<<a);f[871]=d;break}}else{w=g+16|0;f[((f[w>>2]|0)==(i|0)?w:g+20|0)>>2]=b;if(!b)break}f[b+24>>2]=g;a=f[i+16>>2]|0;if(a|0){f[b+16>>2]=a;f[a+24>>2]=b}a=f[i+20>>2]|0;if(a){f[b+20>>2]=a;f[a+24>>2]=b}}while(0);b:do if(h>>>0<16){w=h+k|0;f[i+4>>2]=w|3;w=i+w+4|0;f[w>>2]=f[w>>2]|1}else{f[i+4>>2]=k|3;f[l+4>>2]=h|1;f[l+h>>2]=h;b=h>>>3;if(h>>>0<256){c=3520+(b<<1<<2)|0;a=f[870]|0;b=1<<b;if(!(a&b)){f[870]=a|b;b=c;a=c+8|0}else{a=c+8|0;b=f[a>>2]|0}f[a>>2]=l;f[b+12>>2]=l;f[l+8>>2]=b;f[l+12>>2]=c;break}b=h>>>8;if(b)if(h>>>0>16777215)c=31;else{v=(b+1048320|0)>>>16&8;w=b<<v;t=(w+520192|0)>>>16&4;w=w<<t;c=(w+245760|0)>>>16&2;c=14-(t|v|c)+(w<<c>>>15)|0;c=h>>>(c+7|0)&1|c<<1}else c=0;b=3784+(c<<2)|0;f[l+28>>2]=c;a=l+16|0;f[a+4>>2]=0;f[a>>2]=0;a=1<<c;if(!(d&a)){f[871]=d|a;f[b>>2]=l;f[l+24>>2]=b;f[l+12>>2]=l;f[l+8>>2]=l;break}b=f[b>>2]|0;c:do if((f[b+4>>2]&-8|0)!=(h|0)){d=h<<((c|0)==31?0:25-(c>>>1)|0);while(1){c=b+16+(d>>>31<<2)|0;a=f[c>>2]|0;if(!a)break;if((f[a+4>>2]&-8|0)==(h|0)){b=a;break c}else{d=d<<1;b=a}}f[c>>2]=l;f[l+24>>2]=b;f[l+12>>2]=l;f[l+8>>2]=l;break b}while(0);v=b+8|0;w=f[v>>2]|0;f[w+12>>2]=l;f[v>>2]=l;f[l+8>>2]=w;f[l+12>>2]=b;f[l+24>>2]=0}while(0);w=i+8|0;u=x;return w|0}else m=k}else m=k}else m=-1;while(0);c=f[872]|0;if(c>>>0>=m>>>0){b=c-m|0;a=f[875]|0;if(b>>>0>15){w=a+m|0;f[875]=w;f[872]=b;f[w+4>>2]=b|1;f[a+c>>2]=b;f[a+4>>2]=m|3}else{f[872]=0;f[875]=0;f[a+4>>2]=c|3;w=a+c+4|0;f[w>>2]=f[w>>2]|1}w=a+8|0;u=x;return w|0}h=f[873]|0;if(h>>>0>m>>>0){t=h-m|0;f[873]=t;w=f[876]|0;v=w+m|0;f[876]=v;f[v+4>>2]=t|1;f[w+4>>2]=m|3;w=w+8|0;u=x;return w|0}if(!(f[988]|0)){f[990]=4096;f[989]=4096;f[991]=-1;f[992]=-1;f[993]=0;f[981]=0;f[988]=n&-16^1431655768;a=4096}else a=f[990]|0;i=m+48|0;j=m+47|0;g=a+j|0;e=0-a|0;k=g&e;if(k>>>0<=m>>>0){w=0;u=x;return w|0}a=f[980]|0;if(a|0?(l=f[978]|0,n=l+k|0,n>>>0<=l>>>0|n>>>0>a>>>0):0){w=0;u=x;return w|0}d:do if(!(f[981]&4)){c=f[876]|0;e:do if(c){d=3928;while(1){n=f[d>>2]|0;if(n>>>0<=c>>>0?(n+(f[d+4>>2]|0)|0)>>>0>c>>>0:0)break;a=f[d+8>>2]|0;if(!a){q=128;break e}else d=a}b=g-h&e;if(b>>>0<2147483647){a=ef(b|0)|0;if((a|0)==((f[d>>2]|0)+(f[d+4>>2]|0)|0)){if((a|0)!=(-1|0)){h=b;g=a;q=145;break d}}else{d=a;q=136}}else b=0}else q=128;while(0);do if((q|0)==128){c=ef(0)|0;if((c|0)!=(-1|0)?(b=c,o=f[989]|0,p=o+-1|0,b=((p&b|0)==0?0:(p+b&0-o)-b|0)+k|0,o=f[978]|0,p=b+o|0,b>>>0>m>>>0&b>>>0<2147483647):0){n=f[980]|0;if(n|0?p>>>0<=o>>>0|p>>>0>n>>>0:0){b=0;break}a=ef(b|0)|0;if((a|0)==(c|0)){h=b;g=c;q=145;break d}else{d=a;q=136}}else b=0}while(0);do if((q|0)==136){c=0-b|0;if(!(i>>>0>b>>>0&(b>>>0<2147483647&(d|0)!=(-1|0))))if((d|0)==(-1|0)){b=0;break}else{h=b;g=d;q=145;break d}a=f[990]|0;a=j-b+a&0-a;if(a>>>0>=2147483647){h=b;g=d;q=145;break d}if((ef(a|0)|0)==(-1|0)){ef(c|0)|0;b=0;break}else{h=a+b|0;g=d;q=145;break d}}while(0);f[981]=f[981]|4;q=143}else{b=0;q=143}while(0);if(((q|0)==143?k>>>0<2147483647:0)?(t=ef(k|0)|0,p=ef(0)|0,r=p-t|0,s=r>>>0>(m+40|0)>>>0,!((t|0)==(-1|0)|s^1|t>>>0<p>>>0&((t|0)!=(-1|0)&(p|0)!=(-1|0))^1)):0){h=s?r:b;g=t;q=145}if((q|0)==145){b=(f[978]|0)+h|0;f[978]=b;if(b>>>0>(f[979]|0)>>>0)f[979]=b;j=f[876]|0;f:do if(j){b=3928;while(1){a=f[b>>2]|0;c=f[b+4>>2]|0;if((g|0)==(a+c|0)){q=154;break}d=f[b+8>>2]|0;if(!d)break;else b=d}if(((q|0)==154?(v=b+4|0,(f[b+12>>2]&8|0)==0):0)?g>>>0>j>>>0&a>>>0<=j>>>0:0){f[v>>2]=c+h;w=(f[873]|0)+h|0;t=j+8|0;t=(t&7|0)==0?0:0-t&7;v=j+t|0;t=w-t|0;f[876]=v;f[873]=t;f[v+4>>2]=t|1;f[j+w+4>>2]=40;f[877]=f[992];break}if(g>>>0<(f[874]|0)>>>0)f[874]=g;c=g+h|0;b=3928;while(1){if((f[b>>2]|0)==(c|0)){q=162;break}a=f[b+8>>2]|0;if(!a)break;else b=a}if((q|0)==162?(f[b+12>>2]&8|0)==0:0){f[b>>2]=g;l=b+4|0;f[l>>2]=(f[l>>2]|0)+h;l=g+8|0;l=g+((l&7|0)==0?0:0-l&7)|0;b=c+8|0;b=c+((b&7|0)==0?0:0-b&7)|0;k=l+m|0;i=b-l-m|0;f[l+4>>2]=m|3;g:do if((j|0)==(b|0)){w=(f[873]|0)+i|0;f[873]=w;f[876]=k;f[k+4>>2]=w|1}else{if((f[875]|0)==(b|0)){w=(f[872]|0)+i|0;f[872]=w;f[875]=k;f[k+4>>2]=w|1;f[k+w>>2]=w;break}a=f[b+4>>2]|0;if((a&3|0)==1){h=a&-8;d=a>>>3;h:do if(a>>>0<256){a=f[b+8>>2]|0;c=f[b+12>>2]|0;if((c|0)==(a|0)){f[870]=f[870]&~(1<<d);break}else{f[a+12>>2]=c;f[c+8>>2]=a;break}}else{g=f[b+24>>2]|0;a=f[b+12>>2]|0;do if((a|0)==(b|0)){c=b+16|0;d=c+4|0;a=f[d>>2]|0;if(!a){a=f[c>>2]|0;if(!a){a=0;break}}else c=d;while(1){e=a+20|0;d=f[e>>2]|0;if(!d){e=a+16|0;d=f[e>>2]|0;if(!d)break;else{a=d;c=e}}else{a=d;c=e}}f[c>>2]=0}else{w=f[b+8>>2]|0;f[w+12>>2]=a;f[a+8>>2]=w}while(0);if(!g)break;c=f[b+28>>2]|0;d=3784+(c<<2)|0;do if((f[d>>2]|0)!=(b|0)){w=g+16|0;f[((f[w>>2]|0)==(b|0)?w:g+20|0)>>2]=a;if(!a)break h}else{f[d>>2]=a;if(a|0)break;f[871]=f[871]&~(1<<c);break h}while(0);f[a+24>>2]=g;c=b+16|0;d=f[c>>2]|0;if(d|0){f[a+16>>2]=d;f[d+24>>2]=a}c=f[c+4>>2]|0;if(!c)break;f[a+20>>2]=c;f[c+24>>2]=a}while(0);b=b+h|0;e=h+i|0}else e=i;b=b+4|0;f[b>>2]=f[b>>2]&-2;f[k+4>>2]=e|1;f[k+e>>2]=e;b=e>>>3;if(e>>>0<256){c=3520+(b<<1<<2)|0;a=f[870]|0;b=1<<b;if(!(a&b)){f[870]=a|b;b=c;a=c+8|0}else{a=c+8|0;b=f[a>>2]|0}f[a>>2]=k;f[b+12>>2]=k;f[k+8>>2]=b;f[k+12>>2]=c;break}b=e>>>8;do if(!b)d=0;else{if(e>>>0>16777215){d=31;break}v=(b+1048320|0)>>>16&8;w=b<<v;t=(w+520192|0)>>>16&4;w=w<<t;d=(w+245760|0)>>>16&2;d=14-(t|v|d)+(w<<d>>>15)|0;d=e>>>(d+7|0)&1|d<<1}while(0);b=3784+(d<<2)|0;f[k+28>>2]=d;a=k+16|0;f[a+4>>2]=0;f[a>>2]=0;a=f[871]|0;c=1<<d;if(!(a&c)){f[871]=a|c;f[b>>2]=k;f[k+24>>2]=b;f[k+12>>2]=k;f[k+8>>2]=k;break}b=f[b>>2]|0;i:do if((f[b+4>>2]&-8|0)!=(e|0)){d=e<<((d|0)==31?0:25-(d>>>1)|0);while(1){c=b+16+(d>>>31<<2)|0;a=f[c>>2]|0;if(!a)break;if((f[a+4>>2]&-8|0)==(e|0)){b=a;break i}else{d=d<<1;b=a}}f[c>>2]=k;f[k+24>>2]=b;f[k+12>>2]=k;f[k+8>>2]=k;break g}while(0);v=b+8|0;w=f[v>>2]|0;f[w+12>>2]=k;f[v>>2]=k;f[k+8>>2]=w;f[k+12>>2]=b;f[k+24>>2]=0}while(0);w=l+8|0;u=x;return w|0}b=3928;while(1){a=f[b>>2]|0;if(a>>>0<=j>>>0?(w=a+(f[b+4>>2]|0)|0,w>>>0>j>>>0):0)break;b=f[b+8>>2]|0}e=w+-47|0;a=e+8|0;a=e+((a&7|0)==0?0:0-a&7)|0;e=j+16|0;a=a>>>0<e>>>0?j:a;b=a+8|0;c=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;v=g+t|0;t=c-t|0;f[876]=v;f[873]=t;f[v+4>>2]=t|1;f[g+c+4>>2]=40;f[877]=f[992];c=a+4|0;f[c>>2]=27;f[b>>2]=f[982];f[b+4>>2]=f[983];f[b+8>>2]=f[984];f[b+12>>2]=f[985];f[982]=g;f[983]=h;f[985]=0;f[984]=b;b=a+24|0;do{v=b;b=b+4|0;f[b>>2]=7}while((v+8|0)>>>0<w>>>0);if((a|0)!=(j|0)){g=a-j|0;f[c>>2]=f[c>>2]&-2;f[j+4>>2]=g|1;f[a>>2]=g;b=g>>>3;if(g>>>0<256){c=3520+(b<<1<<2)|0;a=f[870]|0;b=1<<b;if(!(a&b)){f[870]=a|b;b=c;a=c+8|0}else{a=c+8|0;b=f[a>>2]|0}f[a>>2]=j;f[b+12>>2]=j;f[j+8>>2]=b;f[j+12>>2]=c;break}b=g>>>8;if(b)if(g>>>0>16777215)d=31;else{v=(b+1048320|0)>>>16&8;w=b<<v;t=(w+520192|0)>>>16&4;w=w<<t;d=(w+245760|0)>>>16&2;d=14-(t|v|d)+(w<<d>>>15)|0;d=g>>>(d+7|0)&1|d<<1}else d=0;c=3784+(d<<2)|0;f[j+28>>2]=d;f[j+20>>2]=0;f[e>>2]=0;b=f[871]|0;a=1<<d;if(!(b&a)){f[871]=b|a;f[c>>2]=j;f[j+24>>2]=c;f[j+12>>2]=j;f[j+8>>2]=j;break}b=f[c>>2]|0;j:do if((f[b+4>>2]&-8|0)!=(g|0)){d=g<<((d|0)==31?0:25-(d>>>1)|0);while(1){c=b+16+(d>>>31<<2)|0;a=f[c>>2]|0;if(!a)break;if((f[a+4>>2]&-8|0)==(g|0)){b=a;break j}else{d=d<<1;b=a}}f[c>>2]=j;f[j+24>>2]=b;f[j+12>>2]=j;f[j+8>>2]=j;break f}while(0);v=b+8|0;w=f[v>>2]|0;f[w+12>>2]=j;f[v>>2]=j;f[j+8>>2]=w;f[j+12>>2]=b;f[j+24>>2]=0}}else{w=f[874]|0;if((w|0)==0|g>>>0<w>>>0)f[874]=g;f[982]=g;f[983]=h;f[985]=0;f[879]=f[988];f[878]=-1;f[883]=3520;f[882]=3520;f[885]=3528;f[884]=3528;f[887]=3536;f[886]=3536;f[889]=3544;f[888]=3544;f[891]=3552;f[890]=3552;f[893]=3560;f[892]=3560;f[895]=3568;f[894]=3568;f[897]=3576;f[896]=3576;f[899]=3584;f[898]=3584;f[901]=3592;f[900]=3592;f[903]=3600;f[902]=3600;f[905]=3608;f[904]=3608;f[907]=3616;f[906]=3616;f[909]=3624;f[908]=3624;f[911]=3632;f[910]=3632;f[913]=3640;f[912]=3640;f[915]=3648;f[914]=3648;f[917]=3656;f[916]=3656;f[919]=3664;f[918]=3664;f[921]=3672;f[920]=3672;f[923]=3680;f[922]=3680;f[925]=3688;f[924]=3688;f[927]=3696;f[926]=3696;f[929]=3704;f[928]=3704;f[931]=3712;f[930]=3712;f[933]=3720;f[932]=3720;f[935]=3728;f[934]=3728;f[937]=3736;f[936]=3736;f[939]=3744;f[938]=3744;f[941]=3752;f[940]=3752;f[943]=3760;f[942]=3760;f[945]=3768;f[944]=3768;w=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;v=g+t|0;t=w-t|0;f[876]=v;f[873]=t;f[v+4>>2]=t|1;f[g+w+4>>2]=40;f[877]=f[992]}while(0);b=f[873]|0;if(b>>>0>m>>>0){t=b-m|0;f[873]=t;w=f[876]|0;v=w+m|0;f[876]=v;f[v+4>>2]=t|1;f[w+4>>2]=m|3;w=w+8|0;u=x;return w|0}}w=Vd()|0;f[w>>2]=12;w=0;u=x;return w|0}function Md(a){a=a|0;var b=0,c=0,d=0,e=0,g=0,h=0,i=0,j=0;if(!a)return;c=a+-8|0;e=f[874]|0;a=f[a+-4>>2]|0;b=a&-8;j=c+b|0;do if(!(a&1)){d=f[c>>2]|0;if(!(a&3))return;h=c+(0-d)|0;g=d+b|0;if(h>>>0<e>>>0)return;if((f[875]|0)==(h|0)){a=j+4|0;b=f[a>>2]|0;if((b&3|0)!=3){i=h;b=g;break}f[872]=g;f[a>>2]=b&-2;f[h+4>>2]=g|1;f[h+g>>2]=g;return}c=d>>>3;if(d>>>0<256){a=f[h+8>>2]|0;b=f[h+12>>2]|0;if((b|0)==(a|0)){f[870]=f[870]&~(1<<c);i=h;b=g;break}else{f[a+12>>2]=b;f[b+8>>2]=a;i=h;b=g;break}}e=f[h+24>>2]|0;a=f[h+12>>2]|0;do if((a|0)==(h|0)){b=h+16|0;c=b+4|0;a=f[c>>2]|0;if(!a){a=f[b>>2]|0;if(!a){a=0;break}}else b=c;while(1){d=a+20|0;c=f[d>>2]|0;if(!c){d=a+16|0;c=f[d>>2]|0;if(!c)break;else{a=c;b=d}}else{a=c;b=d}}f[b>>2]=0}else{i=f[h+8>>2]|0;f[i+12>>2]=a;f[a+8>>2]=i}while(0);if(e){b=f[h+28>>2]|0;c=3784+(b<<2)|0;if((f[c>>2]|0)==(h|0)){f[c>>2]=a;if(!a){f[871]=f[871]&~(1<<b);i=h;b=g;break}}else{i=e+16|0;f[((f[i>>2]|0)==(h|0)?i:e+20|0)>>2]=a;if(!a){i=h;b=g;break}}f[a+24>>2]=e;b=h+16|0;c=f[b>>2]|0;if(c|0){f[a+16>>2]=c;f[c+24>>2]=a}b=f[b+4>>2]|0;if(b){f[a+20>>2]=b;f[b+24>>2]=a;i=h;b=g}else{i=h;b=g}}else{i=h;b=g}}else{i=c;h=c}while(0);if(h>>>0>=j>>>0)return;a=j+4|0;d=f[a>>2]|0;if(!(d&1))return;if(!(d&2)){if((f[876]|0)==(j|0)){j=(f[873]|0)+b|0;f[873]=j;f[876]=i;f[i+4>>2]=j|1;if((i|0)!=(f[875]|0))return;f[875]=0;f[872]=0;return}if((f[875]|0)==(j|0)){j=(f[872]|0)+b|0;f[872]=j;f[875]=h;f[i+4>>2]=j|1;f[h+j>>2]=j;return}e=(d&-8)+b|0;c=d>>>3;do if(d>>>0<256){b=f[j+8>>2]|0;a=f[j+12>>2]|0;if((a|0)==(b|0)){f[870]=f[870]&~(1<<c);break}else{f[b+12>>2]=a;f[a+8>>2]=b;break}}else{g=f[j+24>>2]|0;a=f[j+12>>2]|0;do if((a|0)==(j|0)){b=j+16|0;c=b+4|0;a=f[c>>2]|0;if(!a){a=f[b>>2]|0;if(!a){c=0;break}}else b=c;while(1){d=a+20|0;c=f[d>>2]|0;if(!c){d=a+16|0;c=f[d>>2]|0;if(!c)break;else{a=c;b=d}}else{a=c;b=d}}f[b>>2]=0;c=a}else{c=f[j+8>>2]|0;f[c+12>>2]=a;f[a+8>>2]=c;c=a}while(0);if(g|0){a=f[j+28>>2]|0;b=3784+(a<<2)|0;if((f[b>>2]|0)==(j|0)){f[b>>2]=c;if(!c){f[871]=f[871]&~(1<<a);break}}else{d=g+16|0;f[((f[d>>2]|0)==(j|0)?d:g+20|0)>>2]=c;if(!c)break}f[c+24>>2]=g;a=j+16|0;b=f[a>>2]|0;if(b|0){f[c+16>>2]=b;f[b+24>>2]=c}a=f[a+4>>2]|0;if(a|0){f[c+20>>2]=a;f[a+24>>2]=c}}}while(0);f[i+4>>2]=e|1;f[h+e>>2]=e;if((i|0)==(f[875]|0)){f[872]=e;return}}else{f[a>>2]=d&-2;f[i+4>>2]=b|1;f[h+b>>2]=b;e=b}a=e>>>3;if(e>>>0<256){c=3520+(a<<1<<2)|0;b=f[870]|0;a=1<<a;if(!(b&a)){f[870]=b|a;a=c;b=c+8|0}else{b=c+8|0;a=f[b>>2]|0}f[b>>2]=i;f[a+12>>2]=i;f[i+8>>2]=a;f[i+12>>2]=c;return}a=e>>>8;if(a)if(e>>>0>16777215)d=31;else{h=(a+1048320|0)>>>16&8;j=a<<h;g=(j+520192|0)>>>16&4;j=j<<g;d=(j+245760|0)>>>16&2;d=14-(g|h|d)+(j<<d>>>15)|0;d=e>>>(d+7|0)&1|d<<1}else d=0;a=3784+(d<<2)|0;f[i+28>>2]=d;f[i+20>>2]=0;f[i+16>>2]=0;b=f[871]|0;c=1<<d;a:do if(!(b&c)){f[871]=b|c;f[a>>2]=i;f[i+24>>2]=a;f[i+12>>2]=i;f[i+8>>2]=i}else{a=f[a>>2]|0;b:do if((f[a+4>>2]&-8|0)!=(e|0)){d=e<<((d|0)==31?0:25-(d>>>1)|0);while(1){c=a+16+(d>>>31<<2)|0;b=f[c>>2]|0;if(!b)break;if((f[b+4>>2]&-8|0)==(e|0)){a=b;break b}else{d=d<<1;a=b}}f[c>>2]=i;f[i+24>>2]=a;f[i+12>>2]=i;f[i+8>>2]=i;break a}while(0);h=a+8|0;j=f[h>>2]|0;f[j+12>>2]=i;f[h>>2]=i;f[i+8>>2]=j;f[i+12>>2]=a;f[i+24>>2]=0}while(0);j=(f[878]|0)+-1|0;f[878]=j;if(j|0)return;a=3936;while(1){a=f[a>>2]|0;if(!a)break;else a=a+8|0}f[878]=-1;return}function Nd(a,b){a=a|0;b=b|0;var c=0;if(a){c=W(b,a)|0;if((b|a)>>>0>65535)c=((c>>>0)/(a>>>0)|0|0)==(b|0)?c:-1}else c=0;a=Ld(c)|0;if(!a)return a|0;if(!(f[a+-4>>2]&3))return a|0;cf(a|0,0,c|0)|0;return a|0}function Od(a,b){a=a|0;b=b|0;var c=0,d=0;if(!a){b=Ld(b)|0;return b|0}if(b>>>0>4294967231){b=Vd()|0;f[b>>2]=12;b=0;return b|0}c=Pd(a+-8|0,b>>>0<11?16:b+11&-8)|0;if(c|0){b=c+8|0;return b|0}c=Ld(b)|0;if(!c){b=0;return b|0}d=f[a+-4>>2]|0;d=(d&-8)-((d&3|0)==0?8:4)|0;af(c|0,a|0,(d>>>0<b>>>0?d:b)|0)|0;Md(a);b=c;return b|0}function Pd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;l=a+4|0;m=f[l>>2]|0;c=m&-8;i=a+c|0;if(!(m&3)){if(b>>>0<256){a=0;return a|0}if(c>>>0>=(b+4|0)>>>0?(c-b|0)>>>0<=f[990]<<1>>>0:0)return a|0;a=0;return a|0}if(c>>>0>=b>>>0){c=c-b|0;if(c>>>0<=15)return a|0;k=a+b|0;f[l>>2]=m&1|b|2;f[k+4>>2]=c|3;m=i+4|0;f[m>>2]=f[m>>2]|1;Qd(k,c);return a|0}if((f[876]|0)==(i|0)){k=(f[873]|0)+c|0;c=k-b|0;d=a+b|0;if(k>>>0<=b>>>0){a=0;return a|0}f[l>>2]=m&1|b|2;f[d+4>>2]=c|1;f[876]=d;f[873]=c;return a|0}if((f[875]|0)==(i|0)){d=(f[872]|0)+c|0;if(d>>>0<b>>>0){a=0;return a|0}c=d-b|0;if(c>>>0>15){k=a+b|0;d=a+d|0;f[l>>2]=m&1|b|2;f[k+4>>2]=c|1;f[d>>2]=c;d=d+4|0;f[d>>2]=f[d>>2]&-2;d=k}else{f[l>>2]=m&1|d|2;d=a+d+4|0;f[d>>2]=f[d>>2]|1;d=0;c=0}f[872]=c;f[875]=d;return a|0}d=f[i+4>>2]|0;if(d&2|0){a=0;return a|0}j=(d&-8)+c|0;if(j>>>0<b>>>0){a=0;return a|0}k=j-b|0;e=d>>>3;do if(d>>>0<256){d=f[i+8>>2]|0;c=f[i+12>>2]|0;if((c|0)==(d|0)){f[870]=f[870]&~(1<<e);break}else{f[d+12>>2]=c;f[c+8>>2]=d;break}}else{h=f[i+24>>2]|0;c=f[i+12>>2]|0;do if((c|0)==(i|0)){d=i+16|0;e=d+4|0;c=f[e>>2]|0;if(!c){c=f[d>>2]|0;if(!c){e=0;break}}else d=e;while(1){g=c+20|0;e=f[g>>2]|0;if(!e){g=c+16|0;e=f[g>>2]|0;if(!e)break;else{c=e;d=g}}else{c=e;d=g}}f[d>>2]=0;e=c}else{e=f[i+8>>2]|0;f[e+12>>2]=c;f[c+8>>2]=e;e=c}while(0);if(h|0){c=f[i+28>>2]|0;d=3784+(c<<2)|0;if((f[d>>2]|0)==(i|0)){f[d>>2]=e;if(!e){f[871]=f[871]&~(1<<c);break}}else{g=h+16|0;f[((f[g>>2]|0)==(i|0)?g:h+20|0)>>2]=e;if(!e)break}f[e+24>>2]=h;c=i+16|0;d=f[c>>2]|0;if(d|0){f[e+16>>2]=d;f[d+24>>2]=e}c=f[c+4>>2]|0;if(c|0){f[e+20>>2]=c;f[c+24>>2]=e}}}while(0);if(k>>>0<16){f[l>>2]=m&1|j|2;m=a+j+4|0;f[m>>2]=f[m>>2]|1;return a|0}else{i=a+b|0;f[l>>2]=m&1|b|2;f[i+4>>2]=k|3;m=a+j+4|0;f[m>>2]=f[m>>2]|1;Qd(i,k);return a|0}return 0}
function Qd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,h=0,i=0;i=a+b|0;c=f[a+4>>2]|0;do if(!(c&1)){e=f[a>>2]|0;if(!(c&3))return;h=a+(0-e)|0;b=e+b|0;if((f[875]|0)==(h|0)){a=i+4|0;c=f[a>>2]|0;if((c&3|0)!=3)break;f[872]=b;f[a>>2]=c&-2;f[h+4>>2]=b|1;f[i>>2]=b;return}d=e>>>3;if(e>>>0<256){a=f[h+8>>2]|0;c=f[h+12>>2]|0;if((c|0)==(a|0)){f[870]=f[870]&~(1<<d);break}else{f[a+12>>2]=c;f[c+8>>2]=a;break}}g=f[h+24>>2]|0;a=f[h+12>>2]|0;do if((a|0)==(h|0)){c=h+16|0;d=c+4|0;a=f[d>>2]|0;if(!a){a=f[c>>2]|0;if(!a){a=0;break}}else c=d;while(1){e=a+20|0;d=f[e>>2]|0;if(!d){e=a+16|0;d=f[e>>2]|0;if(!d)break;else{a=d;c=e}}else{a=d;c=e}}f[c>>2]=0}else{e=f[h+8>>2]|0;f[e+12>>2]=a;f[a+8>>2]=e}while(0);if(g){c=f[h+28>>2]|0;d=3784+(c<<2)|0;if((f[d>>2]|0)==(h|0)){f[d>>2]=a;if(!a){f[871]=f[871]&~(1<<c);break}}else{e=g+16|0;f[((f[e>>2]|0)==(h|0)?e:g+20|0)>>2]=a;if(!a)break}f[a+24>>2]=g;c=h+16|0;d=f[c>>2]|0;if(d|0){f[a+16>>2]=d;f[d+24>>2]=a}c=f[c+4>>2]|0;if(c){f[a+20>>2]=c;f[c+24>>2]=a}}}else h=a;while(0);a=i+4|0;d=f[a>>2]|0;if(!(d&2)){if((f[876]|0)==(i|0)){i=(f[873]|0)+b|0;f[873]=i;f[876]=h;f[h+4>>2]=i|1;if((h|0)!=(f[875]|0))return;f[875]=0;f[872]=0;return}if((f[875]|0)==(i|0)){i=(f[872]|0)+b|0;f[872]=i;f[875]=h;f[h+4>>2]=i|1;f[h+i>>2]=i;return}e=(d&-8)+b|0;c=d>>>3;do if(d>>>0<256){a=f[i+8>>2]|0;b=f[i+12>>2]|0;if((b|0)==(a|0)){f[870]=f[870]&~(1<<c);break}else{f[a+12>>2]=b;f[b+8>>2]=a;break}}else{g=f[i+24>>2]|0;b=f[i+12>>2]|0;do if((b|0)==(i|0)){a=i+16|0;c=a+4|0;b=f[c>>2]|0;if(!b){b=f[a>>2]|0;if(!b){c=0;break}}else a=c;while(1){d=b+20|0;c=f[d>>2]|0;if(!c){d=b+16|0;c=f[d>>2]|0;if(!c)break;else{b=c;a=d}}else{b=c;a=d}}f[a>>2]=0;c=b}else{c=f[i+8>>2]|0;f[c+12>>2]=b;f[b+8>>2]=c;c=b}while(0);if(g|0){b=f[i+28>>2]|0;a=3784+(b<<2)|0;if((f[a>>2]|0)==(i|0)){f[a>>2]=c;if(!c){f[871]=f[871]&~(1<<b);break}}else{d=g+16|0;f[((f[d>>2]|0)==(i|0)?d:g+20|0)>>2]=c;if(!c)break}f[c+24>>2]=g;b=i+16|0;a=f[b>>2]|0;if(a|0){f[c+16>>2]=a;f[a+24>>2]=c}b=f[b+4>>2]|0;if(b|0){f[c+20>>2]=b;f[b+24>>2]=c}}}while(0);f[h+4>>2]=e|1;f[h+e>>2]=e;if((h|0)==(f[875]|0)){f[872]=e;return}}else{f[a>>2]=d&-2;f[h+4>>2]=b|1;f[h+b>>2]=b;e=b}b=e>>>3;if(e>>>0<256){c=3520+(b<<1<<2)|0;a=f[870]|0;b=1<<b;if(!(a&b)){f[870]=a|b;b=c;a=c+8|0}else{a=c+8|0;b=f[a>>2]|0}f[a>>2]=h;f[b+12>>2]=h;f[h+8>>2]=b;f[h+12>>2]=c;return}b=e>>>8;if(b)if(e>>>0>16777215)d=31;else{g=(b+1048320|0)>>>16&8;i=b<<g;c=(i+520192|0)>>>16&4;i=i<<c;d=(i+245760|0)>>>16&2;d=14-(c|g|d)+(i<<d>>>15)|0;d=e>>>(d+7|0)&1|d<<1}else d=0;b=3784+(d<<2)|0;f[h+28>>2]=d;f[h+20>>2]=0;f[h+16>>2]=0;a=f[871]|0;c=1<<d;if(!(a&c)){f[871]=a|c;f[b>>2]=h;f[h+24>>2]=b;f[h+12>>2]=h;f[h+8>>2]=h;return}b=f[b>>2]|0;a:do if((f[b+4>>2]&-8|0)!=(e|0)){d=e<<((d|0)==31?0:25-(d>>>1)|0);while(1){c=b+16+(d>>>31<<2)|0;a=f[c>>2]|0;if(!a)break;if((f[a+4>>2]&-8|0)==(e|0)){b=a;break a}else{d=d<<1;b=a}}f[c>>2]=h;f[h+24>>2]=b;f[h+12>>2]=h;f[h+8>>2]=h;return}while(0);g=b+8|0;i=f[g>>2]|0;f[i+12>>2]=h;f[g>>2]=h;f[h+8>>2]=i;f[h+12>>2]=b;f[h+24>>2]=0;return}function Rd(a){a=a|0;var b=0,c=0;b=u;u=u+16|0;c=b;a=Wd(f[a+60>>2]|0)|0;f[c>>2]=a;a=Ud(Ba(6,c|0)|0)|0;u=b;return a|0}function Sd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;m=u;u=u+48|0;k=m+16|0;g=m;e=m+32|0;i=a+28|0;d=f[i>>2]|0;f[e>>2]=d;j=a+20|0;d=(f[j>>2]|0)-d|0;f[e+4>>2]=d;f[e+8>>2]=b;f[e+12>>2]=c;d=d+c|0;h=a+60|0;f[g>>2]=f[h>>2];f[g+4>>2]=e;f[g+8>>2]=2;g=Ud(za(146,g|0)|0)|0;a:do if((d|0)!=(g|0)){b=2;while(1){if((g|0)<0)break;d=d-g|0;o=f[e+4>>2]|0;n=g>>>0>o>>>0;e=n?e+8|0:e;b=b+(n<<31>>31)|0;o=g-(n?o:0)|0;f[e>>2]=(f[e>>2]|0)+o;n=e+4|0;f[n>>2]=(f[n>>2]|0)-o;f[k>>2]=f[h>>2];f[k+4>>2]=e;f[k+8>>2]=b;g=Ud(za(146,k|0)|0)|0;if((d|0)==(g|0)){l=3;break a}}f[a+16>>2]=0;f[i>>2]=0;f[j>>2]=0;f[a>>2]=f[a>>2]|32;if((b|0)==2)c=0;else c=c-(f[e+4>>2]|0)|0}else l=3;while(0);if((l|0)==3){o=f[a+44>>2]|0;f[a+16>>2]=o+(f[a+48>>2]|0);f[i>>2]=o;f[j>>2]=o}u=m;return c|0}function Td(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0;e=u;u=u+32|0;g=e;d=e+20|0;f[g>>2]=f[a+60>>2];f[g+4>>2]=0;f[g+8>>2]=b;f[g+12>>2]=d;f[g+16>>2]=c;if((Ud(xa(140,g|0)|0)|0)<0){f[d>>2]=-1;a=-1}else a=f[d>>2]|0;u=e;return a|0}function Ud(a){a=a|0;var b=0;if(a>>>0>4294963200){b=Vd()|0;f[b>>2]=0-a;a=-1}return a|0}function Vd(){return 4040}function Wd(a){a=a|0;return a|0}function Xd(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,g=0,h=0,i=0,j=0,k=0,l=0;k=u;u=u+32|0;e=k;i=k+16|0;f[i>>2]=c;g=i+4|0;j=a+48|0;l=f[j>>2]|0;f[g>>2]=d-((l|0)!=0&1);h=a+44|0;f[i+8>>2]=f[h>>2];f[i+12>>2]=l;f[e>>2]=f[a+60>>2];f[e+4>>2]=i;f[e+8>>2]=2;e=Ud(ya(145,e|0)|0)|0;if((e|0)>=1){i=f[g>>2]|0;if(e>>>0>i>>>0){g=f[h>>2]|0;h=a+4|0;f[h>>2]=g;f[a+8>>2]=g+(e-i);if(!(f[j>>2]|0))e=d;else{f[h>>2]=g+1;b[c+(d+-1)>>0]=b[g>>0]|0;e=d}}}else f[a>>2]=f[a>>2]|e&48^16;u=k;return e|0}function Yd(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,g=0;g=u;u=u+32|0;e=g;f[a+36>>2]=12;if((f[a>>2]&64|0)==0?(f[e>>2]=f[a+60>>2],f[e+4>>2]=21523,f[e+8>>2]=g+16,Aa(54,e|0)|0):0)b[a+75>>0]=-1;e=Sd(a,c,d)|0;u=g;return e|0}function Zd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0;f[a+104>>2]=b;c=f[a+8>>2]|0;d=f[a+4>>2]|0;e=c-d|0;f[a+108>>2]=e;if((b|0)!=0&(e|0)>(b|0))f[a+100>>2]=d+b;else f[a+100>>2]=c;return}function _d(a){a=a|0;var c=0,d=0,e=0,g=0,i=0,j=0;d=a+104|0;i=f[d>>2]|0;if((i|0)!=0?(f[a+108>>2]|0)>=(i|0):0)j=4;else{c=ae(a)|0;if((c|0)>=0){d=f[d>>2]|0;i=f[a+8>>2]|0;if(d){g=f[a+4>>2]|0;d=d-(f[a+108>>2]|0)|0;e=i;if((i-g|0)<(d|0))j=9;else f[a+100>>2]=g+(d+-1)}else{e=i;j=9}if((j|0)==9)f[a+100>>2]=i;d=a+4|0;if(!e)d=f[d>>2]|0;else{d=f[d>>2]|0;a=a+108|0;f[a>>2]=e+1-d+(f[a>>2]|0)}d=d+-1|0;if((c|0)!=(h[d>>0]|0|0))b[d>>0]=c}else j=4}if((j|0)==4){f[a+100>>2]=0;c=-1}return c|0}function $d(a){a=a|0;return ((a|0)==32|(a+-9|0)>>>0<5)&1|0}function ae(a){a=a|0;var b=0,c=0;c=u;u=u+16|0;b=c;if((be(a)|0)==0?(Ja[f[a+32>>2]&15](a,b,1)|0)==1:0)a=h[b>>0]|0;else a=-1;u=c;return a|0}function be(a){a=a|0;var c=0,d=0;c=a+74|0;d=b[c>>0]|0;b[c>>0]=d+255|d;c=a+20|0;d=a+28|0;if((f[c>>2]|0)>>>0>(f[d>>2]|0)>>>0)Ja[f[a+36>>2]&15](a,0,0)|0;f[a+16>>2]=0;f[d>>2]=0;f[c>>2]=0;c=f[a>>2]|0;if(!(c&4)){d=(f[a+44>>2]|0)+(f[a+48>>2]|0)|0;f[a+8>>2]=d;f[a+4>>2]=d;c=c<<27>>31}else{f[a>>2]=c|32;c=-1}return c|0}function ce(a,b){a=+a;b=+b;var c=0,d=0;p[s>>3]=a;d=f[s>>2]|0;c=f[s+4>>2]|0;p[s>>3]=b;c=f[s+4>>2]&-2147483648|c&2147483647;f[s>>2]=d;f[s+4>>2]=c;return +(+p[s>>3])}function de(a,c){a=a|0;c=c|0;var d=0,e=0;d=b[a>>0]|0;e=b[c>>0]|0;if(d<<24>>24==0?1:d<<24>>24!=e<<24>>24)a=e;else{do{a=a+1|0;c=c+1|0;d=b[a>>0]|0;e=b[c>>0]|0}while(!(d<<24>>24==0?1:d<<24>>24!=e<<24>>24));a=e}return (d&255)-(a&255)|0}function ee(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,f=0;a:do if(!d)a=0;else{while(1){e=b[a>>0]|0;f=b[c>>0]|0;if(e<<24>>24!=f<<24>>24)break;d=d+-1|0;if(!d){a=0;break a}else{a=a+1|0;c=c+1|0}}a=(e&255)-(f&255)|0}while(0);return a|0}function fe(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,f=0;if(!d)e=0;else{e=b[a>>0]|0;a:do if(!(e<<24>>24))e=0;else while(1){d=d+-1|0;f=b[c>>0]|0;if(!(e<<24>>24==f<<24>>24&((d|0)!=0&f<<24>>24!=0)))break a;a=a+1|0;c=c+1|0;e=b[a>>0]|0;if(!(e<<24>>24)){e=0;break}}while(0);e=(e&255)-(h[c>>0]|0)|0}return e|0}function ge(a){a=a|0;return 0}function he(a){a=a|0;return}function ie(a,b){a=+a;b=b|0;var c=0,d=0,e=0;p[s>>3]=a;c=f[s>>2]|0;d=f[s+4>>2]|0;e=Ze(c|0,d|0,52)|0;switch(e&2047){case 0:{if(a!=0.0){a=+ie(a*18446744073709551616.0,b);c=(f[b>>2]|0)+-64|0}else c=0;f[b>>2]=c;break}case 2047:break;default:{f[b>>2]=(e&2047)+-1022;f[s>>2]=c;f[s+4>>2]=d&-2146435073|1071644672;a=+p[s>>3]}}return +a}function je(){return 1780}function ke(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;m=u;u=u+208|0;j=m+8|0;k=m;h=W(c,b)|0;i=k;f[i>>2]=1;f[i+4>>2]=0;a:do if(h|0){i=0-c|0;f[j+4>>2]=c;f[j>>2]=c;e=2;b=c;g=c;while(1){b=b+c+g|0;f[j+(e<<2)>>2]=b;if(b>>>0<h>>>0){n=g;e=e+1|0;g=b;b=n}else break}g=a+h+i|0;if(g>>>0>a>>>0){h=g;e=1;b=1;do{do if((b&3|0)!=3){b=e+-1|0;if((f[j+(b<<2)>>2]|0)>>>0<(h-a|0)>>>0)le(a,c,d,e,j);else ne(a,c,d,k,e,0,j);if((e|0)==1){oe(k,1);e=0;break}else{oe(k,b);e=1;break}}else{le(a,c,d,e,j);me(k,2);e=e+2|0}while(0);b=f[k>>2]|1;f[k>>2]=b;a=a+c|0}while(a>>>0<g>>>0)}else{e=1;b=1}ne(a,c,d,k,e,0,j);g=k+4|0;while(1){if((e|0)==1&(b|0)==1)if(!(f[g>>2]|0))break a;else l=19;else if((e|0)<2)l=19;else{oe(k,2);n=e+-2|0;f[k>>2]=f[k>>2]^7;me(k,1);ne(a+(0-(f[j+(n<<2)>>2]|0))+i|0,c,d,k,e+-1|0,1,j);oe(k,1);b=f[k>>2]|1;f[k>>2]=b;a=a+i|0;ne(a,c,d,k,n,1,j);e=n}if((l|0)==19){l=0;b=pe(k)|0;me(k,b);a=a+i|0;e=b+e|0;b=f[k>>2]|0}}}while(0);u=m;return}function le(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0;m=u;u=u+240|0;l=m;f[l>>2]=a;a:do if((d|0)>1){k=0-b|0;i=a;g=d;d=1;h=a;while(1){i=i+k|0;j=g+-2|0;a=i+(0-(f[e+(j<<2)>>2]|0))|0;if((Ia[c&7](h,a)|0)>-1?(Ia[c&7](h,i)|0)>-1:0)break a;h=l+(d<<2)|0;if((Ia[c&7](a,i)|0)>-1){f[h>>2]=a;g=g+-1|0}else{f[h>>2]=i;a=i;g=j}d=d+1|0;if((g|0)<=1)break a;i=a;h=f[l>>2]|0}}else d=1;while(0);re(b,l,d);u=m;return}function me(a,b){a=a|0;b=b|0;var c=0,d=0,e=0;e=a+4|0;if(b>>>0>31){d=f[e>>2]|0;f[a>>2]=d;f[e>>2]=0;b=b+-32|0;c=0}else{c=f[e>>2]|0;d=f[a>>2]|0}f[a>>2]=c<<32-b|d>>>b;f[e>>2]=c>>>b;return}function ne(a,b,c,d,e,g,h){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;o=u;u=u+240|0;m=o+232|0;n=o;p=f[d>>2]|0;f[m>>2]=p;j=f[d+4>>2]|0;k=m+4|0;f[k>>2]=j;f[n>>2]=a;a:do if((p|0)!=1|(j|0)!=0?(l=0-b|0,i=a+(0-(f[h+(e<<2)>>2]|0))|0,(Ia[c&7](i,a)|0)>=1):0){d=1;g=(g|0)==0;j=i;while(1){if(g&(e|0)>1){g=a+l|0;i=f[h+(e+-2<<2)>>2]|0;if((Ia[c&7](g,j)|0)>-1){i=10;break a}if((Ia[c&7](g+(0-i)|0,j)|0)>-1){i=10;break a}}g=d+1|0;f[n+(d<<2)>>2]=j;p=pe(m)|0;me(m,p);e=p+e|0;if(!((f[m>>2]|0)!=1|(f[k>>2]|0)!=0)){d=g;a=j;i=10;break a}a=j+(0-(f[h+(e<<2)>>2]|0))|0;if((Ia[c&7](a,f[n>>2]|0)|0)<1){a=j;d=g;g=0;i=9;break}else{p=j;d=g;g=1;j=a;a=p}}}else{d=1;i=9}while(0);if((i|0)==9?(g|0)==0:0)i=10;if((i|0)==10){re(b,n,d);le(a,b,c,e,h)}u=o;return}function oe(a,b){a=a|0;b=b|0;var c=0,d=0,e=0;e=a+4|0;if(b>>>0>31){d=f[a>>2]|0;f[e>>2]=d;f[a>>2]=0;b=b+-32|0;c=0}else{c=f[a>>2]|0;d=f[e>>2]|0}f[e>>2]=c>>>(32-b|0)|d<<b;f[a>>2]=c<<b;return}function pe(a){a=a|0;var b=0;b=qe((f[a>>2]|0)+-1|0)|0;if(!b){b=qe(f[a+4>>2]|0)|0;return ((b|0)==0?0:b+32|0)|0}else return b|0;return 0}function qe(a){a=a|0;var b=0;if(a)if(!(a&1)){b=a;a=0;while(1){a=a+1|0;if(!(b&2))b=b>>>1;else break}}else a=0;else a=32;return a|0}function re(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,g=0,h=0,i=0;h=u;u=u+256|0;d=h;a:do if((c|0)>=2?(g=b+(c<<2)|0,f[g>>2]=d,a|0):0)while(1){e=a>>>0<256?a:256;af(d|0,f[b>>2]|0,e|0)|0;d=0;do{i=b+(d<<2)|0;d=d+1|0;af(f[i>>2]|0,f[b+(d<<2)>>2]|0,e|0)|0;f[i>>2]=(f[i>>2]|0)+e}while((d|0)!=(c|0));a=a-e|0;if(!a)break a;d=f[g>>2]|0}while(0);u=h;return}function se(a,c,d){a=a|0;c=c|0;d=d|0;var e=0.0,g=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;switch(c|0){case 0:{l=-149;m=24;j=4;break}case 1:{l=-1074;m=53;j=4;break}case 2:{l=-1074;m=53;j=4;break}default:e=0.0}a:do if((j|0)==4){o=a+4|0;n=a+100|0;do{c=f[o>>2]|0;if(c>>>0<(f[n>>2]|0)>>>0){f[o>>2]=c+1;c=h[c>>0]|0}else c=_d(a)|0}while(($d(c)|0)!=0);b:do switch(c|0){case 43:case 45:{i=1-(((c|0)==45&1)<<1)|0;c=f[o>>2]|0;if(c>>>0<(f[n>>2]|0)>>>0){f[o>>2]=c+1;g=h[c>>0]|0;break b}else{g=_d(a)|0;break b}}default:{g=c;i=1}}while(0);c=0;while(1){if((g|32|0)!=(b[3462+c>>0]|0))break;do if(c>>>0<7){g=f[o>>2]|0;if(g>>>0<(f[n>>2]|0)>>>0){f[o>>2]=g+1;g=h[g>>0]|0;break}else{g=_d(a)|0;break}}while(0);c=c+1|0;if(c>>>0>=8){c=8;break}}c:do switch(c&2147483647|0){case 8:break;case 3:{j=23;break}default:{k=(d|0)!=0;if(k&c>>>0>3)if((c|0)==8)break c;else{j=23;break c}d:do if(!c){c=0;while(1){if((g|32|0)!=(b[3471+c>>0]|0))break d;do if(c>>>0<2){g=f[o>>2]|0;if(g>>>0<(f[n>>2]|0)>>>0){f[o>>2]=g+1;g=h[g>>0]|0;break}else{g=_d(a)|0;break}}while(0);c=c+1|0;if(c>>>0>=3){c=3;break}}}while(0);switch(c|0){case 3:{c=f[o>>2]|0;if(c>>>0<(f[n>>2]|0)>>>0){f[o>>2]=c+1;c=h[c>>0]|0}else c=_d(a)|0;if((c|0)!=40){if(!(f[n>>2]|0)){e=A;break a}f[o>>2]=(f[o>>2]|0)+-1;e=A;break a}c=1;while(1){g=f[o>>2]|0;if(g>>>0<(f[n>>2]|0)>>>0){f[o>>2]=g+1;g=h[g>>0]|0}else g=_d(a)|0;if(!((g+-48|0)>>>0<10|(g+-65|0)>>>0<26)?!((g|0)==95|(g+-97|0)>>>0<26):0)break;c=c+1|0}if((g|0)==41){e=A;break a}g=(f[n>>2]|0)==0;if(!g)f[o>>2]=(f[o>>2]|0)+-1;if(!k){o=Vd()|0;f[o>>2]=22;Zd(a,0);e=0.0;break a}if(!c){e=A;break a}while(1){c=c+-1|0;if(!g)f[o>>2]=(f[o>>2]|0)+-1;if(!c){e=A;break a}}}case 0:{if((g|0)==48){c=f[o>>2]|0;if(c>>>0<(f[n>>2]|0)>>>0){f[o>>2]=c+1;c=h[c>>0]|0}else c=_d(a)|0;if((c|32|0)==120){e=+te(a,m,l,i,d);break a}if(!(f[n>>2]|0))c=48;else{f[o>>2]=(f[o>>2]|0)+-1;c=48}}else c=g;e=+ue(a,c,m,l,i,d);break a}default:{if(f[n>>2]|0)f[o>>2]=(f[o>>2]|0)+-1;o=Vd()|0;f[o>>2]=22;Zd(a,0);e=0.0;break a}}}}while(0);if((j|0)==23){g=(f[n>>2]|0)==0;if(!g)f[o>>2]=(f[o>>2]|0)+-1;if((d|0)!=0&c>>>0>3)do{if(!g)f[o>>2]=(f[o>>2]|0)+-1;c=c+-1|0}while(c>>>0>3)}e=+(i|0)*B}while(0);return +e}function te(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var g=0.0,i=0,j=0,k=0.0,l=0,m=0,n=0.0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;y=a+4|0;i=f[y>>2]|0;x=a+100|0;if(i>>>0<(f[x>>2]|0)>>>0){f[y>>2]=i+1;i=h[i>>0]|0}else i=_d(a)|0;j=0;a:while(1){switch(i|0){case 46:{w=10;break a}case 48:break;default:{p=0;m=j;l=0;j=0;break a}}i=f[y>>2]|0;if(i>>>0<(f[x>>2]|0)>>>0){f[y>>2]=i+1;i=h[i>>0]|0}else i=_d(a)|0;j=1}if((w|0)==10){i=f[y>>2]|0;if(i>>>0<(f[x>>2]|0)>>>0){f[y>>2]=i+1;i=h[i>>0]|0}else i=_d(a)|0;if((i|0)==48){l=0;j=0;do{i=f[y>>2]|0;if(i>>>0<(f[x>>2]|0)>>>0){f[y>>2]=i+1;i=h[i>>0]|0}else i=_d(a)|0;l=Se(l|0,j|0,-1,-1)|0;j=H}while((i|0)==48);p=1;m=1}else{p=1;m=j;l=0;j=0}}o=0;n=1.0;g=0.0;v=0;s=p;t=m;u=0;m=0;while(1){q=i+-48|0;p=i|32;if(q>>>0>=10){r=(i|0)==46;if(!(r|(p+-97|0)>>>0<6))break;if(r)if(!s){s=1;k=n;q=v;r=t;l=m;j=u;p=u}else{i=46;break}else w=24}else w=24;if((w|0)==24){w=0;i=(i|0)>57?p+-87|0:q;do if(!((u|0)<0|(u|0)==0&m>>>0<8))if((u|0)<0|(u|0)==0&m>>>0<14){n=n*.0625;k=n;g=g+n*+(i|0);i=v;break}else{i=(o|0)!=0|(i|0)==0;o=i?o:1;k=n;g=i?g:g+n*.5;i=v;break}else{k=n;i=i+(v<<4)|0}while(0);m=Se(m|0,u|0,1,0)|0;q=i;r=1;p=H}i=f[y>>2]|0;if(i>>>0<(f[x>>2]|0)>>>0){f[y>>2]=i+1;i=h[i>>0]|0}else i=_d(a)|0;n=k;v=q;t=r;u=p}do if(!t){i=(f[x>>2]|0)==0;if(!i)f[y>>2]=(f[y>>2]|0)+-1;if(e){if(!i?(f[y>>2]=(f[y>>2]|0)+-1,!((s|0)==0|i)):0)f[y>>2]=(f[y>>2]|0)+-1}else Zd(a,0);g=+(d|0)*0.0}else{o=(s|0)==0;p=o?m:l;o=o?u:j;if((u|0)<0|(u|0)==0&m>>>0<8){j=v;l=u;while(1){j=j<<4;w=m;m=Se(m|0,l|0,1,0)|0;if(!((l|0)<0|(l|0)==0&w>>>0<7)){m=j;break}else l=H}}else m=v;if((i|32|0)==112){j=ve(a,e)|0;i=H;if((j|0)==0&(i|0)==-2147483648){if(!e){Zd(a,0);g=0.0;break}if(!(f[x>>2]|0)){j=0;i=0}else{f[y>>2]=(f[y>>2]|0)+-1;j=0;i=0}}}else if(!(f[x>>2]|0)){j=0;i=0}else{f[y>>2]=(f[y>>2]|0)+-1;j=0;i=0}l=_e(p|0,o|0,2)|0;l=Se(l|0,H|0,-32,-1)|0;l=Se(l|0,H|0,j|0,i|0)|0;i=H;if(!m){g=+(d|0)*0.0;break}y=0-c|0;e=((y|0)<0)<<31>>31;if((i|0)>(e|0)|(i|0)==(e|0)&l>>>0>y>>>0){b=Vd()|0;f[b>>2]=34;g=+(d|0)*1797693134862315708145274.0e284*1797693134862315708145274.0e284;break}y=c+-106|0;e=((y|0)<0)<<31>>31;if((i|0)<(e|0)|(i|0)==(e|0)&l>>>0<y>>>0){b=Vd()|0;f[b>>2]=34;g=+(d|0)*2.2250738585072014e-308*2.2250738585072014e-308;break}if((m|0)>-1){j=m;do{y=!(g>=.5);j=j<<1|(y^1)&1;g=g+(y?g:g+-1.0);l=Se(l|0,i|0,-1,-1)|0;i=H}while((j|0)>-1);n=g;m=j}else n=g;y=((b|0)<0)<<31>>31;c=Te(32,0,c|0,((c|0)<0)<<31>>31|0)|0;i=Se(c|0,H|0,l|0,i|0)|0;c=H;if((c|0)<(y|0)|(c|0)==(y|0)&i>>>0<b>>>0)if((i|0)>0)w=65;else{j=0;i=84;w=67}else{i=b;w=65}if((w|0)==65)if((i|0)<53){j=i;i=84-i|0;w=67}else{k=0.0;g=+(d|0)}if((w|0)==67){g=+(d|0);k=+xe(+we(1.0,i),g);i=j}d=(m&1|0)==0&(n!=0.0&(i|0)<32);g=(d?0.0:n)*g+(k+g*+((m+(d&1)|0)>>>0))-k;if(!(g!=0.0)){d=Vd()|0;f[d>>2]=34}g=+ze(g,l)}while(0);return +g}function ue(a,b,c,d,e,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;g=g|0;var i=0.0,j=0,k=0.0,l=0,m=0,n=0,o=0,p=0,q=0.0,r=0.0,s=0,t=0.0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,I=0.0;G=u;u=u+512|0;D=G;E=d+c|0;F=0-E|0;C=a+4|0;B=a+100|0;j=0;a:while(1){switch(b|0){case 46:{z=7;break a}case 48:break;default:{v=0;p=j;j=0;o=0;break a}}b=f[C>>2]|0;if(b>>>0<(f[B>>2]|0)>>>0){f[C>>2]=b+1;b=h[b>>0]|0}else b=_d(a)|0;j=1}if((z|0)==7){b=f[C>>2]|0;if(b>>>0<(f[B>>2]|0)>>>0){f[C>>2]=b+1;b=h[b>>0]|0}else b=_d(a)|0;if((b|0)==48){j=0;b=0;while(1){j=Se(j|0,b|0,-1,-1)|0;o=H;b=f[C>>2]|0;if(b>>>0<(f[B>>2]|0)>>>0){f[C>>2]=b+1;b=h[b>>0]|0}else b=_d(a)|0;if((b|0)==48)b=o;else{v=1;p=1;break}}}else{v=1;p=j;j=0;o=0}}f[D>>2]=0;n=b+-48|0;m=(b|0)==46;b:do if(m|n>>>0<10){A=D+496|0;w=0;l=0;s=0;x=v;y=p;z=n;p=0;n=0;c:while(1){do if(m)if(!x){x=1;j=p;o=n}else break c;else{p=Se(p|0,n|0,1,0)|0;n=H;v=(b|0)!=48;if((l|0)>=125){if(!v)break;f[A>>2]=f[A>>2]|1;break}m=D+(l<<2)|0;if(!w)b=z;else b=b+-48+((f[m>>2]|0)*10|0)|0;f[m>>2]=b;w=w+1|0;y=(w|0)==9;w=y?0:w;l=l+(y&1)|0;s=v?p:s;y=1}while(0);b=f[C>>2]|0;if(b>>>0<(f[B>>2]|0)>>>0){f[C>>2]=b+1;b=h[b>>0]|0}else b=_d(a)|0;z=b+-48|0;m=(b|0)==46;if(!(m|z>>>0<10)){v=x;m=y;z=31;break b}}b=w;m=(y|0)!=0;z=39}else{w=0;l=0;s=0;m=p;p=0;n=0;z=31}while(0);do if((z|0)==31){A=(v|0)==0;j=A?p:j;o=A?n:o;m=(m|0)!=0;if(!(m&(b|32|0)==101))if((b|0)>-1){b=w;z=39;break}else{b=w;z=41;break}m=ve(a,g)|0;b=H;if((m|0)==0&(b|0)==-2147483648){if(!g){Zd(a,0);i=0.0;break}if(!(f[B>>2]|0)){m=0;b=0}else{f[C>>2]=(f[C>>2]|0)+-1;m=0;b=0}}j=Se(m|0,b|0,j|0,o|0)|0;b=w;o=H;z=43}while(0);if((z|0)==39)if(f[B>>2]|0){f[C>>2]=(f[C>>2]|0)+-1;if(m)z=43;else z=42}else z=41;if((z|0)==41)if(m)z=43;else z=42;do if((z|0)==42){F=Vd()|0;f[F>>2]=22;Zd(a,0);i=0.0}else if((z|0)==43){m=f[D>>2]|0;if(!m){i=+(e|0)*0.0;break}if(((n|0)<0|(n|0)==0&p>>>0<10)&((j|0)==(p|0)&(o|0)==(n|0))?(c|0)>30|(m>>>c|0)==0:0){i=+(e|0)*+(m>>>0);break}a=(d|0)/-2|0;C=((a|0)<0)<<31>>31;if((o|0)>(C|0)|(o|0)==(C|0)&j>>>0>a>>>0){F=Vd()|0;f[F>>2]=34;i=+(e|0)*1797693134862315708145274.0e284*1797693134862315708145274.0e284;break}a=d+-106|0;C=((a|0)<0)<<31>>31;if((o|0)<(C|0)|(o|0)==(C|0)&j>>>0<a>>>0){F=Vd()|0;f[F>>2]=34;i=+(e|0)*2.2250738585072014e-308*2.2250738585072014e-308;break}if(b){if((b|0)<9){n=D+(l<<2)|0;m=f[n>>2]|0;while(1){m=m*10|0;if((b|0)>=8)break;else b=b+1|0}f[n>>2]=m}l=l+1|0}if((s|0)<9?(s|0)<=(j|0)&(j|0)<18:0){if((j|0)==9){i=+(e|0)*+((f[D>>2]|0)>>>0);break}if((j|0)<9){i=+(e|0)*+((f[D>>2]|0)>>>0)/+(f[2024+(8-j<<2)>>2]|0);break}a=c+27+(W(j,-3)|0)|0;b=f[D>>2]|0;if((a|0)>30|(b>>>a|0)==0){i=+(e|0)*+(b>>>0)*+(f[2024+(j+-10<<2)>>2]|0);break}}b=(j|0)%9|0;if(!b)m=0;else{s=(j|0)>-1?b:b+9|0;o=f[2024+(8-s<<2)>>2]|0;if(l){p=1e9/(o|0)|0;n=0;m=0;b=0;do{B=D+(b<<2)|0;C=f[B>>2]|0;a=(C>>>0)/(o>>>0)|0;C=C-(W(a,o)|0)|0;a=a+n|0;f[B>>2]=a;n=W(p,C)|0;a=(b|0)==(m|0)&(a|0)==0;j=a?j+-9|0:j;m=a?m+1&127:m;b=b+1|0}while((b|0)!=(l|0));if(!n)b=l;else{f[D+(l<<2)>>2]=n;b=l+1|0}}else{m=0;b=0}l=b;j=9-s+j|0}b=0;d:while(1){v=(j|0)<18;w=(j|0)==18;x=D+(m<<2)|0;while(1){if(!v){if(!w)break d;if((f[x>>2]|0)>>>0>=9007199){j=18;break d}}n=0;y=l;l=l+127|0;while(1){p=l&127;o=D+(p<<2)|0;l=_e(f[o>>2]|0,0,29)|0;l=Se(l|0,H|0,n|0,0)|0;n=H;if(n>>>0>0|(n|0)==0&l>>>0>1e9){s=We(l|0,n|0,1e9,0)|0;a=Re(s|0,H|0,1e9,0)|0;l=Te(l|0,n|0,a|0,H|0)|0}else s=0;f[o>>2]=l;a=(p|0)==(m|0);o=(p|0)!=(y+127&127|0)|a?y:(l|0)==0?p:y;if(a)break;else{n=s;y=o;l=p+-1|0}}b=b+-29|0;if(!s)l=y;else break}m=m+127&127;l=o+127&127;n=D+((o+126&127)<<2)|0;if((m|0)==(o|0))f[n>>2]=f[n>>2]|f[D+(l<<2)>>2];else l=y;f[D+(m<<2)>>2]=s;j=j+9|0}e:while(1){w=l+1&127;x=D+((l+127&127)<<2)|0;while(1){p=(j|0)==18;v=(j|0)>27?9:1;y=m;while(1){o=0;while(1){m=o+y&127;if((m|0)==(l|0)){z=92;break}m=f[D+(m<<2)>>2]|0;n=f[2056+(o<<2)>>2]|0;if(m>>>0<n>>>0){z=92;break}if(m>>>0>n>>>0)break;if((o+1|0)>>>0<2)o=1;else{z=92;break}}if((z|0)==92?(z=0,p):0)break e;b=v+b|0;if((y|0)==(l|0))y=l;else break}p=(1<<v)+-1|0;s=1e9>>>v;o=0;m=y;n=y;do{B=D+(n<<2)|0;C=f[B>>2]|0;a=(C>>>v)+o|0;f[B>>2]=a;o=W(C&p,s)|0;a=(n|0)==(m|0)&(a|0)==0;j=a?j+-9|0:j;m=a?m+1&127:m;n=n+1&127}while((n|0)!=(l|0));if(o|0){if((w|0)!=(m|0))break;f[x>>2]=f[x>>2]|1}}f[D+(l<<2)>>2]=o;l=w}i=0.0;j=l;m=0;do{n=m+y&127;l=j+1&127;if((n|0)==(j|0)){f[D+(l+-1<<2)>>2]=0;j=l}i=i*1.0e9+ +((f[D+(n<<2)>>2]|0)>>>0);m=m+1|0}while((m|0)!=2);t=+(e|0);k=i*t;n=b+53|0;o=n-d|0;p=(o|0)<(c|0);m=p?((o|0)>0?o:0):c;if((m|0)<53){I=+xe(+we(1.0,105-m|0),k);q=+ye(k,+we(1.0,53-m|0));r=I;i=q;q=I+(k-q)}else{r=0.0;i=0.0;q=k}l=y+2&127;if((l|0)!=(j|0)){l=f[D+(l<<2)>>2]|0;do if(l>>>0>=5e8){if((l|0)!=5e8){i=t*.75+i;break}if((y+3&127|0)==(j|0)){i=t*.5+i;break}else{i=t*.75+i;break}}else{if((l|0)==0?(y+3&127|0)==(j|0):0)break;i=t*.25+i}while(0);if((53-m|0)>1?!(+ye(i,1.0)!=0.0):0)k=i+1.0;else k=i}else k=i;i=q+k-r;do if((n&2147483647|0)>(-2-E|0)){E=!(+J(+i)>=9007199254740992.0);b=b+((E^1)&1)|0;i=E?i:i*.5;if((b+50|0)<=(F|0)?!(k!=0.0&(p&((m|0)!=(o|0)|E))):0)break;F=Vd()|0;f[F>>2]=34}while(0);i=+ze(i,b)}while(0);u=G;return +i}function ve(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,g=0,i=0,j=0,k=0,l=0;i=a+4|0;c=f[i>>2]|0;j=a+100|0;if(c>>>0<(f[j>>2]|0)>>>0){f[i>>2]=c+1;d=h[c>>0]|0}else d=_d(a)|0;switch(d|0){case 43:case 45:{e=(d|0)==45&1;c=f[i>>2]|0;if(c>>>0<(f[j>>2]|0)>>>0){f[i>>2]=c+1;c=h[c>>0]|0}else c=_d(a)|0;d=c+-48|0;if((b|0)!=0&d>>>0>9)if(!(f[j>>2]|0)){d=0;c=-2147483648}else{f[i>>2]=(f[i>>2]|0)+-1;k=14}else k=12;break}default:{e=0;c=d;d=d+-48|0;k=12}}if((k|0)==12)if(d>>>0>9)k=14;else{d=0;do{d=c+-48+(d*10|0)|0;c=f[i>>2]|0;if(c>>>0<(f[j>>2]|0)>>>0){f[i>>2]=c+1;c=h[c>>0]|0}else c=_d(a)|0;b=c+-48|0}while(b>>>0<10&(d|0)<214748364);g=((d|0)<0)<<31>>31;if(b>>>0<10){do{l=Re(d|0,g|0,10,0)|0;b=H;c=Se(c|0,((c|0)<0)<<31>>31|0,-48,-1)|0;d=Se(c|0,H|0,l|0,b|0)|0;g=H;c=f[i>>2]|0;if(c>>>0<(f[j>>2]|0)>>>0){f[i>>2]=c+1;c=h[c>>0]|0}else c=_d(a)|0;b=c+-48|0}while(b>>>0<10&((g|0)<21474836|(g|0)==21474836&d>>>0<2061584302));if(b>>>0<10){do{c=f[i>>2]|0;if(c>>>0<(f[j>>2]|0)>>>0){f[i>>2]=c+1;c=h[c>>0]|0}else c=_d(a)|0}while((c+-48|0)>>>0<10);c=g}else c=g}else c=g;if(f[j>>2]|0)f[i>>2]=(f[i>>2]|0)+-1;l=(e|0)==0;j=Te(0,0,d|0,c|0)|0;d=l?d:j;c=l?c:H}if((k|0)==14)if(!(f[j>>2]|0)){d=0;c=-2147483648}else{f[i>>2]=(f[i>>2]|0)+-1;d=0;c=-2147483648}H=c;return d|0}function we(a,b){a=+a;b=b|0;var c=0,d=0;if((b|0)<=1023){if((b|0)<-1022){a=a*2.2250738585072014e-308;d=(b|0)<-2044;c=b+2044|0;a=d?a*2.2250738585072014e-308:a;b=d?((c|0)>-1022?c:-1022):b+1022|0}}else{a=a*8988465674311579538646525.0e283;c=(b|0)>2046;d=b+-2046|0;a=c?a*8988465674311579538646525.0e283:a;b=c?((d|0)<1023?d:1023):b+-1023|0}c=_e(b+1023|0,0,52)|0;d=H;f[s>>2]=c;f[s+4>>2]=d;return +(a*+p[s>>3])}function xe(a,b){a=+a;b=+b;return +(+ce(a,b))}function ye(a,b){a=+a;b=+b;return +(+Ae(a,b))}function ze(a,b){a=+a;b=b|0;return +(+we(a,b))}function Ae(a,b){a=+a;b=+b;var c=0,d=0,e=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;p[s>>3]=a;h=f[s>>2]|0;j=f[s+4>>2]|0;p[s>>3]=b;l=f[s>>2]|0;m=f[s+4>>2]|0;d=Ze(h|0,j|0,52)|0;d=d&2047;k=Ze(l|0,m|0,52)|0;k=k&2047;n=j&-2147483648;g=_e(l|0,m|0,1)|0;i=H;a:do if(!((g|0)==0&(i|0)==0)?(e=Be(b)|0,c=H&2147483647,!((d|0)==2047|(c>>>0>2146435072|(c|0)==2146435072&e>>>0>0))):0){c=_e(h|0,j|0,1)|0;e=H;if(!(e>>>0>i>>>0|(e|0)==(i|0)&c>>>0>g>>>0))return +((c|0)==(g|0)&(e|0)==(i|0)?a*0.0:a);if(!d){c=_e(h|0,j|0,12)|0;e=H;if((e|0)>-1|(e|0)==-1&c>>>0>4294967295){d=0;do{d=d+-1|0;c=_e(c|0,e|0,1)|0;e=H}while((e|0)>-1|(e|0)==-1&c>>>0>4294967295)}else d=0;h=_e(h|0,j|0,1-d|0)|0;g=H}else g=j&1048575|1048576;if(!k){e=_e(l|0,m|0,12)|0;i=H;if((i|0)>-1|(i|0)==-1&e>>>0>4294967295){c=0;do{c=c+-1|0;e=_e(e|0,i|0,1)|0;i=H}while((i|0)>-1|(i|0)==-1&e>>>0>4294967295)}else c=0;l=_e(l|0,m|0,1-c|0)|0;k=c;j=H}else j=m&1048575|1048576;e=Te(h|0,g|0,l|0,j|0)|0;c=H;i=(c|0)>-1|(c|0)==-1&e>>>0>4294967295;b:do if((d|0)>(k|0)){while(1){if(i){if((e|0)==0&(c|0)==0)break}else{e=h;c=g}h=_e(e|0,c|0,1)|0;g=H;d=d+-1|0;e=Te(h|0,g|0,l|0,j|0)|0;c=H;i=(c|0)>-1|(c|0)==-1&e>>>0>4294967295;if((d|0)<=(k|0))break b}b=a*0.0;break a}while(0);if(i){if((e|0)==0&(c|0)==0){b=a*0.0;break}}else{c=g;e=h}if(c>>>0<1048576|(c|0)==1048576&e>>>0<0)do{e=_e(e|0,c|0,1)|0;c=H;d=d+-1|0}while(c>>>0<1048576|(c|0)==1048576&e>>>0<0);if((d|0)>0){m=Se(e|0,c|0,0,-1048576)|0;c=H;d=_e(d|0,0,52)|0;c=c|H;d=m|d}else{d=Ze(e|0,c|0,1-d|0)|0;c=H}f[s>>2]=d;f[s+4>>2]=c|n;b=+p[s>>3]}else o=3;while(0);if((o|0)==3){b=a*b;b=b/b}return +b}function Be(a){a=+a;var b=0;p[s>>3]=a;b=f[s>>2]|0;H=f[s+4>>2]|0;return b|0}function Ce(a){a=a|0;var c=0,d=0,e=0;e=a;a:do if(!(e&3))d=5;else{c=e;while(1){if(!(b[a>>0]|0)){a=c;break a}a=a+1|0;c=a;if(!(c&3)){d=5;break}}}while(0);if((d|0)==5){while(1){c=f[a>>2]|0;if(!((c&-2139062144^-2139062144)&c+-16843009))a=a+4|0;else break}if((c&255)<<24>>24)do a=a+1|0;while((b[a>>0]|0)!=0)}return a-e|0}function De(a,c){a=a|0;c=c|0;a=Ee(a,c)|0;return ((b[a>>0]|0)==(c&255)<<24>>24?a:0)|0}function Ee(a,c){a=a|0;c=c|0;var d=0,e=0,g=0;e=c&255;a:do if(!e)a=a+(Ce(a)|0)|0;else{if(a&3){d=c&255;do{g=b[a>>0]|0;if(g<<24>>24==0?1:g<<24>>24==d<<24>>24)break a;a=a+1|0}while((a&3|0)!=0)}e=W(e,16843009)|0;d=f[a>>2]|0;b:do if(!((d&-2139062144^-2139062144)&d+-16843009))do{g=d^e;if((g&-2139062144^-2139062144)&g+-16843009|0)break b;a=a+4|0;d=f[a>>2]|0}while(!((d&-2139062144^-2139062144)&d+-16843009|0));while(0);d=c&255;while(1){g=b[a>>0]|0;if(g<<24>>24==0?1:g<<24>>24==d<<24>>24)break;else a=a+1|0}}while(0);return a|0}function Fe(a){a=+a;return ~~+df(+a)|0}function Ge(a){a=a|0;var b=0;if(f[a+68>>2]|0){b=f[a+116>>2]|0;a=a+112|0;if(b|0)f[b+112>>2]=f[a>>2];a=f[a>>2]|0;if(!a)a=(He()|0)+232|0;else a=a+116|0;f[a>>2]=b}return}function He(){return je()|0}function Ie(){va(4044);return 4052}function Je(){Ca(4044);return}function Ke(a){a=a|0;var b=0,c=0,d=0,e=0,g=0;if((f[a+76>>2]|0)>-1)e=ge(a)|0;else e=0;Ge(a);g=(f[a>>2]&1|0)!=0;if(!g){d=Ie()|0;c=f[a+52>>2]|0;b=a+56|0;if(c|0)f[c+56>>2]=f[b>>2];b=f[b>>2]|0;if(b|0)f[b+52>>2]=c;if((f[d>>2]|0)==(a|0))f[d>>2]=b;Je()}b=Le(a)|0;b=Ha[f[a+12>>2]&7](a)|0|b;c=f[a+92>>2]|0;if(c|0)Md(c);if(g){if(e|0)he(a)}else Md(a);return b|0}function Le(a){a=a|0;var b=0,c=0;do if(a){if((f[a+76>>2]|0)<=-1){b=Me(a)|0;break}c=(ge(a)|0)==0;b=Me(a)|0;if(!c)he(a)}else{if(!(f[444]|0))b=0;else b=Le(f[444]|0)|0;a=Ie()|0;a=f[a>>2]|0;if(a)do{if((f[a+76>>2]|0)>-1)c=ge(a)|0;else c=0;if((f[a+20>>2]|0)>>>0>(f[a+28>>2]|0)>>>0)b=Me(a)|0|b;if(c|0)he(a);a=f[a+56>>2]|0}while((a|0)!=0);Je()}while(0);return b|0}function Me(a){a=a|0;var b=0,c=0,d=0,e=0,g=0,h=0;b=a+20|0;h=a+28|0;if((f[b>>2]|0)>>>0>(f[h>>2]|0)>>>0?(Ja[f[a+36>>2]&15](a,0,0)|0,(f[b>>2]|0)==0):0)a=-1;else{c=a+4|0;d=f[c>>2]|0;e=a+8|0;g=f[e>>2]|0;if(d>>>0<g>>>0)Ja[f[a+40>>2]&15](a,d-g|0,1)|0;f[a+16>>2]=0;f[h>>2]=0;f[b>>2]=0;f[e>>2]=0;f[c>>2]=0;a=0}return a|0}function Ne(a,b){a=a|0;b=b|0;return +(+Oe(a,b,1))}function Oe(a,b,c){a=a|0;b=b|0;c=c|0;var d=0.0,e=0,g=0,h=0,i=0;i=u;u=u+128|0;h=i;e=h;g=e+124|0;do{f[e>>2]=0;e=e+4|0}while((e|0)<(g|0));e=h+4|0;f[e>>2]=a;g=h+8|0;f[g>>2]=-1;f[h+44>>2]=a;f[h+76>>2]=-1;Zd(h,0);d=+se(h,c,1);c=(f[e>>2]|0)-(f[g>>2]|0)+(f[h+108>>2]|0)|0;if(b|0)f[b>>2]=(c|0)==0?a:a+c|0;u=i;return +d}function Pe(){}function Qe(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;f=a&65535;e=b&65535;c=W(e,f)|0;d=a>>>16;a=(c>>>16)+(W(e,d)|0)|0;e=b>>>16;b=W(e,f)|0;return (H=(a>>>16)+(W(e,d)|0)+(((a&65535)+b|0)>>>16)|0,a+b<<16|c&65535|0)|0}function Re(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=a;f=c;c=Qe(e,f)|0;a=H;return (H=(W(b,f)|0)+(W(d,e)|0)+a|a&0,c|0|0)|0}function Se(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;c=a+c>>>0;return (H=b+d+(c>>>0<a>>>0|0)>>>0,c|0)|0}function Te(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;d=b-d-(c>>>0>a>>>0|0)>>>0;return (H=d,a-c>>>0|0)|0}function Ue(a){a=a|0;return (a?31-(Z(a^a-1)|0)|0:32)|0}function Ve(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;l=a;j=b;k=j;h=c;n=d;i=n;if(!k){g=(e|0)!=0;if(!i){if(g){f[e>>2]=(l>>>0)%(h>>>0);f[e+4>>2]=0}n=0;e=(l>>>0)/(h>>>0)>>>0;return (H=n,e)|0}else{if(!g){n=0;e=0;return (H=n,e)|0}f[e>>2]=a|0;f[e+4>>2]=b&0;n=0;e=0;return (H=n,e)|0}}g=(i|0)==0;do if(h){if(!g){g=(Z(i|0)|0)-(Z(k|0)|0)|0;if(g>>>0<=31){m=g+1|0;i=31-g|0;b=g-31>>31;h=m;a=l>>>(m>>>0)&b|k<<i;b=k>>>(m>>>0)&b;g=0;i=l<<i;break}if(!e){n=0;e=0;return (H=n,e)|0}f[e>>2]=a|0;f[e+4>>2]=j|b&0;n=0;e=0;return (H=n,e)|0}g=h-1|0;if(g&h|0){i=(Z(h|0)|0)+33-(Z(k|0)|0)|0;p=64-i|0;m=32-i|0;j=m>>31;o=i-32|0;b=o>>31;h=i;a=m-1>>31&k>>>(o>>>0)|(k<<m|l>>>(i>>>0))&b;b=b&k>>>(i>>>0);g=l<<p&j;i=(k<<p|l>>>(o>>>0))&j|l<<m&i-33>>31;break}if(e|0){f[e>>2]=g&l;f[e+4>>2]=0}if((h|0)==1){o=j|b&0;p=a|0|0;return (H=o,p)|0}else{p=Ue(h|0)|0;o=k>>>(p>>>0)|0;p=k<<32-p|l>>>(p>>>0)|0;return (H=o,p)|0}}else{if(g){if(e|0){f[e>>2]=(k>>>0)%(h>>>0);f[e+4>>2]=0}o=0;p=(k>>>0)/(h>>>0)>>>0;return (H=o,p)|0}if(!l){if(e|0){f[e>>2]=0;f[e+4>>2]=(k>>>0)%(i>>>0)}o=0;p=(k>>>0)/(i>>>0)>>>0;return (H=o,p)|0}g=i-1|0;if(!(g&i)){if(e|0){f[e>>2]=a|0;f[e+4>>2]=g&k|b&0}o=0;p=k>>>((Ue(i|0)|0)>>>0);return (H=o,p)|0}g=(Z(i|0)|0)-(Z(k|0)|0)|0;if(g>>>0<=30){b=g+1|0;i=31-g|0;h=b;a=k<<i|l>>>(b>>>0);b=k>>>(b>>>0);g=0;i=l<<i;break}if(!e){o=0;p=0;return (H=o,p)|0}f[e>>2]=a|0;f[e+4>>2]=j|b&0;o=0;p=0;return (H=o,p)|0}while(0);if(!h){k=i;j=0;i=0}else{m=c|0|0;l=n|d&0;k=Se(m|0,l|0,-1,-1)|0;c=H;j=i;i=0;do{d=j;j=g>>>31|j<<1;g=i|g<<1;d=a<<1|d>>>31|0;n=a>>>31|b<<1|0;Te(k|0,c|0,d|0,n|0)|0;p=H;o=p>>31|((p|0)<0?-1:0)<<1;i=o&1;a=Te(d|0,n|0,o&m|0,(((p|0)<0?-1:0)>>31|((p|0)<0?-1:0)<<1)&l|0)|0;b=H;h=h-1|0}while((h|0)!=0);k=j;j=0}h=0;if(e|0){f[e>>2]=a;f[e+4>>2]=b}o=(g|0)>>>31|(k|h)<<1|(h<<1|g>>>31)&0|j;p=(g<<1|0>>>31)&-2|i;return (H=o,p)|0}function We(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return Ve(a,b,c,d,0)|0}function Xe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,g=0;g=u;u=u+16|0;e=g|0;Ve(a,b,c,d,e)|0;u=g;return (H=f[e+4>>2]|0,f[e>>2]|0)|0}function Ye(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){H=b>>c;return a>>>c|(b&(1<<c)-1)<<32-c}H=(b|0)<0?-1:0;return b>>c-32|0}function Ze(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){H=b>>>c;return a>>>c|(b&(1<<c)-1)<<32-c}H=0;return b>>>c-32|0}function _e(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){H=b<<c|(a&(1<<c)-1<<32-c)>>>32-c;return a<<c}H=a<<c-32;return 0}function $e(a){a=a|0;return (a&255)<<24|(a>>8&255)<<16|(a>>16&255)<<8|a>>>24|0}function af(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,g=0,h=0;if((d|0)>=8192)return Da(a|0,c|0,d|0)|0;h=a|0;g=a+d|0;if((a&3)==(c&3)){while(a&3){if(!d)return h|0;b[a>>0]=b[c>>0]|0;a=a+1|0;c=c+1|0;d=d-1|0}d=g&-4|0;e=d-64|0;while((a|0)<=(e|0)){f[a>>2]=f[c>>2];f[a+4>>2]=f[c+4>>2];f[a+8>>2]=f[c+8>>2];f[a+12>>2]=f[c+12>>2];f[a+16>>2]=f[c+16>>2];f[a+20>>2]=f[c+20>>2];f[a+24>>2]=f[c+24>>2];f[a+28>>2]=f[c+28>>2];f[a+32>>2]=f[c+32>>2];f[a+36>>2]=f[c+36>>2];f[a+40>>2]=f[c+40>>2];f[a+44>>2]=f[c+44>>2];f[a+48>>2]=f[c+48>>2];f[a+52>>2]=f[c+52>>2];f[a+56>>2]=f[c+56>>2];f[a+60>>2]=f[c+60>>2];a=a+64|0;c=c+64|0}while((a|0)<(d|0)){f[a>>2]=f[c>>2];a=a+4|0;c=c+4|0}}else{d=g-4|0;while((a|0)<(d|0)){b[a>>0]=b[c>>0]|0;b[a+1>>0]=b[c+1>>0]|0;b[a+2>>0]=b[c+2>>0]|0;b[a+3>>0]=b[c+3>>0]|0;a=a+4|0;c=c+4|0}}while((a|0)<(g|0)){b[a>>0]=b[c>>0]|0;a=a+1|0;c=c+1|0}return h|0}function bf(a,c,d){a=a|0;c=c|0;d=d|0;var e=0;if((c|0)<(a|0)&(a|0)<(c+d|0)){e=a;c=c+d|0;a=a+d|0;while((d|0)>0){a=a-1|0;c=c-1|0;d=d-1|0;b[a>>0]=b[c>>0]|0}a=e}else af(a,c,d)|0;return a|0}function cf(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,g=0,h=0,i=0;h=a+d|0;c=c&255;if((d|0)>=67){while(a&3){b[a>>0]=c;a=a+1|0}e=h&-4|0;g=e-64|0;i=c|c<<8|c<<16|c<<24;while((a|0)<=(g|0)){f[a>>2]=i;f[a+4>>2]=i;f[a+8>>2]=i;f[a+12>>2]=i;f[a+16>>2]=i;f[a+20>>2]=i;f[a+24>>2]=i;f[a+28>>2]=i;f[a+32>>2]=i;f[a+36>>2]=i;f[a+40>>2]=i;f[a+44>>2]=i;f[a+48>>2]=i;f[a+52>>2]=i;f[a+56>>2]=i;f[a+60>>2]=i;a=a+64|0}while((a|0)<(e|0)){f[a>>2]=i;a=a+4|0}}while((a|0)<(h|0)){b[a>>0]=c;a=a+1|0}return h-d|0}function df(a){a=+a;return a>=0.0?+I(a+.5):+V(a-.5)}function ef(a){a=a|0;var b=0,c=0;c=f[r>>2]|0;b=c+a|0;if((a|0)>0&(b|0)<(c|0)|(b|0)<0){ca()|0;wa(12);return -1}f[r>>2]=b;if((b|0)>(ba()|0)?(aa()|0)==0:0){f[r>>2]=c;wa(12);return -1}return c|0}function ff(a,b){a=a|0;b=b|0;return Ha[a&7](b|0)|0}function gf(a){a=a|0;return ea(0,a|0)|0}function hf(a){a=a|0;return ea(1,a|0)|0}function jf(a){a=a|0;return ea(2,a|0)|0}function kf(a){a=a|0;return ea(3,a|0)|0}function lf(a){a=a|0;return ea(4,a|0)|0}function mf(a,b,c){a=a|0;b=b|0;c=c|0;return Ia[a&7](b|0,c|0)|0}function nf(a,b){a=a|0;b=b|0;return ga(0,a|0,b|0)|0}function of(a,b){a=a|0;b=b|0;return ga(1,a|0,b|0)|0}function pf(a,b){a=a|0;b=b|0;return ga(2,a|0,b|0)|0}function qf(a,b){a=a|0;b=b|0;return ga(3,a|0,b|0)|0}function rf(a,b){a=a|0;b=b|0;return ga(4,a|0,b|0)|0}function sf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return Ja[a&15](b|0,c|0,d|0)|0}function tf(a,b,c){a=a|0;b=b|0;c=c|0;return ia(0,a|0,b|0,c|0)|0}function uf(a,b,c){a=a|0;b=b|0;c=c|0;return ia(1,a|0,b|0,c|0)|0}function vf(a,b,c){a=a|0;b=b|0;c=c|0;return ia(2,a|0,b|0,c|0)|0}function wf(a,b,c){a=a|0;b=b|0;c=c|0;return ia(3,a|0,b|0,c|0)|0}function xf(a,b,c){a=a|0;b=b|0;c=c|0;return ia(4,a|0,b|0,c|0)|0}function yf(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;return Ka[a&7](b|0,c|0,d|0,e|0)|0}function zf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return ka(0,a|0,b|0,c|0,d|0)|0}function Af(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return ka(1,a|0,b|0,c|0,d|0)|0}function Bf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return ka(2,a|0,b|0,c|0,d|0)|0}function Cf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return ka(3,a|0,b|0,c|0,d|0)|0}function Df(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return ka(4,a|0,b|0,c|0,d|0)|0}function Ef(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;return La[a&7](b|0,c|0,d|0,e|0,f|0,g|0)|0}function Ff(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return ma(0,a|0,b|0,c|0,d|0,e|0,f|0)|0}function Gf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return ma(1,a|0,b|0,c|0,d|0,e|0,f|0)|0}function Hf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return ma(2,a|0,b|0,c|0,d|0,e|0,f|0)|0}function If(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return ma(3,a|0,b|0,c|0,d|0,e|0,f|0)|0}function Jf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return ma(4,a|0,b|0,c|0,d|0,e|0,f|0)|0}function Kf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;Ma[a&7](b|0,c|0,d|0)}function Lf(a,b,c){a=a|0;b=b|0;c=c|0;oa(0,a|0,b|0,c|0)}function Mf(a,b,c){a=a|0;b=b|0;c=c|0;oa(1,a|0,b|0,c|0)}function Nf(a,b,c){a=a|0;b=b|0;c=c|0;oa(2,a|0,b|0,c|0)}function Of(a,b,c){a=a|0;b=b|0;c=c|0;oa(3,a|0,b|0,c|0)}function Pf(a,b,c){a=a|0;b=b|0;c=c|0;oa(4,a|0,b|0,c|0)}function Qf(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;Na[a&7](b|0,c|0,d|0,e|0)}function Rf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;qa(0,a|0,b|0,c|0,d|0)}function Sf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;qa(1,a|0,b|0,c|0,d|0)}function Tf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;qa(2,a|0,b|0,c|0,d|0)}function Uf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;qa(3,a|0,b|0,c|0,d|0)}function Vf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;qa(4,a|0,b|0,c|0,d|0)}function Wf(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;Oa[a&15](b|0,c|0,d|0,e|0,f|0,g|0)}function Xf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;sa(0,a|0,b|0,c|0,d|0,e|0,f|0)}function Yf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;sa(1,a|0,b|0,c|0,d|0,e|0,f|0)}function Zf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;sa(2,a|0,b|0,c|0,d|0,e|0,f|0)}function _f(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;sa(3,a|0,b|0,c|0,d|0,e|0,f|0)}function $f(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;sa(4,a|0,b|0,c|0,d|0,e|0,f|0)}function ag(a,b,c,d,e,f,g,h){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;Pa[a&7](b|0,c|0,d|0,e|0,f|0,g|0,h|0)}function bg(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;ua(0,a|0,b|0,c|0,d|0,e|0,f|0,g|0)}function cg(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;ua(1,a|0,b|0,c|0,d|0,e|0,f|0,g|0)}function dg(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;ua(2,a|0,b|0,c|0,d|0,e|0,f|0,g|0)}function eg(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;ua(3,a|0,b|0,c|0,d|0,e|0,f|0,g|0)}function fg(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;ua(4,a|0,b|0,c|0,d|0,e|0,f|0,g|0)}function gg(a){a=a|0;_(0);return 0}function hg(a,b){a=a|0;b=b|0;_(1);return 0}function ig(a,b,c){a=a|0;b=b|0;c=c|0;_(2);return 0}function jg(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;_(3);return 0}function kg(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;_(4);return 0}function lg(a,b,c){a=a|0;b=b|0;c=c|0;_(5)}function mg(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;_(6)}function ng(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;_(7)}function og(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;_(8)}

// EMSCRIPTEN_END_FUNCS
var Ha=[gg,gf,hf,jf,kf,lf,Rd,gg];var Ia=[hg,nf,of,pf,qf,rf,Yb,hg];var Ja=[ig,tf,uf,vf,wf,xf,Xd,Td,Yd,Dc,Pb,Qb,Sd,ig,ig,ig];var Ka=[jg,zf,Af,Bf,Cf,Df,ad,bd];var La=[kg,Ff,Gf,Hf,If,Jf,kg,kg];var Ma=[lg,Lf,Mf,Nf,Of,Pf,cd,dd];var Na=[mg,Rf,Sf,Tf,Uf,Vf,gc,mg];var Oa=[ng,Xf,Yf,Zf,_f,$f,lc,mc,jc,kc,ng,ng,ng,ng,ng,ng];var Pa=[og,bg,cg,dg,eg,fg,$c,og];return{_FLAC__stream_decoder_delete:zc,_FLAC__stream_decoder_finish:Ac,_FLAC__stream_decoder_get_md5_checking:Hc,_FLAC__stream_decoder_get_state:Gc,_FLAC__stream_decoder_init_stream:Bc,_FLAC__stream_decoder_new:yc,_FLAC__stream_decoder_process_single:Ic,_FLAC__stream_decoder_process_until_end_of_metadata:Oc,_FLAC__stream_decoder_process_until_end_of_stream:Pc,_FLAC__stream_decoder_reset:Ec,_FLAC__stream_decoder_set_md5_checking:Fc,_FLAC__stream_encoder_delete:Sc,_FLAC__stream_encoder_finish:Tc,_FLAC__stream_encoder_get_state:ld,_FLAC__stream_encoder_init_stream:Zc,_FLAC__stream_encoder_new:Qc,_FLAC__stream_encoder_process_interleaved:md,_FLAC__stream_encoder_set_bits_per_sample:gd,_FLAC__stream_encoder_set_blocksize:jd,_FLAC__stream_encoder_set_channels:fd,_FLAC__stream_encoder_set_compression_level:id,_FLAC__stream_encoder_set_sample_rate:hd,_FLAC__stream_encoder_set_total_samples_estimate:kd,_FLAC__stream_encoder_set_verify:ed,___errno_location:Vd,___muldi3:Re,___udivdi3:We,___uremdi3:Xe,_bitshift64Ashr:Ye,_bitshift64Lshr:Ze,_bitshift64Shl:_e,_emscripten_replace_memory:Ga,_free:Md,_i64Add:Se,_i64Subtract:Te,_llvm_bswap_i32:$e,_malloc:Ld,_memcpy:af,_memmove:bf,_memset:cf,_round:df,_sbrk:ef,dynCall_ii:ff,dynCall_iii:mf,dynCall_iiii:sf,dynCall_iiiii:yf,dynCall_iiiiiii:Ef,dynCall_viii:Kf,dynCall_viiii:Qf,dynCall_viiiiii:Wf,dynCall_viiiiiii:ag,establishStackSpace:Ta,getTempRet0:Wa,runPostSets:Pe,setTempRet0:Va,setThrew:Ua,stackAlloc:Qa,stackRestore:Sa,stackSave:Ra}})


// EMSCRIPTEN_END_ASM
(Module.asmGlobalArg,Module.asmLibraryArg,buffer);var _FLAC__stream_decoder_delete=Module["_FLAC__stream_decoder_delete"]=asm["_FLAC__stream_decoder_delete"];var _FLAC__stream_decoder_finish=Module["_FLAC__stream_decoder_finish"]=asm["_FLAC__stream_decoder_finish"];var _FLAC__stream_decoder_get_md5_checking=Module["_FLAC__stream_decoder_get_md5_checking"]=asm["_FLAC__stream_decoder_get_md5_checking"];var _FLAC__stream_decoder_get_state=Module["_FLAC__stream_decoder_get_state"]=asm["_FLAC__stream_decoder_get_state"];var _FLAC__stream_decoder_init_stream=Module["_FLAC__stream_decoder_init_stream"]=asm["_FLAC__stream_decoder_init_stream"];var _FLAC__stream_decoder_new=Module["_FLAC__stream_decoder_new"]=asm["_FLAC__stream_decoder_new"];var _FLAC__stream_decoder_process_single=Module["_FLAC__stream_decoder_process_single"]=asm["_FLAC__stream_decoder_process_single"];var _FLAC__stream_decoder_process_until_end_of_metadata=Module["_FLAC__stream_decoder_process_until_end_of_metadata"]=asm["_FLAC__stream_decoder_process_until_end_of_metadata"];var _FLAC__stream_decoder_process_until_end_of_stream=Module["_FLAC__stream_decoder_process_until_end_of_stream"]=asm["_FLAC__stream_decoder_process_until_end_of_stream"];var _FLAC__stream_decoder_reset=Module["_FLAC__stream_decoder_reset"]=asm["_FLAC__stream_decoder_reset"];var _FLAC__stream_decoder_set_md5_checking=Module["_FLAC__stream_decoder_set_md5_checking"]=asm["_FLAC__stream_decoder_set_md5_checking"];var _FLAC__stream_encoder_delete=Module["_FLAC__stream_encoder_delete"]=asm["_FLAC__stream_encoder_delete"];var _FLAC__stream_encoder_finish=Module["_FLAC__stream_encoder_finish"]=asm["_FLAC__stream_encoder_finish"];var _FLAC__stream_encoder_get_state=Module["_FLAC__stream_encoder_get_state"]=asm["_FLAC__stream_encoder_get_state"];var _FLAC__stream_encoder_init_stream=Module["_FLAC__stream_encoder_init_stream"]=asm["_FLAC__stream_encoder_init_stream"];var _FLAC__stream_encoder_new=Module["_FLAC__stream_encoder_new"]=asm["_FLAC__stream_encoder_new"];var _FLAC__stream_encoder_process_interleaved=Module["_FLAC__stream_encoder_process_interleaved"]=asm["_FLAC__stream_encoder_process_interleaved"];var _FLAC__stream_encoder_set_bits_per_sample=Module["_FLAC__stream_encoder_set_bits_per_sample"]=asm["_FLAC__stream_encoder_set_bits_per_sample"];var _FLAC__stream_encoder_set_blocksize=Module["_FLAC__stream_encoder_set_blocksize"]=asm["_FLAC__stream_encoder_set_blocksize"];var _FLAC__stream_encoder_set_channels=Module["_FLAC__stream_encoder_set_channels"]=asm["_FLAC__stream_encoder_set_channels"];var _FLAC__stream_encoder_set_compression_level=Module["_FLAC__stream_encoder_set_compression_level"]=asm["_FLAC__stream_encoder_set_compression_level"];var _FLAC__stream_encoder_set_sample_rate=Module["_FLAC__stream_encoder_set_sample_rate"]=asm["_FLAC__stream_encoder_set_sample_rate"];var _FLAC__stream_encoder_set_total_samples_estimate=Module["_FLAC__stream_encoder_set_total_samples_estimate"]=asm["_FLAC__stream_encoder_set_total_samples_estimate"];var _FLAC__stream_encoder_set_verify=Module["_FLAC__stream_encoder_set_verify"]=asm["_FLAC__stream_encoder_set_verify"];var ___errno_location=Module["___errno_location"]=asm["___errno_location"];var ___muldi3=Module["___muldi3"]=asm["___muldi3"];var ___udivdi3=Module["___udivdi3"]=asm["___udivdi3"];var ___uremdi3=Module["___uremdi3"]=asm["___uremdi3"];var _bitshift64Ashr=Module["_bitshift64Ashr"]=asm["_bitshift64Ashr"];var _bitshift64Lshr=Module["_bitshift64Lshr"]=asm["_bitshift64Lshr"];var _bitshift64Shl=Module["_bitshift64Shl"]=asm["_bitshift64Shl"];var _emscripten_replace_memory=Module["_emscripten_replace_memory"]=asm["_emscripten_replace_memory"];var _free=Module["_free"]=asm["_free"];var _i64Add=Module["_i64Add"]=asm["_i64Add"];var _i64Subtract=Module["_i64Subtract"]=asm["_i64Subtract"];var _llvm_bswap_i32=Module["_llvm_bswap_i32"]=asm["_llvm_bswap_i32"];var _malloc=Module["_malloc"]=asm["_malloc"];var _memcpy=Module["_memcpy"]=asm["_memcpy"];var _memmove=Module["_memmove"]=asm["_memmove"];var _memset=Module["_memset"]=asm["_memset"];var _round=Module["_round"]=asm["_round"];var _sbrk=Module["_sbrk"]=asm["_sbrk"];var establishStackSpace=Module["establishStackSpace"]=asm["establishStackSpace"];var getTempRet0=Module["getTempRet0"]=asm["getTempRet0"];var runPostSets=Module["runPostSets"]=asm["runPostSets"];var setTempRet0=Module["setTempRet0"]=asm["setTempRet0"];var setThrew=Module["setThrew"]=asm["setThrew"];var stackAlloc=Module["stackAlloc"]=asm["stackAlloc"];var stackRestore=Module["stackRestore"]=asm["stackRestore"];var stackSave=Module["stackSave"]=asm["stackSave"];var dynCall_ii=Module["dynCall_ii"]=asm["dynCall_ii"];var dynCall_iii=Module["dynCall_iii"]=asm["dynCall_iii"];var dynCall_iiii=Module["dynCall_iiii"]=asm["dynCall_iiii"];var dynCall_iiiii=Module["dynCall_iiiii"]=asm["dynCall_iiiii"];var dynCall_iiiiiii=Module["dynCall_iiiiiii"]=asm["dynCall_iiiiiii"];var dynCall_viii=Module["dynCall_viii"]=asm["dynCall_viii"];var dynCall_viiii=Module["dynCall_viiii"]=asm["dynCall_viiii"];var dynCall_viiiiii=Module["dynCall_viiiiii"]=asm["dynCall_viiiiii"];var dynCall_viiiiiii=Module["dynCall_viiiiiii"]=asm["dynCall_viiiiiii"];Module["asm"]=asm;Module["ccall"]=ccall;Module["cwrap"]=cwrap;Module["setValue"]=setValue;Module["getValue"]=getValue;if(memoryInitializer){if(!isDataURI(memoryInitializer)){if(typeof Module["locateFile"]==="function"){memoryInitializer=Module["locateFile"](memoryInitializer)}else if(Module["memoryInitializerPrefixURL"]){memoryInitializer=Module["memoryInitializerPrefixURL"]+memoryInitializer}}if(ENVIRONMENT_IS_NODE||ENVIRONMENT_IS_SHELL){var data=Module["readBinary"](memoryInitializer);HEAPU8.set(data,GLOBAL_BASE)}else{addRunDependency("memory initializer");var applyMemoryInitializer=(function(data){if(data.byteLength)data=new Uint8Array(data);HEAPU8.set(data,GLOBAL_BASE);if(Module["memoryInitializerRequest"])delete Module["memoryInitializerRequest"].response;removeRunDependency("memory initializer")});function doBrowserLoad(){Module["readAsync"](memoryInitializer,applyMemoryInitializer,(function(){throw"could not load memory initializer "+memoryInitializer}))}if(Module["memoryInitializerRequest"]){function useRequest(){var request=Module["memoryInitializerRequest"];var response=request.response;if(request.status!==200&&request.status!==0){console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+request.status+", retrying "+memoryInitializer);doBrowserLoad();return}applyMemoryInitializer(response)}if(Module["memoryInitializerRequest"].response){setTimeout(useRequest,0)}else{Module["memoryInitializerRequest"].addEventListener("load",useRequest)}}else{doBrowserLoad()}}}function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}ExitStatus.prototype=new Error;ExitStatus.prototype.constructor=ExitStatus;var initialStackTop;dependenciesFulfilled=function runCaller(){if(!Module["calledRun"])run();if(!Module["calledRun"])dependenciesFulfilled=runCaller};function run(args){args=args||Module["arguments"];if(runDependencies>0){return}preRun();if(runDependencies>0)return;if(Module["calledRun"])return;function doRun(){if(Module["calledRun"])return;Module["calledRun"]=true;if(ABORT)return;ensureInitRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout((function(){setTimeout((function(){Module["setStatus"]("")}),1);doRun()}),1)}else{doRun()}}Module["run"]=run;function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}if(what!==undefined){out(what);err(what);what=JSON.stringify(what)}else{what=""}ABORT=true;EXITSTATUS=1;throw"abort("+what+"). Build with -s ASSERTIONS=1 for more info."}Module["abort"]=abort;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}Module["noExitRuntime"]=true;run();function _readStreamInfo(p_streaminfo){var min_blocksize=Module.getValue(p_streaminfo,"i32");var max_blocksize=Module.getValue(p_streaminfo+4,"i32");var min_framesize=Module.getValue(p_streaminfo+8,"i32");var max_framesize=Module.getValue(p_streaminfo+12,"i32");var sample_rate=Module.getValue(p_streaminfo+16,"i32");var channels=Module.getValue(p_streaminfo+20,"i32");var bits_per_sample=Module.getValue(p_streaminfo+24,"i32");var total_samples=Module.getValue(p_streaminfo+32,"i64");var md5sum=_readMd5(p_streaminfo+40);return{min_blocksize:min_blocksize,max_blocksize:max_blocksize,min_framesize:min_framesize,max_framesize:max_framesize,sampleRate:sample_rate,channels:channels,bitsPerSample:bits_per_sample,total_samples:total_samples,md5sum:md5sum}}function _readMd5(p_md5){var sb=[],v,str;for(var i=0,len=16;i<len;++i){v=Module.getValue(p_md5+i,"i8");if(v<0)v=256+v;str=v.toString(16);if(str.length<2)str="0"+str;sb.push(str)}return sb.join("")}function _readFrameHdr(p_frame){var blocksize=Module.getValue(p_frame,"i32");var sample_rate=Module.getValue(p_frame+4,"i32");var channels=Module.getValue(p_frame+8,"i32");var channel_assignment=Module.getValue(p_frame+12,"i32");var bits_per_sample=Module.getValue(p_frame+16,"i32");var number_type=Module.getValue(p_frame+20,"i32");var frame_number=Module.getValue(p_frame+24,"i32");var sample_number=Module.getValue(p_frame+24,"i64");var number=number_type===0?frame_number:sample_number;var crc=Module.getValue(p_frame+36,"i8");return{blocksize:blocksize,sampleRate:sample_rate,channels:channels,bitsPerSample:bits_per_sample,number:number,crc:crc}}function __fix_write_buffer(heapOffset,newBuffer){var dv=new DataView(newBuffer.buffer);var targetSize=newBuffer.length;var increase=2;var buffer=HEAPU8.subarray(heapOffset,heapOffset+targetSize*increase);var jump,isPrint;for(var i=0,j=0,size=buffer.length;i<size&&j<targetSize;++i,++j){if(i===size-1&&j<targetSize-1){buffer=HEAPU8.subarray(heapOffset,size+targetSize);size=buffer.length}if(buffer[i]===0||buffer[i]===255){jump=0;isPrint=true;if(i+1<size&&buffer[i]===buffer[i+1]){++jump;if(i+2<size){if(buffer[i]===buffer[i+2]){++jump}else{isPrint=false}}}if(isPrint){dv.setUint8(j,buffer[i]);if(jump===2&&i+3<size&&buffer[i]===buffer[i+3]){++jump;dv.setUint8(++j,buffer[i])}}else{--j}i+=jump}else{dv.setUint8(j,buffer[i])}}}var FLAC__STREAM_DECODER_READ_STATUS_CONTINUE=0;var FLAC__STREAM_DECODER_READ_STATUS_END_OF_STREAM=1;var FLAC__STREAM_DECODER_READ_STATUS_ABORT=2;var FLAC__STREAM_DECODER_WRITE_STATUS_CONTINUE=0;var FLAC__STREAM_DECODER_INIT_STATUS_INVALID_CALLBACKS=2;var FLAC__STREAM_ENCODER_INIT_STATUS_INVALID_CALLBACKS=3;var FLAC__STREAM_ENCODER_WRITE_STATUS_OK=0;var FLAC__STREAM_ENCODER_WRITE_STATUS_FATAL_ERROR=1;var coders={};function getCallback(p_coder,func_type){if(coders[p_coder]){return coders[p_coder][func_type]}}function setCallback(p_coder,func_type,callback){if(!coders[p_coder]){coders[p_coder]={}}coders[p_coder][func_type]=callback}var enc_write_fn_ptr=addFunction((function(p_encoder,buffer,bytes,samples,current_frame,p_client_data){var arraybuf=new ArrayBuffer(buffer);var retdata=new Uint8Array(bytes);retdata.set(HEAPU8.subarray(buffer,buffer+bytes));var write_callback_fn=getCallback(p_encoder,"write");try{write_callback_fn(retdata,bytes,samples,current_frame,p_client_data)}catch(err){console.error(err);return FLAC__STREAM_ENCODER_WRITE_STATUS_FATAL_ERROR}return FLAC__STREAM_ENCODER_WRITE_STATUS_OK}),"iiiiiii");var dec_read_fn_ptr=addFunction((function(p_decoder,buffer,bytes,p_client_data){var len=Module.getValue(bytes,"i32");if(len===0){return FLAC__STREAM_DECODER_READ_STATUS_ABORT}var read_callback_fn=getCallback(p_decoder,"read");var readResult=read_callback_fn(len,p_client_data);var readLen=readResult.readDataLength;Module.setValue(bytes,readLen,"i32");if(readResult.error){return FLAC__STREAM_DECODER_READ_STATUS_ABORT}if(readLen===0){return FLAC__STREAM_DECODER_READ_STATUS_END_OF_STREAM}var readBuf=readResult.buffer;var dataHeap=new Uint8Array(Module.HEAPU8.buffer,buffer,readLen);dataHeap.set(new Uint8Array(readBuf));return FLAC__STREAM_DECODER_READ_STATUS_CONTINUE}),"iiiii");var dec_write_fn_ptr=addFunction((function(p_decoder,p_frame,p_buffer,p_client_data){var frameInfo=_readFrameHdr(p_frame);var channels=frameInfo.channels;var block_size=frameInfo.blocksize*(frameInfo.bitsPerSample/8);var data=[];var bufferOffset,_buffer;for(var i=0;i<channels;++i){bufferOffset=Module.getValue(p_buffer+i*4,"i32");_buffer=new Uint8Array(block_size);__fix_write_buffer(bufferOffset,_buffer);data.push(_buffer.subarray(0,block_size))}var write_callback_fn=getCallback(p_decoder,"write");write_callback_fn(data,frameInfo);return FLAC__STREAM_DECODER_WRITE_STATUS_CONTINUE}),"iiiii");var dec_error_fn_ptr=addFunction((function(p_decoder,err,p_client_data){var msg;switch(err){case 0:msg="FLAC__STREAM_DECODER_ERROR_STATUS_LOST_SYNC";break;case 1:msg="FLAC__STREAM_DECODER_ERROR_STATUS_BAD_HEADER";break;case 2:msg="FLAC__STREAM_DECODER_ERROR_STATUS_FRAME_CRC_MISMATCH";break;case 3:msg="FLAC__STREAM_DECODER_ERROR_STATUS_UNPARSEABLE_STREAM";break;default:msg="FLAC__STREAM_DECODER_ERROR__UNKNOWN__"}var error_callback_fn=getCallback(p_decoder,"error");error_callback_fn(err,msg,p_client_data)}),"viii");var metadata_fn_ptr=addFunction((function(p_coder,p_metadata,p_client_data){var type=Module.getValue(p_metadata,"i32");var is_last=Module.getValue(p_metadata+4,"i32");var length=Module.getValue(p_metadata+8,"i64");var metadata_callback_fn=getCallback(p_coder,"metadata");var meta_data;if(type===0){meta_data=_readStreamInfo(p_metadata+16);metadata_callback_fn(meta_data)}}),"viii");var _exported={_module:Module,_clear_enc_cb:(function(enc_ptr){delete coders[enc_ptr]}),_clear_dec_cb:(function(dec_ptr){delete coders[dec_ptr]}),isReady:(function(){return _flac_ready}),onready:void 0,FLAC__stream_encoder_set_verify:Module.cwrap("FLAC__stream_encoder_set_verify","number",["number","number"]),FLAC__stream_encoder_set_compression_level:Module.cwrap("FLAC__stream_encoder_set_compression_level","number",["number","number"]),FLAC__stream_encoder_set_blocksize:Module.cwrap("FLAC__stream_encoder_set_blocksize","number",["number","number"]),create_libflac_encoder:(function(sample_rate,channels,bps,compression_level,total_samples,is_verify,block_size){is_verify=typeof is_verify==="undefined"?1:is_verify+0;total_samples=typeof total_samples==="number"?total_samples:0;block_size=typeof block_size==="number"?block_size:0;var ok=true;var encoder=Module.ccall("FLAC__stream_encoder_new","number",[],[]);ok&=Module.ccall("FLAC__stream_encoder_set_verify","number",["number","number"],[encoder,is_verify]);ok&=Module.ccall("FLAC__stream_encoder_set_compression_level","number",["number","number"],[encoder,compression_level]);ok&=Module.ccall("FLAC__stream_encoder_set_channels","number",["number","number"],[encoder,channels]);ok&=Module.ccall("FLAC__stream_encoder_set_bits_per_sample","number",["number","number"],[encoder,bps]);ok&=Module.ccall("FLAC__stream_encoder_set_sample_rate","number",["number","number"],[encoder,sample_rate]);ok&=Module.ccall("FLAC__stream_encoder_set_blocksize","number",["number","number"],[encoder,block_size]);ok&=Module.ccall("FLAC__stream_encoder_set_total_samples_estimate","number",["number","number"],[encoder,total_samples]);if(ok){return encoder}return 0}),init_libflac_encoder:(function(){return this.create_libflac_encoder.apply(this,arguments)}),create_libflac_decoder:(function(is_verify){is_verify=typeof is_verify==="undefined"?1:is_verify+0;var ok=true;var decoder=Module.ccall("FLAC__stream_decoder_new","number",[],[]);ok&=Module.ccall("FLAC__stream_decoder_set_md5_checking","number",["number","number"],[decoder,is_verify]);if(ok){return decoder}return 0}),init_libflac_decoder:(function(){return this.create_libflac_decoder.apply(this,arguments)}),init_encoder_stream:(function(encoder,write_callback_fn,metadata_callback_fn,client_data){client_data=client_data|0;if(typeof write_callback_fn!=="function"){return FLAC__STREAM_ENCODER_INIT_STATUS_INVALID_CALLBACKS}setCallback(encoder,"write",write_callback_fn);var __metadata_callback_fn_ptr=0;if(typeof metadata_callback_fn==="function"){setCallback(encoder,"metadata",metadata_callback_fn);__metadata_callback_fn_ptr=metadata_fn_ptr}var init_status=Module.ccall("FLAC__stream_encoder_init_stream","number",["number","number","number","number","number","number"],[encoder,enc_write_fn_ptr,0,0,__metadata_callback_fn_ptr,client_data]);return init_status}),init_decoder_stream:(function(decoder,read_callback_fn,write_callback_fn,error_callback_fn,metadata_callback_fn,client_data){client_data=client_data|0;if(typeof read_callback_fn!=="function"){return FLAC__STREAM_DECODER_INIT_STATUS_INVALID_CALLBACKS}setCallback(decoder,"read",read_callback_fn);if(typeof write_callback_fn!=="function"){return FLAC__STREAM_DECODER_INIT_STATUS_INVALID_CALLBACKS}setCallback(decoder,"write",write_callback_fn);var __error_callback_fn_ptr=0;if(typeof error_callback_fn==="function"){setCallback(decoder,"error",error_callback_fn);__error_callback_fn_ptr=dec_error_fn_ptr}var __metadata_callback_fn_ptr=0;if(typeof metadata_callback_fn==="function"){setCallback(decoder,"metadata",metadata_callback_fn);__metadata_callback_fn_ptr=metadata_fn_ptr}var init_status=Module.ccall("FLAC__stream_decoder_init_stream","number",["number","number","number","number","number","number","number","number","number","number"],[decoder,dec_read_fn_ptr,0,0,0,0,dec_write_fn_ptr,__metadata_callback_fn_ptr,__error_callback_fn_ptr,client_data]);return init_status}),FLAC__stream_encoder_process_interleaved:(function(encoder,buffer,num_of_samples){var numBytes=buffer.length*buffer.BYTES_PER_ELEMENT;var ptr=Module._malloc(numBytes);var heapBytes=new Uint8Array(Module.HEAPU8.buffer,ptr,numBytes);heapBytes.set(new Uint8Array(buffer.buffer));var status=Module.ccall("FLAC__stream_encoder_process_interleaved","number",["number","number","number"],[encoder,heapBytes.byteOffset,num_of_samples]);Module._free(ptr);return status}),FLAC__stream_decoder_process_single:Module.cwrap("FLAC__stream_decoder_process_single","number",["number"]),FLAC__stream_decoder_process_until_end_of_stream:Module.cwrap("FLAC__stream_decoder_process_until_end_of_stream","number",["number"]),FLAC__stream_decoder_process_until_end_of_metadata:Module.cwrap("FLAC__stream_decoder_process_until_end_of_metadata","number",["number"]),FLAC__stream_decoder_get_state:Module.cwrap("FLAC__stream_decoder_get_state","number",["number"]),FLAC__stream_encoder_get_state:Module.cwrap("FLAC__stream_encoder_get_state","number",["number"]),FLAC__stream_decoder_get_md5_checking:Module.cwrap("FLAC__stream_decoder_get_md5_checking","number",["number"]),FLAC__stream_encoder_finish:Module.cwrap("FLAC__stream_encoder_finish","number",["number"]),FLAC__stream_decoder_finish:Module.cwrap("FLAC__stream_decoder_finish","number",["number"]),FLAC__stream_decoder_reset:Module.cwrap("FLAC__stream_decoder_reset","number",["number"]),FLAC__stream_encoder_delete:(function(encoder){this._clear_enc_cb(encoder);return Module.ccall("FLAC__stream_encoder_delete","number",["number"],[encoder])}),FLAC__stream_decoder_delete:(function(decoder){this._clear_dec_cb(decoder);return Module.ccall("FLAC__stream_decoder_delete","number",["number"],[decoder])})};return _exported}))




