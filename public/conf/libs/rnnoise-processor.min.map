{"version": 3, "file": "rnnoise-processor.min.js", "mappings": "mBACA,ICCMA,EDDFC,EAAsB,CEA1BA,EAAwB,CAACC,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXF,EAAoBI,EAAEF,EAAYC,KAASH,EAAoBI,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDH,EAAwB,CAACS,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFV,EAAyBC,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,GAAO,G,8EHU9D,SAbMjB,EAAiC,oBAAbkB,UAA4BA,SAASC,cAAgBD,SAASC,cAAcC,SAAMC,EAG5G,SAASC,GAGJ,IAAqDC,GAAjDD,OAAuB,KAF9BA,EAASA,GAAU,CAAC,GAEsBA,EAAO,CAAC,GAA2D,MAAE,IAAIE,SAAQ,SAASC,EAAQC,GAAQH,EAAoBE,CAAiC,IAAG,IAA2BrB,EAAvBuB,EAAgB,CAAC,EAAU,IAAIvB,KAAOkB,EAAWA,EAAOT,eAAeT,KAAMuB,EAAgBvB,GAAKkB,EAAOlB,IAAM,IAAyGwB,EAA6BC,EAAlIC,EAAW,GAAgNF,EAAmC,iBAATG,OAAkBF,EAA6C,mBAAhBG,cAAgE,iBAAVC,SAA8C,iBAAnBA,QAAQC,UAA4BD,QAAQC,SAASC,KAAuG,IAA4KC,EAAxKC,EAAgB,IAAqLT,GAAoBC,KAA0BA,EAAuBQ,EAAgBC,KAAKC,SAASC,KAAatB,SAASC,gBAAekB,EAAgBnB,SAASC,cAAcC,KAAOpB,IAAYqC,EAAgBrC,GAAoDqC,EAAH,IAAnCA,EAAgBI,QAAQ,SAA8BJ,EAAgBK,OAAO,EAAEL,EAAgBM,YAAY,KAAK,GAAwB,GAAmId,IAAuBO,EAAW,SAAoBQ,GAAK,IAAIC,EAAI,IAAIC,eAAuF,OAAxED,EAAIE,KAAK,MAAMH,GAAI,GAAOC,EAAIG,aAAa,cAAcH,EAAII,KAAK,MAAa,IAAIC,WAAWL,EAAIM,SAAS,IAA8V,IAAiYC,EAAmOC,EAAhmBC,EAAIhC,EAAc,OAAGiC,QAAQC,IAAIC,KAAKF,SAAaG,EAAIpC,EAAiB,UAAGiC,QAAQI,KAAKF,KAAKF,SAAS,IAAInD,KAAOuB,EAAoBA,EAAgBd,eAAeT,KAAMkB,EAAOlB,GAAKuB,EAAgBvB,IAAMuB,EAAgB,KAAQL,EAAkB,YAAEQ,EAAWR,EAAkB,WAAKA,EAAoB,aAAcA,EAAoB,YAAKA,EAAa,MAAQA,EAAa,KAAoBA,EAAmB,aAAE8B,EAAW9B,EAAmB,YAAuBA,EAAsB,eAAgBA,EAAsB,cAA0B,iBAAdsC,aAAwBF,EAAI,mCAAkD,IAAiOG,EAAaC,EAAsBC,EAAhQC,EAAU,IAAIJ,YAAYK,MAAM,CAAC,QAAU,EAAE,QAAU,EAAI,QAAU,YAAgBC,GAAM,EAAoM,SAASC,EAA2BC,GAAKP,EAAOO,EAAI9C,EAAc,MAAQ,IAAI+C,UAAUD,GAAK9C,EAAe,OAAS,IAAIgD,WAAWF,GAAK9C,EAAe,OAAEyC,EAAO,IAAIQ,WAAWH,GAAK9C,EAAe,OAAEwC,EAAO,IAAIZ,WAAWkB,GAAK9C,EAAgB,QAAU,IAAIkD,YAAYJ,GAAK9C,EAAgB,QAAU,IAAImD,YAAYL,GAAK9C,EAAgB,QAAU,IAAIoD,aAAaN,GAAK9C,EAAgB,QAAU,IAAIqD,aAAaP,EAAI,CAAC,IAAkDQ,EAAuBtD,EAAuB,gBAAG,SAA2V,SAASuD,EAAqBC,GAAW,KAAMA,EAAUC,OAAO,GAAE,CAAC,IAAIC,EAASF,EAAUG,QAAQ,GAAoB,mBAAVD,EAAV,CAA0D,IAAIE,EAAKF,EAASE,KAAsB,iBAAPA,OAAmC7D,IAAf2D,EAASG,IAAiB7D,EAAkB,UAAE4D,GAAW5D,EAAmB,WAAE4D,EAAKF,EAASG,KAAWD,OAAoB7D,IAAf2D,EAASG,IAAgB,KAAKH,EAASG,IAAzM,MAAzBH,EAAS1D,EAA8N,CAAC,EAAnqB+B,EAAtB/B,EAAmB,WAAcA,EAAmB,WAAkB,IAAIsC,YAAYwB,OAAO,CAAC,QAAUR,EAAhxB,MAAszB,QAAU,WAA2Cf,EAAOR,EAAWQ,QAAOe,EAAuBf,EAAOwB,WAAWlB,EAA2BN,GAAQE,EAAOuB,OAA7Y,QAAwxB,IAAIC,EAAa,GAAOC,EAAW,GAAOC,EAAW,GAAOC,EAAc,GAA6sBC,EAAgB,EAAMC,EAAqB,KAASC,EAAsB,KAA2iB,SAASC,EAAMC,GAA2K,MAAlKzE,EAAgB,SAAGA,EAAgB,QAAEyE,GAAezC,EAATyC,GAAM,IAAarC,EAAIqC,GAAM7B,GAAM,EAAkB6B,EAAK,SAASA,EAAK,+CAAqD,IAAInC,YAAYoC,aAAaD,EAAK,CAA5RzE,EAAwB,gBAAE,CAAC,EAAEA,EAAwB,gBAAE,CAAC,EAAgZ,SAAS2E,EAAUC,GAAU,OAArLC,EAAsMD,EAAlME,EAA4G,wCAA7FC,OAAOzF,UAAU0F,WAAWH,EAAIG,WAAWF,GAA8B,IAAtBD,EAAI1D,QAAQ2D,GAArG,IAAmBD,EAAIC,CAAyN,CAAC,IAAlmKG,EAAsmKC,EAAe,eAAwF,SAASC,IAAY,IAAI,GAAGrD,EAAY,OAAO,IAAIF,WAAWE,GAAY,GAAGhB,EAAY,OAAOA,EAAWoE,GAAqB,KAAK,iDAAkD,CAAC,MAAM9C,GAAKoC,EAAMpC,EAAI,CAAC,CAAu5D,SAASgD,EAA0BC,GAAM,IAAqG,OAAjGtD,EAAWuD,KAAKD,EAAK9C,EAAOwB,WAAW,QAAQ,IAAIlB,EAA2Bd,EAAWQ,QAAe,CAAC,CAAC,MAAMgD,GAAG,CAAC,CAA30EZ,EAAUO,KAAlpKD,EAA6rKC,EAA1BA,EAA1pKlF,EAAmB,WAAUA,EAAmB,WAAEiF,EAAKlE,GAAwBA,EAAgBkE,GAAqgOf,EAAWsB,KAAK,CAAC5B,KAAK,WAAW6B,GAAoB,IAAy4B,IAAynCC,EAArnCC,EAAc,CAAC,EAAx5B,SAAgCC,EAAK9F,EAAI+F,GAAKrD,EAAOsD,WAAWF,EAAK9F,EAAIA,EAAI+F,EAAI,EAAk2B,EAAroB,SAAiCE,GAAeA,KAA8B,EAAE,IAAIC,EAApQxD,EAAOiB,OAA6TwC,EAAY,WAAW,GAAGF,EAAcE,EAAa,OAAO,EAA+B,IAAzB,IAAnvLC,EAAoxLC,EAAQ,EAAEA,GAAS,EAAEA,GAAS,EAAE,CAAC,IAAIC,EAAkBJ,GAAS,EAAE,GAAGG,GAAiP,GAAxOC,EAAkBC,KAAKC,IAAIF,EAAkBL,EAAc,WAA0IX,EAAnHiB,KAAKC,IAAIL,IAA77LC,EAAi9LG,KAAKE,IAAnN,SAAmOR,EAAcK,IAA7U,MAA7oL,IAAGF,GAA0oL,MAA9nLA,EAA8nL,OAA5mLA,KAA+hM,OAAO,CAAK,CAAC,OAAO,CAAK,EAA2E,OAASnE,EAAW,MAAQW,GAAoC+C,GAAr3E,WAAsB,IAAIe,EAAK,CAAC,EAAIb,GAAe,SAASc,EAAgBC,EAASC,GAAQ,IAAI/H,EAAQ8H,EAAS9H,QAAQoB,EAAY,IAAEpB,EAAxtD,SAA6BgI,GAA6G,GAAzGvC,IAAqBrE,EAA+B,wBAAGA,EAA+B,uBAAEqE,GAAqC,GAAjBA,IAA8C,OAAvBC,IAA6BuC,cAAcvC,GAAsBA,EAAqB,MAAQC,GAAsB,CAAC,IAAIb,EAASa,EAAsBA,EAAsB,KAAKb,GAAU,CAAE,CAA63CoD,EAAuC,CAAsC,SAASC,EAA0BC,GAAQP,EAAgBO,EAAiB,SAAE,CAAC,SAASC,EAAuBC,GAAU,OAA1tBpF,IAAaxB,IAAoBC,GAAuC,mBAAR4G,MAAwQ,IAAIjH,SAAQ,SAASC,EAAQC,GAAQD,EAAQgF,IAAY,IAAtSgC,MAAMjC,EAAe,CAACkC,YAAY,gBAAgBC,MAAK,SAASxF,GAAU,IAAIA,EAAa,GAAG,KAAK,uCAAuCqD,EAAe,IAAI,OAAOrD,EAAsB,aAAG,IAAGyF,OAAM,WAAW,OAAOnC,GAAW,KAAsbkC,MAAK,SAASE,GAAQ,OAAOjF,YAAYkF,YAAYD,EAAOf,EAAK,IAAGa,KAAKH,GAAS,SAASO,GAAQrF,EAAI,0CAA0CqF,GAAQjD,EAAMiD,EAAO,GAAE,CAAwjB,GAA1wFpD,IAAqBrE,EAA+B,wBAAGA,EAA+B,uBAAEqE,GAAqrFrE,EAAwB,gBAAG,IAAgE,OAAhDA,EAAwB,gBAAEwG,EAAKC,EAA+B,CAAC,MAAMlB,GAAgE,OAA7DnD,EAAI,sDAAsDmD,IAAU,CAAK,EAAtvB,WAA4B,GAAIzD,GAAsD,mBAAnCQ,YAAYoF,sBAAoC/C,EAAUO,IAAgC,mBAARiC,MAAwX,OAAOF,EAAuBF,GAAlYI,MAAMjC,EAAe,CAACkC,YAAY,gBAAgBC,MAAK,SAASxF,GAAqE,OAAhDS,YAAYoF,qBAAqB7F,EAAS2E,GAAoBa,KAAKN,GAA0B,SAASU,GAAuG,OAA/FrF,EAAI,kCAAkCqF,GAAQrF,EAAI,6CAAoD6E,EAAuBF,EAA0B,GAAE,GAAiE,CAAkMY,EAA2B,CAAwjCC,GAAoC5H,EAA2B,mBAAE,WAAW,OAAOyF,EAAmBzF,EAA2B,mBAAEA,EAAY,IAAK,GAAG6H,MAAM,KAAKC,UAAU,GAA48B,SAASC,EAAIC,GAA8F,SAASC,IAAWvC,IAAiBA,GAAU,EAAK1F,EAAkB,WAAE,EAAQ4C,IAAj2LW,EAAqBW,GAA+BX,EAAqBY,GAA6zLlE,EAAoBD,GAAWA,EAA6B,sBAAEA,EAA6B,uBAA54L,WAAmB,GAAGA,EAAgB,QAAiF,IAAjD,mBAAnBA,EAAgB,UAAcA,EAAgB,QAAE,CAACA,EAAgB,UAASA,EAAgB,QAAEyD,QAA6JyE,EAAxIlI,EAAgB,QAAE2D,QAA0HS,EAAc+D,QAAQD,GAAhD,IAAsBA,EAA5G3E,EAAqBa,EAAc,CAA6qLgE,IAAS,CAArTJ,EAAKA,GAAMxH,EAAc6D,EAAgB,IAA5+L,WAAkB,GAAGrE,EAAe,OAA8E,IAA/C,mBAAlBA,EAAe,SAAcA,EAAe,OAAE,CAACA,EAAe,SAASA,EAAe,OAAEyD,QAA+cyE,EAA3blI,EAAe,OAAE2D,QAA8aM,EAAakE,QAAQD,GAA9C,IAAqBA,EAAha3E,EAAqBU,EAAa,CAA4xLoE,GAAYhE,EAAgB,IAA0OrE,EAAkB,WAAGA,EAAkB,UAAE,cAAcsI,YAAW,WAAWA,YAAW,WAAWtI,EAAkB,UAAE,GAAG,GAAE,GAAGiI,GAAO,GAAE,IAAQA,KAAQ,CAAmB,GAA/6CjI,EAAsB,cAAE,WAAW,OAAqBA,EAAsB,cAAEA,EAAY,IAAK,GAAG6H,MAAM,KAAKC,UAAU,EAAsB9H,EAAwB,gBAAE,WAAW,OAAuBA,EAAwB,gBAAEA,EAAY,IAAK,GAAG6H,MAAM,KAAKC,UAAU,EAAc9H,EAAgB,QAAE,WAAW,OAAeA,EAAgB,QAAEA,EAAY,IAAK,GAAG6H,MAAM,KAAKC,UAAU,EAAuB9H,EAAyB,iBAAE,WAAW,OAAwBA,EAAyB,iBAAEA,EAAY,IAAK,GAAG6H,MAAM,KAAKC,UAAU,EAAY9H,EAAc,MAAE,WAAW,OAAaA,EAAc,MAAEA,EAAY,IAAK,GAAG6H,MAAM,KAAKC,UAAU,EAA6B9H,EAA+B,uBAAE,WAAW,OAA8BA,EAA+B,uBAAEA,EAAY,IAAK,GAAG6H,MAAM,KAAKC,UAAU,EAAgBvD,EAAsB,SAASgE,IAAgB7C,GAAUqC,IAAUrC,IAAUnB,EAAsBgE,EAAS,EAAuevI,EAAY,IAAE+H,EAAO/H,EAAgB,QAAiF,IAAjD,mBAAnBA,EAAgB,UAAcA,EAAgB,QAAE,CAACA,EAAgB,UAASA,EAAgB,QAAEyD,OAAO,GAAGzD,EAAgB,QAAEwI,KAAlBxI,GAGxxV,OAHs0V+H,IAG/zV/H,EAAOyI,KAChB,GIPO,MAAMC,EAAgC,IAKvCC,EAAsD,EAAxBD,EAYrB,MAAME,EAqCjBC,WAAAA,CAAYC,G,YA/BZ,K,GAGsB,G,oSAHtB,iB,wFAkCI,IAMI,GALAC,KAAKC,eAAiBF,EAGtBC,KAAKE,cAAgBF,KAAKC,eAAeE,QAAQP,IAE5CI,KAAKE,cACN,MAAME,MAAM,8CAKhB,GAFAJ,KAAKK,eAAiBL,KAAKC,eAAeE,QAAQP,IAE7CI,KAAKK,eAEN,MADAN,EAAcO,MAAMN,KAAKE,eACnBE,MAAM,+CAMhBJ,KAAKO,sBAAwBP,KAAKE,cAAgB,EAElDF,KAAKQ,SAAWR,KAAKC,eAAeQ,iBACxC,CAAE,MAAOC,GAGL,MADAV,KAAKW,wBACCD,CACV,CACJ,CAQAE,0BAAAA,CAA2BC,GACvBb,KAAKC,eAAea,QAAQC,IAAIF,EAAWb,KAAKO,sBACpD,CAQAS,kBAAAA,CAAmBC,GACf,IAAK,MAAQC,EAAOtK,KAAWqK,EAASE,UACpCF,EAASC,GAAiB,MAARtK,CAE1B,CAQA+J,qBAAAA,GAEQX,KAAKE,gBACLF,KAAKC,eAAeK,MAAMN,KAAKE,eAC/BF,KAAKE,cAAgB,MAGrBF,KAAKK,iBACLL,KAAKC,eAAeK,MAAMN,KAAKK,gBAC/BL,KAAKK,eAAiB,MAGtBL,KAAKQ,WACLR,KAAKC,eAAemB,iBAAiBpB,KAAKQ,UAC1CR,KAAKQ,SAAW,KAExB,CAOAa,eAAAA,GACI,OAAO1B,CACX,CAOA2B,uBAAAA,GACI,OA1IsB,KA2I1B,CAQAC,OAAAA,GAEQvB,KAAKwB,aAITxB,KAAKW,wBAELX,KAAKwB,YAAa,EACtB,CASAC,sBAAAA,CAAuBC,GACnB,GAAI1B,KAAKwB,WACL,MAAM,IAAIpB,MAAM,sEAGpB,MAAMuB,EAAiBD,EAAShH,OAEhC,GAAIiH,IAAmBhC,EACnB,MAAM,IAAIS,MAAM,wEAAwEuB,KAM5F,OAHA3B,KAAKgB,mBAAmBU,GACxB1B,KAAKY,2BAA2Bc,GAEzB1B,KAAKC,eAAe2B,uBAAuB5B,KAAKQ,SAAUR,KAAKK,eAAgBL,KAAKE,cAC/F,ECxLJ,IAAI2B,EAOG,SAASC,IAKZ,OAJKD,IACDA,EAAgBE,KAGbF,EAAcvD,MAAK0D,GAAO,IAAInC,EAAiBmC,IAC1D,I", "sources": ["webpack://MeetHourJS.app.effects.rnnoise/webpack/bootstrap", "webpack://MeetHourJS.app.effects.rnnoise/./node_modules/rnnoise-wasm/dist/index.js", "webpack://MeetHourJS.app.effects.rnnoise/webpack/runtime/define property getters", "webpack://MeetHourJS.app.effects.rnnoise/webpack/runtime/hasOwnProperty shorthand", "webpack://MeetHourJS.app.effects.rnnoise/webpack/runtime/make namespace object", "webpack://MeetHourJS.app.effects.rnnoise/./react/features/stream-effects/rnnoise/RnnoiseProcessor.js", "webpack://MeetHourJS.app.effects.rnnoise/./react/features/stream-effects/rnnoise/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "\nvar Module = (function() {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(Module) {\n  Module = Module || {};\n\nnull;var Module=typeof Module!==\"undefined\"?Module:{};var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram=\"./this.program\";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;ENVIRONMENT_IS_WEB=typeof window===\"object\";ENVIRONMENT_IS_WORKER=typeof importScripts===\"function\";ENVIRONMENT_IS_NODE=typeof process===\"object\"&&typeof process.versions===\"object\"&&typeof process.versions.node===\"string\";ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=function shell_read(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function readBinary(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function readAsync(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=function xhr_onload(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];var noExitRuntime;if(Module[\"noExitRuntime\"])noExitRuntime=Module[\"noExitRuntime\"];if(typeof WebAssembly!==\"object\"){err(\"no native wasm support detected\")}var wasmMemory;var wasmTable=new WebAssembly.Table({\"initial\":1,\"maximum\":1+0,\"element\":\"anyfunc\"});var ABORT=false;var EXITSTATUS=0;var WASM_PAGE_SIZE=65536;function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module[\"HEAP8\"]=HEAP8=new Int8Array(buf);Module[\"HEAP16\"]=HEAP16=new Int16Array(buf);Module[\"HEAP32\"]=HEAP32=new Int32Array(buf);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(buf);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(buf);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(buf);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(buf);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(buf)}var DYNAMIC_BASE=5340496,DYNAMICTOP_PTR=97456;var INITIAL_INITIAL_MEMORY=Module[\"INITIAL_MEMORY\"]||16777216;if(Module[\"wasmMemory\"]){wasmMemory=Module[\"wasmMemory\"]}else{wasmMemory=new WebAssembly.Memory({\"initial\":INITIAL_INITIAL_MEMORY/WASM_PAGE_SIZE,\"maximum\":2147483648/WASM_PAGE_SIZE})}if(wasmMemory){buffer=wasmMemory.buffer}INITIAL_INITIAL_MEMORY=buffer.byteLength;updateGlobalBufferAndViews(buffer);HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE;function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback==\"function\"){callback(Module);continue}var func=callback.func;if(typeof func===\"number\"){if(callback.arg===undefined){Module[\"dynCall_v\"](func)}else{Module[\"dynCall_vi\"](func,callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module[\"preloadedImages\"]={};Module[\"preloadedAudios\"]={};function abort(what){if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}what+=\"\";out(what);err(what);ABORT=true;EXITSTATUS=1;what=\"abort(\"+what+\"). Build with -s ASSERTIONS=1 for more info.\";throw new WebAssembly.RuntimeError(what)}function hasPrefix(str,prefix){return String.prototype.startsWith?str.startsWith(prefix):str.indexOf(prefix)===0}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return hasPrefix(filename,dataURIPrefix)}var wasmBinaryFile=\"rnnoise.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(){try{if(wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(wasmBinaryFile)}else{throw\"both async and sync fetching of the wasm failed\"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+wasmBinaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(function(){return getBinary()})}return new Promise(function(resolve,reject){resolve(getBinary())})}function createWasm(){var info={\"a\":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module[\"asm\"]=exports;removeRunDependency(\"wasm-instantiate\")}addRunDependency(\"wasm-instantiate\");function receiveInstantiatedSource(output){receiveInstance(output[\"instance\"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(receiver,function(reason){err(\"failed to asynchronously prepare wasm: \"+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming===\"function\"&&!isDataURI(wasmBinaryFile)&&typeof fetch===\"function\"){fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiatedSource,function(reason){err(\"wasm streaming compile failed: \"+reason);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(receiveInstantiatedSource)})})}else{return instantiateArrayBuffer(receiveInstantiatedSource)}}if(Module[\"instantiateWasm\"]){try{var exports=Module[\"instantiateWasm\"](info,receiveInstance);return exports}catch(e){err(\"Module.instantiateWasm callback failed with error: \"+e);return false}}instantiateAsync();return{}}__ATINIT__.push({func:function(){___wasm_call_ctors()}});function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function _emscripten_get_heap_size(){return HEAPU8.length}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){requestedSize=requestedSize>>>0;var oldSize=_emscripten_get_heap_size();var PAGE_MULTIPLE=65536;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}var minHeapSize=16777216;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(minHeapSize,requestedSize,overGrownHeapSize),PAGE_MULTIPLE));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}var asmLibraryArg={\"a\":_emscripten_memcpy_big,\"b\":_emscripten_resize_heap,\"memory\":wasmMemory,\"table\":wasmTable};var asm=createWasm();var ___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=function(){return(___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=Module[\"asm\"][\"c\"]).apply(null,arguments)};var _rnnoise_init=Module[\"_rnnoise_init\"]=function(){return(_rnnoise_init=Module[\"_rnnoise_init\"]=Module[\"asm\"][\"d\"]).apply(null,arguments)};var _rnnoise_create=Module[\"_rnnoise_create\"]=function(){return(_rnnoise_create=Module[\"_rnnoise_create\"]=Module[\"asm\"][\"e\"]).apply(null,arguments)};var _malloc=Module[\"_malloc\"]=function(){return(_malloc=Module[\"_malloc\"]=Module[\"asm\"][\"f\"]).apply(null,arguments)};var _rnnoise_destroy=Module[\"_rnnoise_destroy\"]=function(){return(_rnnoise_destroy=Module[\"_rnnoise_destroy\"]=Module[\"asm\"][\"g\"]).apply(null,arguments)};var _free=Module[\"_free\"]=function(){return(_free=Module[\"_free\"]=Module[\"asm\"][\"h\"]).apply(null,arguments)};var _rnnoise_process_frame=Module[\"_rnnoise_process_frame\"]=function(){return(_rnnoise_process_frame=Module[\"_rnnoise_process_frame\"]=Module[\"asm\"][\"i\"]).apply(null,arguments)};var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0)return;function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();preMain();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}Module[\"run\"]=run;if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}noExitRuntime=true;run();\n\n\n  return Module.ready\n}\n);\n})();\nexport default Module;", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// @flow\n\n/**\n * Constant. Rnnoise default sample size, samples of different size won't work.\n */\nexport const RNNOISE_SAMPLE_LENGTH: number = 480;\n\n/**\n *  Constant. Rnnoise only takes inputs of 480 PCM float32 samples thus 480*4.\n */\nconst RNNOISE_BUFFER_SIZE: number = RNNOISE_SAMPLE_LENGTH * 4;\n\n/**\n *  Constant. Rnnoise only takes operates on 44.1Khz float 32 little endian PCM.\n */\nconst PCM_FREQUENCY: number = 44100;\n\n/**\n * Represents an adaptor for the rnnoise library compiled to webassembly. The class takes care of webassembly\n * memory management and exposes rnnoise functionality such as PCM audio denoising and VAD (voice activity\n * detection) scores.\n */\nexport default class RnnoiseProcessor {\n    /**\n     * Rnnoise context object needed to perform the audio processing.\n     */\n    _context: ?Object;\n\n    /**\n     * State flag, check if the instance was destroyed.\n     */\n    _destroyed: boolean = false;\n\n    /**\n     * WASM interface through which calls to rnnoise are made.\n     */\n    _wasmInterface: Object;\n\n    /**\n     * WASM dynamic memory buffer used as input for rnnoise processing method.\n     */\n    _wasmPcmInput: Object;\n\n    /**\n     * The Float32Array index representing the start point in the wasm heap of the _wasmPcmInput buffer.\n     */\n    _wasmPcmInputF32Index: number;\n\n    /**\n     * WASM dynamic memory buffer used as output for rnnoise processing method.\n     */\n    _wasmPcmOutput: Object;\n\n    /**\n     * Constructor.\n     *\n     * @class\n     * @param {Object} wasmInterface - WebAssembly module interface that exposes rnnoise functionality.\n     */\n    constructor(wasmInterface: Object) {\n        // Considering that we deal with dynamic allocated memory employ exception safety strong guarantee\n        // i.e. in case of exception there are no side effects.\n        try {\n            this._wasmInterface = wasmInterface;\n\n            // For VAD score purposes only allocate the buffers once and reuse them\n            this._wasmPcmInput = this._wasmInterface._malloc(RNNOISE_BUFFER_SIZE);\n\n            if (!this._wasmPcmInput) {\n                throw Error('Failed to create wasm input memory buffer!');\n            }\n\n            this._wasmPcmOutput = this._wasmInterface._malloc(RNNOISE_BUFFER_SIZE);\n\n            if (!this._wasmPcmOutput) {\n                wasmInterface._free(this._wasmPcmInput);\n                throw Error('Failed to create wasm output memory buffer!');\n            }\n\n            // The HEAPF32.set function requires an index relative to a Float32 array view of the wasm memory model\n            // which is an array of bytes. This means we have to divide it by the size of a float to get the index\n            // relative to a Float32 Array.\n            this._wasmPcmInputF32Index = this._wasmPcmInput / 4;\n\n            this._context = this._wasmInterface._rnnoise_create();\n        } catch (error) {\n            // release can be called even if not all the components were initialized.\n            this._releaseWasmResources();\n            throw error;\n        }\n    }\n\n    /**\n     * Copy the input PCM Audio Sample to the wasm input buffer.\n     *\n     * @param {Float32Array} pcmSample - Array containing 16 bit format PCM sample stored in 32 Floats .\n     * @returns {void}\n     */\n    _copyPCMSampleToWasmBuffer(pcmSample: Float32Array) {\n        this._wasmInterface.HEAPF32.set(pcmSample, this._wasmPcmInputF32Index);\n    }\n\n    /**\n     * Convert 32 bit Float PCM samples to 16 bit Float PCM samples and store them in 32 bit Floats.\n     *\n     * @param {Float32Array} f32Array - Array containing 32 bit PCM samples.\n     * @returns {void}\n     */\n    _convertTo16BitPCM(f32Array: Float32Array) {\n        for (const [ index, value ] of f32Array.entries()) {\n            f32Array[index] = value * 0x7fff;\n        }\n    }\n\n    /**\n     * Release resources associated with the wasm context. If something goes downhill here\n     * i.e. Exception is thrown, there is nothing much we can do.\n     *\n     * @returns {void}\n     */\n    _releaseWasmResources() {\n        // For VAD score purposes only allocate the buffers once and reuse them\n        if (this._wasmPcmInput) {\n            this._wasmInterface._free(this._wasmPcmInput);\n            this._wasmPcmInput = null;\n        }\n\n        if (this._wasmPcmOutput) {\n            this._wasmInterface._free(this._wasmPcmOutput);\n            this._wasmPcmOutput = null;\n        }\n\n        if (this._context) {\n            this._wasmInterface._rnnoise_destroy(this._context);\n            this._context = null;\n        }\n    }\n\n    /**\n     * Rnnoise can only operate on a certain PCM array size.\n     *\n     * @returns {number} - The PCM sample array size as required by rnnoise.\n     */\n    getSampleLength() {\n        return RNNOISE_SAMPLE_LENGTH;\n    }\n\n    /**\n     * Rnnoise can only operate on a certain format of PCM sample namely float 32 44.1Kz.\n     *\n     * @returns {number} - PCM sample frequency as required by rnnoise.\n     */\n    getRequiredPCMFrequency() {\n        return PCM_FREQUENCY;\n    }\n\n    /**\n     * Release any resources required by the rnnoise context this needs to be called\n     * before destroying any context that uses the processor.\n     *\n     * @returns {void}\n     */\n    destroy() {\n        // Attempting to release a non initialized processor, do nothing.\n        if (this._destroyed) {\n            return;\n        }\n\n        this._releaseWasmResources();\n\n        this._destroyed = true;\n    }\n\n    /**\n     * Calculate the Voice Activity Detection for a raw Float32 PCM sample Array.\n     * The size of the array must be of exactly 480 samples, this constraint comes from the rnnoise library.\n     *\n     * @param {Float32Array} pcmFrame - Array containing 32 bit PCM samples.\n     * @returns {Float} Contains VAD score in the interval 0 - 1 i.e. 0.90 .\n     */\n    calculateAudioFrameVAD(pcmFrame: Float32Array) {\n        if (this._destroyed) {\n            throw new Error('RnnoiseProcessor instance is destroyed, please create another one!');\n        }\n\n        const pcmFrameLength = pcmFrame.length;\n\n        if (pcmFrameLength !== RNNOISE_SAMPLE_LENGTH) {\n            throw new Error(`Rnnoise can only process PCM frames of 480 samples! Input sample was:${pcmFrameLength}`);\n        }\n\n        this._convertTo16BitPCM(pcmFrame);\n        this._copyPCMSampleToWasmBuffer(pcmFrame);\n\n        return this._wasmInterface._rnnoise_process_frame(this._context, this._wasmPcmOutput, this._wasmPcmInput);\n    }\n}\n", "// @flow\n\n// <PERSON>ript expects to find rnnoise webassembly binary in the same public path root, otherwise it won't load\n// During the build phase this needs to be taken care of manually\nimport rnnoiseWasmInit from 'rnnoise-wasm';\n\nimport RnnoiseProcessor from './RnnoiseProcessor';\n\nexport { RNNOISE_SAMPLE_LENGTH } from './RnnoiseProcessor';\nexport type { RnnoiseProcessor };\n\nlet rnnoiseModule;\n\n/**\n * Creates a new instance of RnnoiseProcessor.\n *\n * @returns {Promise<RnnoiseProcessor>}\n */\nexport function createRnnoiseProcessor() {\n    if (!rnnoiseModule) {\n        rnnoiseModule = rnnoiseWasmInit();\n    }\n\n    return rnnoiseModule.then(mod => new RnnoiseProcessor(mod));\n}\n"], "names": ["_scriptDir", "__webpack_require__", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "document", "currentScript", "src", "undefined", "<PERSON><PERSON><PERSON>", "readyPromiseResolve", "Promise", "resolve", "reject", "moduleOverrides", "ENVIRONMENT_IS_WEB", "ENVIRONMENT_IS_WORKER", "arguments_", "window", "importScripts", "process", "versions", "node", "readBinary", "scriptDirectory", "self", "location", "href", "indexOf", "substr", "lastIndexOf", "url", "xhr", "XMLHttpRequest", "open", "responseType", "send", "Uint8Array", "response", "wasmBinary", "was<PERSON><PERSON><PERSON><PERSON>", "out", "console", "log", "bind", "err", "warn", "WebAssembly", "buffer", "HEAPU8", "HEAP32", "wasmTable", "Table", "ABORT", "updateGlobalBufferAndViews", "buf", "Int8Array", "Int16Array", "Int32Array", "Uint16Array", "Uint32Array", "Float32Array", "Float64Array", "INITIAL_INITIAL_MEMORY", "callRuntimeCallbacks", "callbacks", "length", "callback", "shift", "func", "arg", "Memory", "byteLength", "DYNAMICTOP_PTR", "__ATPRERUN__", "__ATINIT__", "__ATMAIN__", "__ATPOSTRUN__", "runDependencies", "runDependencyWatcher", "dependenciesFulfilled", "abort", "what", "RuntimeError", "isDataURI", "filename", "str", "prefix", "String", "startsWith", "path", "wasmBinaryFile", "getBinary", "emscripten_realloc_buffer", "size", "grow", "e", "push", "___wasm_call_ctors", "calledRun", "asmLibraryArg", "dest", "num", "copyWithin", "requestedSize", "oldSize", "maxHeapSize", "x", "cutDown", "overGrownHeapSize", "Math", "min", "max", "info", "receiveInstance", "instance", "module", "id", "clearInterval", "removeRunDependency", "receiveInstantiatedSource", "output", "instantiateArrayBuffer", "receiver", "fetch", "credentials", "then", "catch", "binary", "instantiate", "reason", "instantiateStreaming", "instantiateAsync", "createWasm", "apply", "arguments", "run", "args", "doRun", "cb", "unshift", "postRun", "preRun", "setTimeout", "runCaller", "pop", "ready", "RNNOISE_SAMPLE_LENGTH", "RNNOISE_BUFFER_SIZE", "RnnoiseProcessor", "constructor", "wasmInterface", "this", "_wasmInterface", "_wasmPcmInput", "_malloc", "Error", "_wasmPcmOutput", "_free", "_wasmPcmInputF32Index", "_context", "_rnnoise_create", "error", "_releaseWasmResources", "_copyPCMSampleToWasmBuffer", "pcmSample", "HEAPF32", "set", "_convertTo16BitPCM", "f32Array", "index", "entries", "_rnnoise_destroy", "getSampleLength", "getRequiredPCMFrequency", "destroy", "_destroyed", "calculateAudioFrameVAD", "pcmFrame", "pcm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_rnnoise_process_frame", "rnnoiseModule", "createRnnoiseProcessor", "rnnoiseWasmInit", "mod"], "sourceRoot": ""}