/* global ga */

(function(ctx) {
    /**
     *
     */
    function Analytics(options) {
        /* eslint-disable */

        if (!options.googleAnalyticsTrackingId) {
            console.log(
                'Failed to initialize Google Analytics handler, no tracking ID');
             return;
        }
        /**
         * Google Analytics
         * TODO: Keep this local, there's no need to add it to window.
         */
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer',options.googleAnalyticsTrackingId);
        if(localStorage.getItem("features/base/settings")){
            const userData = {
                displayName: localStorage.getItem("features/base/settings")?.displayName,
                email: localStorage.getItem("features/base/settings")?.email,
                avatarURL: localStorage.getItem("features/base/settings")?.avatarURL
            }
            gtag('user_data', userData)
        }
        /* eslint-enable */
    }

    /**
     * Extracts the integer to use for a Google Analytics event's value field
     * from a lib-meet-hour analytics event.
     *
     * @param {Object} event - The lib-meet-hour analytics event.
     * @returns {Object} - The integer to use for the 'value' of a Google
     * Analytics event.
     * @private
     */
    Analytics.prototype._extractAction = function(event) {
        // Page events have a single 'name' field.
        if (event.type === 'page') {
            return event.name;
        }

        // All other events have action, actionSubject, and source fields. All
        // three fields are required, and the often meet-hour and
        // lib-meet-hour use the same value when separate values are not
        // necessary (i.e. event.action == event.actionSubject).
        // Here we concatenate these three fields, but avoid adding the same
        // value twice, because it would only make the GA event's action harder
        // to read.
        let action = event.action;

        if (event.actionSubject && event.actionSubject !== event.action) {
            // Intentionally use string concatenation as analytics needs to
            // work on IE but this file does not go through babel. For some
            // reason disabling this globally for the file does not have an
            // effect.
            // eslint-disable-next-line prefer-template
            action = event.actionSubject + '.' + action;
        }
        if (event.source && event.source !== event.action
                && event.source !== event.action) {
            // eslint-disable-next-line prefer-template
            action = event.source + '.' + action;
        }

        return action;
    };

    /**
     * Extracts the integer to use for a Google Analytics event's value field
     * from a lib-meet-hour analytics event.
     *
     * @param {Object} event - The lib-meet-hour analytics event.
     * @returns {Object} - The integer to use for the 'value' of a Google
     * Analytics event, or NaN if the lib-meet-hour event doesn't contain a
     * suitable value.
     * @private
     */
    Analytics.prototype._extractValue = function(event) {
        let value = event && event.attributes && event.attributes.value;

        // Try to extract an integer from the "value" attribute.
        value = Math.round(parseFloat(value));

        return value;
    };

    /**
     * Extracts the string to use for a Google Analytics event's label field
     * from a lib-meet-hour analytics event.
     *
     * @param {Object} event - The lib-meet-hour analytics event.
     * @returns {string} - The string to use for the 'label' of a Google
     * Analytics event.
     * @private
     */
    Analytics.prototype._extractLabel = function(event) {
        let label = '';

        // The label field is limited to 500B. We will concatenate all
        // attributes of the event, except the user agent because it may be
        // lengthy and is probably included from elsewhere.
        for (const property in event.attributes) {
            if (property !== 'permanent_user_agent'
                && property !== 'permanent_callstats_name'
                && event.attributes.hasOwnProperty(property)) {
                // eslint-disable-next-line prefer-template
                label += property + '=' + event.attributes[property] + '&';
            }
        }

        if (label.length > 0) {
            label = label.slice(0, -1);
        }

        return label;
    };

    /**
     * This is the entry point of the API. The function sends an event to
     * google analytics. The format of the event is described in
     * AnalyticsAdapter in lib-meet-hour.
     *
     * @param {Object} event - The event in the format specified by
     * lib-meet-hour.
     */
    Analytics.prototype.sendEvent = function(event) {
        if (!event || !ga) {
            return;
        }

        const ignoredEvents
            = [ 'e2e_rtt', 'rtp.stats', 'rtt.by.region', 'available.device',
                'stream.switch.delay', 'ice.state.changed', 'ice.duration' ];

        // Temporary removing some of the events that are too noisy.
        if (ignoredEvents.indexOf(event.action) !== -1) {
            return;
        }

        const gaEvent = {
            'eventCategory': 'meet-hour',
            'eventAction': this._extractAction(event),
            'eventLabel': this._extractLabel(event)
        };
        const value = this._extractValue(event);

        if (!isNaN(value)) {
            gaEvent.eventValue = value;
        }

        ga('send', 'event', gaEvent);
    };

    if (typeof ctx.MeetHourJS === 'undefined') {
        ctx.MeetHourJS = {};
    }
    if (typeof ctx.MeetHourJS.app === 'undefined') {
        ctx.MeetHourJS.app = {};
    }
    if (typeof ctx.MeetHourJS.app.analyticsHandlers === 'undefined') {
        ctx.MeetHourJS.app.analyticsHandlers = [];
    }
    ctx.MeetHourJS.app.analyticsHandlers.push(Analytics);
})(window);
/* eslint-enable prefer-template */
