(()=>{var e={759:e=>{"use strict";function t(e,t){if(!f)return!1;const r=e.buffer;let a=l.get(r);if(null==a){if((a=n.validate(r))&&t)try{new n.Instance(new n.Module(r)).exports[0]()}catch(e){a=!1}l.set(r,a)}return a}const n=WebAssembly,r=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Uint8Array.of(0,97,115,109,1,0,0,0,...t)},a=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Uint32Array.of(1836278016,1,...t)},i=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r(1,4,1,96,0,0,3,2,1,0,...t,11,0,10,4,110,97,109,101,2,3,1,0,0)},s=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Uint16Array.of(24832,28019,1,0,1025,24577,0,515,1,...t)},o=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a(1610679297,33751040,...t,40239360,259)},u=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return s(...t,2842,4096,28164,28001,357,260,256,560,259,0)},c=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return s(...t,2560,28164,28001,613,259,0)},f="object"==typeof n,p=e=>f&&"function"==typeof e,l=new WeakMap,m=a(1610679553,58589440,117440770,805372165,101318656,1107297281,268438272,1835101700,17039717,36700416,259),d=c(773,1,2561,269,11,65,65,65,3068,2816),h=c(781,1,2560,265,7,16390,2311,2827),g=r(2,8,1,1,97,1,98,3,127,1,6,6,1,127,1,65,0,11,7,5,1,1,97,3,1,0,8,4,110,97,109,101,2,1,0),y=Uint16Array.of(24832,28019,1,0,1537,24577,512,32639,515,1,2058,1537,16640,16640,2816,2560,28164,28001,613,259,0),v=u(3082,2561,17152,0,0,252),_=u(2058,1537,16640,49152),w=o(101318657,301990913,268438272,1835101700,17039717),b=i(5,4,1,3,1,1,10,7,1,5,0,254,3,0),E=o(84344833,6357249,17369600,4259847,186257917,1845758464),A=i(10,7,1,5,0,208,112,26);e.exports={support:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return t(Uint32Array.of(1836278016,e))},get supportStreaming(){return p(n.instantiateStreaming)},feature:{get bigInt(){return t(m,!0)},get bulk(){return t(d)},get exceptions(){return t(h)},get mutableGlobal(){return t(g)},get multiValue(){return t(y)},get saturateConversions(){return t(v)},get signExtensions(){return t(_)},get tailCall(){return t(w)},get threads(){return t(b)},get simd(){return t(E)},get references(){return t(A)},get typeReflection(){return p(n.Memory.type)},get funcReferences(){return p(n.Function)}}}},599:(e,t,n)=>{var r,a=(r=(r="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0)||"react/features/stream-effects/virtual-background/vendor/tflite/tflite-simd.js",function(e){var t,a,i=void 0!==(e=e||{})?e:{};i.ready=new Promise((function(e,n){t=e,a=n}));var s,o={};for(s in i)i.hasOwnProperty(s)&&(o[s]=i[s]);var u,c,f,p,l=[],m="./this.program",d=function(e,t){throw t};f="object"==typeof window,p="function"==typeof importScripts,u="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,c=!f&&!u&&!p;var h,g,y,v,_,w="";u?(w=p?n(940).dirname(w)+"/":"//",h=function(e,t){return v||(v=n(853)),_||(_=n(940)),e=_.normalize(e),v.readFileSync(e,t?null:"utf8")},y=function(e){var t=h(e,!0);return t.buffer||(t=new Uint8Array(t)),k(t.buffer),t},process.argv.length>1&&(m=process.argv[1].replace(/\\/g,"/")),l=process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof oe))throw e})),process.on("unhandledRejection",q),d=function(e){process.exit(e)},i.inspect=function(){return"[Emscripten Module object]"}):c?("undefined"!=typeof read&&(h=function(e){return read(e)}),y=function(e){var t;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(k("object"==typeof(t=read(e,"binary"))),t)},"undefined"!=typeof scriptArgs?l=scriptArgs:void 0!==arguments&&(l=arguments),"function"==typeof quit&&(d=function(e){quit(e)}),"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)):(f||p)&&(p?w=self.location.href:"undefined"!=typeof document&&document.currentScript&&(w=document.currentScript.src),r&&(w=r),w=0!==w.indexOf("blob:")?w.substr(0,w.lastIndexOf("/")+1):"",h=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},p&&(y=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),g=function(e,t,n){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=function(){200==r.status||0==r.status&&r.response?t(r.response):n()},r.onerror=n,r.send(null)});var b,E,A,C=i.print||console.log.bind(console),M=i.printErr||console.warn.bind(console);for(s in o)o.hasOwnProperty(s)&&(i[s]=o[s]);o=null,i.arguments&&(l=i.arguments),i.thisProgram&&(m=i.thisProgram),i.quit&&(d=i.quit),i.wasmBinary&&(b=i.wasmBinary),i.noExitRuntime&&(E=i.noExitRuntime),"object"!=typeof WebAssembly&&q("no native wasm support detected");var I=!1;function k(e,t){e||q("Assertion failed: "+t)}var x,R,S,O,P="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function T(e,t,n){for(var r=t+n,a=t;e[a]&&!(a>=r);)++a;if(a-t>16&&e.subarray&&P)return P.decode(e.subarray(t,a));for(var i="";t<a;){var s=e[t++];if(128&s){var o=63&e[t++];if(192!=(224&s)){var u=63&e[t++];if((s=224==(240&s)?(15&s)<<12|o<<6|u:(7&s)<<18|o<<12|u<<6|63&e[t++])<65536)i+=String.fromCharCode(s);else{var c=s-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&s)<<6|o)}else i+=String.fromCharCode(s)}return i}function H(e){x=e,i.HEAP8=R=new Int8Array(e),i.HEAP16=new Int16Array(e),i.HEAP32=O=new Int32Array(e),i.HEAPU8=S=new Uint8Array(e),i.HEAPU16=new Uint16Array(e),i.HEAPU32=new Uint32Array(e),i.HEAPF32=new Float32Array(e),i.HEAPF64=new Float64Array(e)}i.INITIAL_MEMORY;var W,U=[],F=[],j=[],B=[];F.push({func:function(){ie()}});var V=0,D=null,L=null;function q(e){i.onAbort&&i.onAbort(e),M(e+=""),I=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(e);throw a(t),t}function N(e,t){return String.prototype.startsWith?e.startsWith(t):0===e.indexOf(t)}i.preloadedImages={},i.preloadedAudios={};function G(e){return N(e,"data:application/octet-stream;base64,")}function z(e){return N(e,"file://")}var X,J,Y="tflite-simd.wasm";function K(e){try{if(e==Y&&b)return new Uint8Array(b);if(y)return y(e);throw"both async and sync fetching of the wasm failed"}catch(e){q(e)}}function Q(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var n=t.func;"number"==typeof n?void 0===t.arg?W.get(n)():W.get(n)(t.arg):n(void 0===t.arg?null:t.arg)}else t(i)}}function Z(e){return O[se()>>2]=e,e}function $(e){try{return A.grow(e-x.byteLength+65535>>>16),H(A.buffer),1}catch(e){}}G(Y)||(X=Y,Y=i.locateFile?i.locateFile(X,w):w+X),J=u?function(){var e=process.hrtime();return 1e3*e[0]+e[1]/1e6}:"undefined"!=typeof dateNow?dateNow:function(){return performance.now()};var ee={};function te(){if(!te.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:m||"./this.program"};for(var t in ee)e[t]=ee[t];var n=[];for(var t in e)n.push(t+"="+e[t]);te.strings=n}return te.strings}var ne,re={mappings:{},buffers:[null,[],[]],printChar:function(e,t){var n=re.buffers[e];0===t||10===t?((1===e?C:M)(T(n,0)),n.length=0):n.push(t)},varargs:void 0,get:function(){return re.varargs+=4,O[re.varargs-4>>2]},getStr:function(e){var t=function(e,t){return e?T(S,e,void 0):""}(e);return t},get64:function(e,t){return e}},ae={a:function(){q()},n:function(e,t){var n;if(0===e)n=Date.now();else{if(1!==e&&4!==e)return Z(28),-1;n=J()}return O[t>>2]=n/1e3|0,O[t+4>>2]=n%1e3*1e3*1e3|0,0},i:function(e,t){q("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")},e:function(e,t){q("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")},l:function(e,t,n){S.copyWithin(e,t,t+n)},m:function(e){e>>>=0;var t=S.length,n=2147483648;if(e>n)return!1;for(var r,a=1;a<=4;a*=2){var i=t*(1+.2/a);if(i=Math.min(i,e+100663296),$(Math.min(n,((r=Math.max(16777216,e,i))%65536>0&&(r+=65536-r%65536),r))))return!0}return!1},o:function(e){for(var t=J();J()-t<e;);},p:function(e,t){var n=0;return te().forEach((function(r,a){var i=t+n;O[e+4*a>>2]=i,function(e,t,n){for(var r=0;r<e.length;++r)R[0|t++]=e.charCodeAt(r);R[0|t]=0}(r,i),n+=r.length+1})),0},g:function(e,t){var n=te();O[e>>2]=n.length;var r=0;return n.forEach((function(e){r+=e.length+1})),O[t>>2]=r,0},j:function(e){!function(e,t){E||(i.onExit&&i.onExit(e),I=!0),d(e,new oe(e))}(e)},h:function(e){return 0},k:function(e,t,n,r,a){},c:function(e,t,n,r){for(var a=0,i=0;i<n;i++){for(var s=O[t+8*i>>2],o=O[t+(8*i+4)>>2],u=0;u<o;u++)re.printChar(e,S[s+u]);a+=o}return O[r>>2]=a,0},d:function(){return 6},f:function(){return 28},b:function(e){switch(e){case 30:case 75:return 16384;case 85:return 131072;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:case 80:case 81:case 79:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return"object"==typeof navigator&&navigator.hardwareConcurrency||1}return Z(28),-1}},ie=(function(){var e={a:ae};function t(e,t){var n=e.exports;i.asm=n,H((A=i.asm.q).buffer),W=i.asm.D,function(e){if(V--,i.monitorRunDependencies&&i.monitorRunDependencies(V),0==V&&(null!==D&&(clearInterval(D),D=null),L)){var t=L;L=null,t()}}()}function n(e){t(e.instance)}function r(t){return function(){if(!b&&(f||p)){if("function"==typeof fetch&&!z(Y))return fetch(Y,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+Y+"'";return e.arrayBuffer()})).catch((function(){return K(Y)}));if(g)return new Promise((function(e,t){g(Y,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return K(Y)}))}().then((function(t){return WebAssembly.instantiate(t,e)})).then(t,(function(e){M("failed to asynchronously prepare wasm: "+e),q(e)}))}if(V++,i.monitorRunDependencies&&i.monitorRunDependencies(V),i.instantiateWasm)try{return i.instantiateWasm(e,t)}catch(e){return M("Module.instantiateWasm callback failed with error: "+e),!1}(b||"function"!=typeof WebAssembly.instantiateStreaming||G(Y)||z(Y)||"function"!=typeof fetch?r(n):fetch(Y,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(n,(function(e){return M("wasm streaming compile failed: "+e),M("falling back to ArrayBuffer instantiation"),r(n)}))}))).catch(a)}(),i.___wasm_call_ctors=function(){return(ie=i.___wasm_call_ctors=i.asm.r).apply(null,arguments)}),se=(i._getModelBufferMemoryOffset=function(){return(i._getModelBufferMemoryOffset=i.asm.s).apply(null,arguments)},i._getInputMemoryOffset=function(){return(i._getInputMemoryOffset=i.asm.t).apply(null,arguments)},i._getInputHeight=function(){return(i._getInputHeight=i.asm.u).apply(null,arguments)},i._getInputWidth=function(){return(i._getInputWidth=i.asm.v).apply(null,arguments)},i._getInputChannelCount=function(){return(i._getInputChannelCount=i.asm.w).apply(null,arguments)},i._getOutputMemoryOffset=function(){return(i._getOutputMemoryOffset=i.asm.x).apply(null,arguments)},i._getOutputHeight=function(){return(i._getOutputHeight=i.asm.y).apply(null,arguments)},i._getOutputWidth=function(){return(i._getOutputWidth=i.asm.z).apply(null,arguments)},i._getOutputChannelCount=function(){return(i._getOutputChannelCount=i.asm.A).apply(null,arguments)},i._loadModel=function(){return(i._loadModel=i.asm.B).apply(null,arguments)},i._runInference=function(){return(i._runInference=i.asm.C).apply(null,arguments)},i.___errno_location=function(){return(se=i.___errno_location=i.asm.E).apply(null,arguments)});function oe(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function ue(e){function n(){ne||(ne=!0,i.calledRun=!0,I||(Q(F),Q(j),t(i),i.onRuntimeInitialized&&i.onRuntimeInitialized(),function(){if(i.postRun)for("function"==typeof i.postRun&&(i.postRun=[i.postRun]);i.postRun.length;)e=i.postRun.shift(),B.unshift(e);var e;Q(B)}()))}e=e||l,V>0||(function(){if(i.preRun)for("function"==typeof i.preRun&&(i.preRun=[i.preRun]);i.preRun.length;)e=i.preRun.shift(),U.unshift(e);var e;Q(U)}(),V>0||(i.setStatus?(i.setStatus("Running..."),setTimeout((function(){setTimeout((function(){i.setStatus("")}),1),n()}),1)):n()))}if(L=function e(){ne||ue(),ne||(L=e)},i.run=ue,i.preInit)for("function"==typeof i.preInit&&(i.preInit=[i.preInit]);i.preInit.length>0;)i.preInit.pop()();return E=!0,ue(),e.ready});e.exports=a},360:(e,t,n)=>{var r,a=(r=(r="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0)||"react/features/stream-effects/virtual-background/vendor/tflite/tflite.js",function(e){var t,a,i=void 0!==(e=e||{})?e:{};i.ready=new Promise((function(e,n){t=e,a=n}));var s,o={};for(s in i)i.hasOwnProperty(s)&&(o[s]=i[s]);var u,c,f,p,l=[],m="./this.program",d=function(e,t){throw t};f="object"==typeof window,p="function"==typeof importScripts,u="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,c=!f&&!u&&!p;var h,g,y,v,_,w="";u?(w=p?n(940).dirname(w)+"/":"//",h=function(e,t){return v||(v=n(853)),_||(_=n(940)),e=_.normalize(e),v.readFileSync(e,t?null:"utf8")},y=function(e){var t=h(e,!0);return t.buffer||(t=new Uint8Array(t)),k(t.buffer),t},process.argv.length>1&&(m=process.argv[1].replace(/\\/g,"/")),l=process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof oe))throw e})),process.on("unhandledRejection",q),d=function(e){process.exit(e)},i.inspect=function(){return"[Emscripten Module object]"}):c?("undefined"!=typeof read&&(h=function(e){return read(e)}),y=function(e){var t;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(k("object"==typeof(t=read(e,"binary"))),t)},"undefined"!=typeof scriptArgs?l=scriptArgs:void 0!==arguments&&(l=arguments),"function"==typeof quit&&(d=function(e){quit(e)}),"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)):(f||p)&&(p?w=self.location.href:"undefined"!=typeof document&&document.currentScript&&(w=document.currentScript.src),r&&(w=r),w=0!==w.indexOf("blob:")?w.substr(0,w.lastIndexOf("/")+1):"",h=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},p&&(y=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),g=function(e,t,n){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=function(){200==r.status||0==r.status&&r.response?t(r.response):n()},r.onerror=n,r.send(null)});var b,E,A,C=i.print||console.log.bind(console),M=i.printErr||console.warn.bind(console);for(s in o)o.hasOwnProperty(s)&&(i[s]=o[s]);o=null,i.arguments&&(l=i.arguments),i.thisProgram&&(m=i.thisProgram),i.quit&&(d=i.quit),i.wasmBinary&&(b=i.wasmBinary),i.noExitRuntime&&(E=i.noExitRuntime),"object"!=typeof WebAssembly&&q("no native wasm support detected");var I=!1;function k(e,t){e||q("Assertion failed: "+t)}var x,R,S,O,P="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function T(e,t,n){for(var r=t+n,a=t;e[a]&&!(a>=r);)++a;if(a-t>16&&e.subarray&&P)return P.decode(e.subarray(t,a));for(var i="";t<a;){var s=e[t++];if(128&s){var o=63&e[t++];if(192!=(224&s)){var u=63&e[t++];if((s=224==(240&s)?(15&s)<<12|o<<6|u:(7&s)<<18|o<<12|u<<6|63&e[t++])<65536)i+=String.fromCharCode(s);else{var c=s-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&s)<<6|o)}else i+=String.fromCharCode(s)}return i}function H(e){x=e,i.HEAP8=R=new Int8Array(e),i.HEAP16=new Int16Array(e),i.HEAP32=O=new Int32Array(e),i.HEAPU8=S=new Uint8Array(e),i.HEAPU16=new Uint16Array(e),i.HEAPU32=new Uint32Array(e),i.HEAPF32=new Float32Array(e),i.HEAPF64=new Float64Array(e)}i.INITIAL_MEMORY;var W,U=[],F=[],j=[],B=[];F.push({func:function(){ie()}});var V=0,D=null,L=null;function q(e){i.onAbort&&i.onAbort(e),M(e+=""),I=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(e);throw a(t),t}function N(e,t){return String.prototype.startsWith?e.startsWith(t):0===e.indexOf(t)}i.preloadedImages={},i.preloadedAudios={};function G(e){return N(e,"data:application/octet-stream;base64,")}function z(e){return N(e,"file://")}var X,J,Y="tflite.wasm";function K(e){try{if(e==Y&&b)return new Uint8Array(b);if(y)return y(e);throw"both async and sync fetching of the wasm failed"}catch(e){q(e)}}function Q(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var n=t.func;"number"==typeof n?void 0===t.arg?W.get(n)():W.get(n)(t.arg):n(void 0===t.arg?null:t.arg)}else t(i)}}function Z(e){return O[se()>>2]=e,e}function $(e){try{return A.grow(e-x.byteLength+65535>>>16),H(A.buffer),1}catch(e){}}G(Y)||(X=Y,Y=i.locateFile?i.locateFile(X,w):w+X),J=u?function(){var e=process.hrtime();return 1e3*e[0]+e[1]/1e6}:"undefined"!=typeof dateNow?dateNow:function(){return performance.now()};var ee={};function te(){if(!te.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:m||"./this.program"};for(var t in ee)e[t]=ee[t];var n=[];for(var t in e)n.push(t+"="+e[t]);te.strings=n}return te.strings}var ne,re={mappings:{},buffers:[null,[],[]],printChar:function(e,t){var n=re.buffers[e];0===t||10===t?((1===e?C:M)(T(n,0)),n.length=0):n.push(t)},varargs:void 0,get:function(){return re.varargs+=4,O[re.varargs-4>>2]},getStr:function(e){var t=function(e,t){return e?T(S,e,void 0):""}(e);return t},get64:function(e,t){return e}},ae={a:function(){q()},n:function(e,t){var n;if(0===e)n=Date.now();else{if(1!==e&&4!==e)return Z(28),-1;n=J()}return O[t>>2]=n/1e3|0,O[t+4>>2]=n%1e3*1e3*1e3|0,0},i:function(e,t){q("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")},e:function(e,t){q("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")},l:function(e,t,n){S.copyWithin(e,t,t+n)},m:function(e){e>>>=0;var t=S.length,n=2147483648;if(e>n)return!1;for(var r,a=1;a<=4;a*=2){var i=t*(1+.2/a);if(i=Math.min(i,e+100663296),$(Math.min(n,((r=Math.max(16777216,e,i))%65536>0&&(r+=65536-r%65536),r))))return!0}return!1},o:function(e){for(var t=J();J()-t<e;);},p:function(e,t){var n=0;return te().forEach((function(r,a){var i=t+n;O[e+4*a>>2]=i,function(e,t,n){for(var r=0;r<e.length;++r)R[0|t++]=e.charCodeAt(r);R[0|t]=0}(r,i),n+=r.length+1})),0},g:function(e,t){var n=te();O[e>>2]=n.length;var r=0;return n.forEach((function(e){r+=e.length+1})),O[t>>2]=r,0},j:function(e){!function(e,t){E||(i.onExit&&i.onExit(e),I=!0),d(e,new oe(e))}(e)},h:function(e){return 0},k:function(e,t,n,r,a){},c:function(e,t,n,r){for(var a=0,i=0;i<n;i++){for(var s=O[t+8*i>>2],o=O[t+(8*i+4)>>2],u=0;u<o;u++)re.printChar(e,S[s+u]);a+=o}return O[r>>2]=a,0},d:function(){return 6},f:function(){return 28},b:function(e){switch(e){case 30:case 75:return 16384;case 85:return 131072;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:case 80:case 81:case 79:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return"object"==typeof navigator&&navigator.hardwareConcurrency||1}return Z(28),-1}},ie=(function(){var e={a:ae};function t(e,t){var n=e.exports;i.asm=n,H((A=i.asm.q).buffer),W=i.asm.D,function(e){if(V--,i.monitorRunDependencies&&i.monitorRunDependencies(V),0==V&&(null!==D&&(clearInterval(D),D=null),L)){var t=L;L=null,t()}}()}function n(e){t(e.instance)}function r(t){return function(){if(!b&&(f||p)){if("function"==typeof fetch&&!z(Y))return fetch(Y,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+Y+"'";return e.arrayBuffer()})).catch((function(){return K(Y)}));if(g)return new Promise((function(e,t){g(Y,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return K(Y)}))}().then((function(t){return WebAssembly.instantiate(t,e)})).then(t,(function(e){M("failed to asynchronously prepare wasm: "+e),q(e)}))}if(V++,i.monitorRunDependencies&&i.monitorRunDependencies(V),i.instantiateWasm)try{return i.instantiateWasm(e,t)}catch(e){return M("Module.instantiateWasm callback failed with error: "+e),!1}(b||"function"!=typeof WebAssembly.instantiateStreaming||G(Y)||z(Y)||"function"!=typeof fetch?r(n):fetch(Y,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(n,(function(e){return M("wasm streaming compile failed: "+e),M("falling back to ArrayBuffer instantiation"),r(n)}))}))).catch(a)}(),i.___wasm_call_ctors=function(){return(ie=i.___wasm_call_ctors=i.asm.r).apply(null,arguments)}),se=(i._getModelBufferMemoryOffset=function(){return(i._getModelBufferMemoryOffset=i.asm.s).apply(null,arguments)},i._getInputMemoryOffset=function(){return(i._getInputMemoryOffset=i.asm.t).apply(null,arguments)},i._getInputHeight=function(){return(i._getInputHeight=i.asm.u).apply(null,arguments)},i._getInputWidth=function(){return(i._getInputWidth=i.asm.v).apply(null,arguments)},i._getInputChannelCount=function(){return(i._getInputChannelCount=i.asm.w).apply(null,arguments)},i._getOutputMemoryOffset=function(){return(i._getOutputMemoryOffset=i.asm.x).apply(null,arguments)},i._getOutputHeight=function(){return(i._getOutputHeight=i.asm.y).apply(null,arguments)},i._getOutputWidth=function(){return(i._getOutputWidth=i.asm.z).apply(null,arguments)},i._getOutputChannelCount=function(){return(i._getOutputChannelCount=i.asm.A).apply(null,arguments)},i._loadModel=function(){return(i._loadModel=i.asm.B).apply(null,arguments)},i._runInference=function(){return(i._runInference=i.asm.C).apply(null,arguments)},i.___errno_location=function(){return(se=i.___errno_location=i.asm.E).apply(null,arguments)});function oe(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function ue(e){function n(){ne||(ne=!0,i.calledRun=!0,I||(Q(F),Q(j),t(i),i.onRuntimeInitialized&&i.onRuntimeInitialized(),function(){if(i.postRun)for("function"==typeof i.postRun&&(i.postRun=[i.postRun]);i.postRun.length;)e=i.postRun.shift(),B.unshift(e);var e;Q(B)}()))}e=e||l,V>0||(function(){if(i.preRun)for("function"==typeof i.preRun&&(i.preRun=[i.preRun]);i.preRun.length;)e=i.preRun.shift(),U.unshift(e);var e;Q(U)}(),V>0||(i.setStatus?(i.setStatus("Running..."),setTimeout((function(){setTimeout((function(){i.setStatus("")}),1),n()}),1)):n()))}if(L=function e(){ne||ue(),ne||(L=e)},i.run=ue,i.preInit)for("function"==typeof i.preInit&&(i.preInit=[i.preInit]);i.preInit.length>0;)i.preInit.pop()();return E=!0,ue(),e.ready});e.exports=a},853:()=>{},940:()=>{}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";n.r(r),n.d(r,{createVirtualBackgroundEffect:()=>p});var e=n(759);const t=URL.createObjectURL(new Blob(["\n    var timer;\n\n    onmessage = function(request) {\n        switch (request.data.id) {\n        case 1: {\n            timer = setTimeout(() => {\n                postMessage({ id: 3 });\n            }, request.data.timeMs);\n            break;\n        }\n        case 2: {\n            if (timer) {\n                clearTimeout(timer);\n            }\n            break;\n        }\n        }\n    };\n"],{type:"application/javascript"}));class a{constructor(e,t){this._options=t,this._options.virtualBackground.isVirtualBackground&&(this._virtualImage=document.createElement("img"),this._virtualImage.crossOrigin="anonymous",this._virtualImage.src=this._options.virtualBackground.virtualSource),this._model=e,this._options=t,this._segmentationPixelCount=this._options.width*this._options.height,this._onMaskFrameTimer=this._onMaskFrameTimer.bind(this),this._outputCanvasElement=document.createElement("canvas"),this._outputCanvasElement.getContext("2d"),this._inputVideoElement=document.createElement("video")}async _onMaskFrameTimer(e){3===e.data.id&&await this._renderMask()}runPostProcessing(){this._outputCanvasCtx.globalCompositeOperation="copy",this._options.virtualBackground.isVirtualBackground?this._outputCanvasCtx.filter="blur(4px)":this._outputCanvasCtx.filter="blur(8px)",this._outputCanvasCtx.drawImage(this._segmentationMaskCanvas,0,0,this._options.width,this._options.height,0,0,this._inputVideoElement.width,this._inputVideoElement.height),this._outputCanvasCtx.globalCompositeOperation="source-in",this._outputCanvasCtx.filter="none",this._outputCanvasCtx.drawImage(this._inputVideoElement,0,0),this._outputCanvasCtx.globalCompositeOperation="destination-over",this._options.virtualBackground.isVirtualBackground?this._outputCanvasCtx.drawImage(this._virtualImage,0,0,this._inputVideoElement.width,this._inputVideoElement.height):(this._outputCanvasCtx.filter="blur(25px)",this._outputCanvasCtx.drawImage(this._inputVideoElement,0,0))}runInference(){this._model._runInference();const e=this._model._getOutputMemoryOffset()/4;for(let t=0;t<this._segmentationPixelCount;t++){const n=this._model.HEAPF32[e+2*t],r=this._model.HEAPF32[e+2*t+1],a=Math.max(n,r),i=Math.exp(n-a),s=Math.exp(r-a);this._segmentationMask.data[4*t+3]=255*s/(i+s)}this._segmentationMaskCtx.putImageData(this._segmentationMask,0,0)}_renderMask(){this.resizeSource(),this.runInference(),this.runPostProcessing(),this._maskFrameTimerWorker.postMessage({id:1,timeMs:1e3/30})}resizeSource(){this._segmentationMaskCtx.drawImage(this._inputVideoElement,0,0,this._inputVideoElement.width,this._inputVideoElement.height,0,0,this._options.width,this._options.height);const e=this._segmentationMaskCtx.getImageData(0,0,this._options.width,this._options.height),t=this._model._getInputMemoryOffset()/4;for(let n=0;n<this._segmentationPixelCount;n++)this._model.HEAPF32[t+3*n]=e.data[4*n]/255,this._model.HEAPF32[t+3*n+1]=e.data[4*n+1]/255,this._model.HEAPF32[t+3*n+2]=e.data[4*n+2]/255}isEnabled(e){return e.isVideoTrack()&&"camera"===e.videoType}startEffect(e){this._maskFrameTimerWorker=new Worker(t,{name:"Blur effect worker"}),this._maskFrameTimerWorker.onmessage=this._onMaskFrameTimer;const n=e.getVideoTracks()[0],{height:r,frameRate:a,width:i}=n.getSettings?n.getSettings():n.getConstraints();return this._segmentationMask=new ImageData(this._options.width,this._options.height),this._segmentationMaskCanvas=document.createElement("canvas"),this._segmentationMaskCanvas.width=this._options.width,this._segmentationMaskCanvas.height=this._options.height,this._segmentationMaskCtx=this._segmentationMaskCanvas.getContext("2d"),this._outputCanvasElement.width=parseInt(i,10),this._outputCanvasElement.height=parseInt(r,10),this._outputCanvasCtx=this._outputCanvasElement.getContext("2d"),this._inputVideoElement.width=parseInt(i,10),this._inputVideoElement.height=parseInt(r,10),this._inputVideoElement.autoplay=!0,this._inputVideoElement.srcObject=e,this._inputVideoElement.onloadeddata=()=>{this._maskFrameTimerWorker.postMessage({id:1,timeMs:1e3/30})},this._outputCanvasElement.captureStream(parseInt(a,10))}stopEffect(){this._maskFrameTimerWorker&&this._maskFrameTimerWorker.postMessage({id:2}),this._maskFrameTimerWorker.terminate()}}var i=n(360),s=n.n(i),o=n(599),u=n.n(o);const c={model96:"libs/segm_lite_v681.tflite",model144:"libs/segm_full_v679.tflite"},f={model96:{height:96,width:160},model144:{height:144,width:256}};async function p(t){if(!MediaStreamTrack.prototype.getSettings&&!MediaStreamTrack.prototype.getConstraints)throw new Error("MHStreamBackgroundEffect not supported!");let n;n=e.feature.simd?await u()():await s()();const r=n._getModelBufferMemoryOffset(),i=await fetch(e.feature.simd?c.model144:c.model96);if(!i.ok)throw new Error("Failed to download tflite model!");const o=await i.arrayBuffer();n.HEAPU8.set(new Uint8Array(o),r),n._loadModel(o.byteLength);const p={...e.feature.simd?f.model144:f.model96,virtualBackground:t};return new a(n,p)}})(),((window.MeetHourJS=window.MeetHourJS||{}).app=window.MeetHourJS.app||{}).effects=r})();
//# sourceMappingURL=virtual-background-effect.min.map