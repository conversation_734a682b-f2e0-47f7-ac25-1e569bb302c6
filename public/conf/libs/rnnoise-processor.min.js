(()=>{"use strict";var e,t={d:(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},n={};t.r(n),t.d(n,{RNNOISE_SAMPLE_LENGTH:()=>o,createRnnoiseProcessor:()=>u});const r=(e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(t){var n;(t=void 0!==(t=t||{})?t:{}).ready=new Promise((function(e,t){n=e}));var r,o={};for(r in t)t.hasOwnProperty(r)&&(o[r]=t[r]);var i,s,a=[];i="object"==typeof window,s="function"==typeof importScripts,"object"==typeof process&&"object"==typeof process.versions&&process.versions.node;var u,c="";(i||s)&&(s?c=self.location.href:document.currentScript&&(c=document.currentScript.src),e&&(c=e),c=0!==c.indexOf("blob:")?c.substr(0,c.lastIndexOf("/")+1):"",s&&(u=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}));var f,m,l=t.print||console.log.bind(console),p=t.printErr||console.warn.bind(console);for(r in o)o.hasOwnProperty(r)&&(t[r]=o[r]);o=null,t.arguments&&(a=t.arguments),t.thisProgram&&t.thisProgram,t.quit&&t.quit,t.wasmBinary&&(f=t.wasmBinary),t.noExitRuntime&&t.noExitRuntime,"object"!=typeof WebAssembly&&p("no native wasm support detected");var y,h,_,w=new WebAssembly.Table({initial:1,maximum:1,element:"anyfunc"}),d=!1;function b(e){y=e,t.HEAP8=new Int8Array(e),t.HEAP16=new Int16Array(e),t.HEAP32=_=new Int32Array(e),t.HEAPU8=h=new Uint8Array(e),t.HEAPU16=new Uint16Array(e),t.HEAPU32=new Uint32Array(e),t.HEAPF32=new Float32Array(e),t.HEAPF64=new Float64Array(e)}var v=t.INITIAL_MEMORY||16777216;function P(e){for(;e.length>0;){var n=e.shift();if("function"!=typeof n){var r=n.func;"number"==typeof r?void 0===n.arg?t.dynCall_v(r):t.dynCall_vi(r,n.arg):r(void 0===n.arg?null:n.arg)}else n(t)}}(m=t.wasmMemory?t.wasmMemory:new WebAssembly.Memory({initial:v/65536,maximum:32768}))&&(y=m.buffer),v=y.byteLength,b(y),_[24364]=5340496;var I=[],g=[],A=[],R=[],S=0,E=null,M=null;function O(e){throw t.onAbort&&t.onAbort(e),l(e+=""),p(e),d=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(e)}t.preloadedImages={},t.preloadedAudios={};function W(e){return t=e,n="data:application/octet-stream;base64,",String.prototype.startsWith?t.startsWith(n):0===t.indexOf(n);var t,n}var x,H="rnnoise.wasm";function T(){try{if(f)return new Uint8Array(f);if(u)return u(H);throw"both async and sync fetching of the wasm failed"}catch(e){O(e)}}function F(e){try{return m.grow(e-y.byteLength+65535>>>16),b(m.buffer),1}catch(e){}}W(H)||(x=H,H=t.locateFile?t.locateFile(x,c):c+x),g.push({func:function(){C()}});var j,B={a:function(e,t,n){h.copyWithin(e,t,t+n)},b:function(e){e>>>=0;var t=h.length,n=2147483648;if(e>n)return!1;for(var r,o=1;o<=4;o*=2){var i=t*(1+.2/o);if(i=Math.min(i,e+100663296),F(Math.min(n,((r=Math.max(16777216,e,i))%65536>0&&(r+=65536-r%65536),r))))return!0}return!1},memory:m,table:w},C=(function(){var e={a:B};function n(e,n){var r=e.exports;t.asm=r,function(e){if(S--,t.monitorRunDependencies&&t.monitorRunDependencies(S),0==S&&(null!==E&&(clearInterval(E),E=null),M)){var n=M;M=null,n()}}()}function r(e){n(e.instance)}function o(t){return(f||!i&&!s||"function"!=typeof fetch?new Promise((function(e,t){e(T())})):fetch(H,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+H+"'";return e.arrayBuffer()})).catch((function(){return T()}))).then((function(t){return WebAssembly.instantiate(t,e)})).then(t,(function(e){p("failed to asynchronously prepare wasm: "+e),O(e)}))}if(S++,t.monitorRunDependencies&&t.monitorRunDependencies(S),t.instantiateWasm)try{return t.instantiateWasm(e,n)}catch(e){return p("Module.instantiateWasm callback failed with error: "+e),!1}!function(){if(f||"function"!=typeof WebAssembly.instantiateStreaming||W(H)||"function"!=typeof fetch)return o(r);fetch(H,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(r,(function(e){return p("wasm streaming compile failed: "+e),p("falling back to ArrayBuffer instantiation"),o(r)}))}))}()}(),t.___wasm_call_ctors=function(){return(C=t.___wasm_call_ctors=t.asm.c).apply(null,arguments)});function U(e){function r(){j||(j=!0,t.calledRun=!0,d||(P(g),P(A),n(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)e=t.postRun.shift(),R.unshift(e);var e;P(R)}()))}e=e||a,S>0||(function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)e=t.preRun.shift(),I.unshift(e);var e;P(I)}(),S>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),r()}),1)):r()))}if(t._rnnoise_init=function(){return(t._rnnoise_init=t.asm.d).apply(null,arguments)},t._rnnoise_create=function(){return(t._rnnoise_create=t.asm.e).apply(null,arguments)},t._malloc=function(){return(t._malloc=t.asm.f).apply(null,arguments)},t._rnnoise_destroy=function(){return(t._rnnoise_destroy=t.asm.g).apply(null,arguments)},t._free=function(){return(t._free=t.asm.h).apply(null,arguments)},t._rnnoise_process_frame=function(){return(t._rnnoise_process_frame=t.asm.i).apply(null,arguments)},M=function e(){j||U(),j||(M=e)},t.run=U,t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return U(),t.ready});const o=480,i=4*o;class s{constructor(e){var t,n,r;t=this,r=!1,(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(n="_destroyed"))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r;try{if(this._wasmInterface=e,this._wasmPcmInput=this._wasmInterface._malloc(i),!this._wasmPcmInput)throw Error("Failed to create wasm input memory buffer!");if(this._wasmPcmOutput=this._wasmInterface._malloc(i),!this._wasmPcmOutput)throw e._free(this._wasmPcmInput),Error("Failed to create wasm output memory buffer!");this._wasmPcmInputF32Index=this._wasmPcmInput/4,this._context=this._wasmInterface._rnnoise_create()}catch(e){throw this._releaseWasmResources(),e}}_copyPCMSampleToWasmBuffer(e){this._wasmInterface.HEAPF32.set(e,this._wasmPcmInputF32Index)}_convertTo16BitPCM(e){for(const[t,n]of e.entries())e[t]=32767*n}_releaseWasmResources(){this._wasmPcmInput&&(this._wasmInterface._free(this._wasmPcmInput),this._wasmPcmInput=null),this._wasmPcmOutput&&(this._wasmInterface._free(this._wasmPcmOutput),this._wasmPcmOutput=null),this._context&&(this._wasmInterface._rnnoise_destroy(this._context),this._context=null)}getSampleLength(){return o}getRequiredPCMFrequency(){return 44100}destroy(){this._destroyed||(this._releaseWasmResources(),this._destroyed=!0)}calculateAudioFrameVAD(e){if(this._destroyed)throw new Error("RnnoiseProcessor instance is destroyed, please create another one!");const t=e.length;if(t!==o)throw new Error(`Rnnoise can only process PCM frames of 480 samples! Input sample was:${t}`);return this._convertTo16BitPCM(e),this._copyPCMSampleToWasmBuffer(e),this._wasmInterface._rnnoise_process_frame(this._context,this._wasmPcmOutput,this._wasmPcmInput)}}let a;function u(){return a||(a=r()),a.then((e=>new s(e)))}(((window.MeetHourJS=window.MeetHourJS||{}).app=window.MeetHourJS.app||{}).effects=window.MeetHourJS.app.effects||{}).rnnoise=n})();
//# sourceMappingURL=rnnoise-processor.min.map