local store = module:open_store("muc_donorbox", "map");
if not store then
    module:log("error","Failed to open storage.");
    return nil;
end

module:hook("muc-room-destroyed", function (event)
    local room = event.room;
    local donorboxurl = room:get_donorboxurl(room);

    if donorboxurl then
        module:log("debug", "Room %s with pass %s about to be destroyed", room, donorboxurl);
        if not store then
                module:log("debug","failed to open store on destroy");
            return nil;
        end
        
        local now = os.time();
        store:set(room.jid, "last_used_donorbox", now);
        store:set(room.jid, "donorboxurl" , donorboxurl);
        module:log("debug", "Stored %s, %s for room %s", donorboxurl, now, room);
    else    
	    -- moderator removed old donorboxurl from restored room, delete stored entry
        store:set(room.jid, "last_used_donorbox", nil);
        store:set(room.jid, "donorboxurl" , nil);
        module:log("debug", "Deleted stored entries for room %s", room);
    end

    return nil; -- can be removed
end, 0);

module:hook("muc-room-created", function (event)
    local room = event.room;
    module:log("debug","hooked room create for %s", room);

    local old_donorboxurl = store:get(room.jid, "donorboxurl");
    if not old_donorboxurl then
        module:log("debug", "No donorbox url to restore for room %s", room);
	return nil;
    end

    module:log("debug", "Loaded old donorbox url '%s' for room %s", old_donorboxurl, room);
    local last_used_donorbox = store:get(room.jid, "last_used_donorbox");
    if not last_used_donorbox then
        module:log("debug", "No stored timestamp found for room %s. Removing stored entry.", room, err);
        store:set(room.jid, "last_used_donorbox", nil);
        store:set(room.jid, "donorboxurl" , nil);
	return nil;
    end

    if is_room_stale(last_used_donorbox) then
        -- delete entry
        store:set(room.jid, "last_used_donorbox", nil);
        store:set(room.jid, "donorboxurl" , nil);
	module:log("debug", "deleted donorboxurl for stale room %s", room); 
	return nil;
    end

    -- restore old pass for the mucroom
    local success = room:set_donorboxurl(old_donorboxurl);
    if not success then 
        module:log("warn", "Failed to set old donorboxurl %s for restored room %s.", old_donorboxurl, room);
    end
    
    module:log("debug", "Set donorboxurl '%s' for restored room %s.", old_donorboxurl, room);
    return nil;
end, 0);

function is_room_stale(last_used_donorbox)
    local days = module:get_option_number("days_to_persist_muc_passwds", 30);
    module:log("debug", "Function is_stale() called with '%s', days is set to %s", last_used_donorbox, days);
    local daysfrom = os.difftime(os.time(), last_used_donorbox) / (24 * 60 * 60); 
    local roomage = math.floor(daysfrom) ;
    module:log("debug", "roomage is %s days", roomage);
    if roomage then
        return roomage > days; 
    end
    return false;
end
