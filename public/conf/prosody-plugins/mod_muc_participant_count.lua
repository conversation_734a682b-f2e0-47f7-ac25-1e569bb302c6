local jid_split = require 'util.jid'.split;
local iterators = require "util.iterators";
local store = module:open_store("persistent_muc_participant", "map");
if not store then
    module:log("error","Failed to open storage.");
    return nil;
end



-- local main_muc_service;
module:hook("muc-room-destroyed", function (event)
    local room = event.room;
    local now = os.time();
    store:get(room.jid, "last_participant");
    local part = store:get(room.jid, "participants");
    module:log("info", "muc-room-destroyed is %s this", part);
    store:set(room.jid, "participants", 0);

end, 0);

module:hook("muc-occupant-pre-leave", function (event)
    local room = event.room;
    local now = os.time();
    store:get(room.jid, "last_participant");
    local parrt  = store:get(room.jid, "participants");
    module:log("info","muc-occupant-pre-leave had participants %s ",parrt - 1)
    if parrt == 1 then 
        module:log("info","muc-occupant-pre-leave had only one  participants %s ",parrt)
        store:set(room.jid, "participants", 0);
        return nil;
    end

    store:set(room.jid, "participants", parrt - 1);

end, 0);

module:hook("muc-room-created", function (event)
    local room = event.room;
    local now = os.time();
    store:get(room.jid, "last_participant");
    local parrt  = store:get(room.jid, "participants");
    module:log("info","muc-room-created had participants %s ",parrt)
    store:set(room.jid, "participants", 0);

end, 0);

module:hook("muc-occupant-joined", function (event)    
    local room = event.room
	local participant_count = 0;
    if room then
		local occupants = room._occupants;
		if occupants then
			participant_count = iterators.count(room:each_occupant());
		end

        if participant_count > 1 then
            participant_count = participant_count - 1;
        end
        local now = os.time();
        store:set(room.jid, "last_participant", now);
        store:set(room.jid, "participants", participant_count);
		log("debug",
            "there are %s occupants in room", tostring(participant_count));
	else
		log("debug", "no such room exists");
		return { status_code = 404; };
	end

    module:log("info", "participant_count is %s this", participant_count);

end, 0);

function is_room_stale(last_used)
    local days = module:get_option_number("days_to_persist_muc_passwds", 30);
    module:log("debug", "Function is_stale() called with '%s', days is set to %s", last_used, days);
    local daysfrom = os.difftime(os.time(), last_used) / (24 * 60 * 60); 
    local roomage = math.floor(daysfrom) ;
    module:log("debug", "roomage is %s days", roomage);
    if roomage then
        return roomage > days; 
    end
    return false;
end