<html itemscope itemtype="http://schema.org/Product" prefix="og: http://ogp.me/ns#" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--#include virtual="/base.html" -->
    <!--#include virtual="/title.html" -->
    <script>
        (function() {
            function parseURLParams(
        urls,
        dontParse = false,
        source = 'hash') {
    const url = new URL(urls)
    const paramStr = source === 'search' ? url.search : url.hash;
    const params = {};
    const paramParts = (paramStr && paramStr.substr(1).split('&')) || [];

    // Detect and ignore hash params for hash routers.
    if (source === 'hash' && paramParts.length === 1) {
        const firstParam = paramParts[0];

        if (firstParam.startsWith('/') && firstParam.split('&').length === 1) {
            return params;
        }
    }

    paramParts.forEach(part => {
        const param = part.split('=');
        const key = param[0];

        if (!key) {
            return;
        }

        let value;

        try {
            value = param[1];

            if (!dontParse) {
                const decoded = decodeURIComponent(value).replace(/\\&/, '&');

                value = decoded === 'undefined' ? undefined : JSON.parse(decoded);
            }
        } catch (e) {
            reportError(
                e, `Failed to parse URL parameter value: ${String(value)}`);

            return;
        }
        params[key] = value;
    });

    return params;
}
            const appUrl = parseURLParams(window.location.href, true, 'search')?.app_url
            const codee = parseURLParams(window.location.href, true, 'search')?.code

            if(!(codee == null) && !(appUrl == null)){
                // window.location = appUrl + '&code=' + codee
                return;
            }
            var windowName = window.name;
            window.opener && window.opener.postMessage({
                type: 'dropbox-login',
                windowName,
                url: window.location.href
            }, window.location.origin);
        })();
    </script>
</head>
<body>
</body>
</html>
